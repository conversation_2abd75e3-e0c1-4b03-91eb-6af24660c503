<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 资源测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .tile-preview {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        .tile {
            width: 60px;
            height: 60px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            text-align: center;
        }
        .background-preview {
            width: 200px;
            height: 150px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            background-size: cover;
            background-position: center;
            margin-top: 10px;
        }
        .sound-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        button {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 4px;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
        }
        .loading {
            background: rgba(255, 193, 7, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 花砖物语 - 资源测试</h1>
        
        <div class="section">
            <h2>📊 资源加载状态</h2>
            <div id="loadingStatus" class="status loading">正在加载资源...</div>
        </div>

        <div class="section">
            <h2>🎨 瓷砖图片预览</h2>
            <div class="tile-preview" id="tilePreview">
                <!-- 瓷砖预览将在这里显示 -->
            </div>
        </div>

        <div class="section">
            <h2>🖼️ 背景图片预览</h2>
            <div class="background-preview" id="backgroundPreview"></div>
        </div>

        <div class="section">
            <h2>🎵 音效测试</h2>
            <div class="sound-controls">
                <button onclick="testSound('bgm1')">BGM1</button>
                <button onclick="testSound('bgm2')">BGM2</button>
                <button onclick="testSound('tileSelect')">选择音效</button>
                <button onclick="testSound('tilePlace')">放置音效</button>
                <button onclick="testSound('scoreUpdate')">得分音效</button>
                <button onclick="testSound('roundEnd')">回合结束</button>
                <button onclick="testSound('gameWin')">胜利音效</button>
                <button onclick="testSound('buttonClick')">按钮点击</button>
                <button onclick="stopAllSounds()">停止所有音效</button>
            </div>
        </div>

        <div class="section">
            <h2>🎮 启动游戏</h2>
            <button onclick="startGame()" style="font-size: 18px; padding: 12px 24px;">启动花砖物语</button>
        </div>
    </div>

    <script>
        // 等待页面加载完成后开始测试
        window.addEventListener('load', async () => {
            await testResourceLoading();
        });

        async function testResourceLoading() {
            const statusDiv = document.getElementById('loadingStatus');
            const tilePreview = document.getElementById('tilePreview');
            const backgroundPreview = document.getElementById('backgroundPreview');

            try {
                statusDiv.textContent = '正在测试图片资源...';
                
                // 测试瓷砖图片
                const tileImages = {
                    blue: 'images/white_sheep.gif',
                    yellow: 'images/yellow_sheep.gif',
                    red: 'images/pink_sheep.gif',
                    black: 'images/black_sheep.gif',
                    teal: 'images/gray_sheep.gif'
                };

                const tileNames = {
                    blue: '蓝色(白羊)',
                    yellow: '黄色(黄羊)',
                    red: '红色(粉羊)', 
                    black: '黑色(黑羊)',
                    teal: '青色(灰羊)'
                };

                for (const [color, src] of Object.entries(tileImages)) {
                    const tileDiv = document.createElement('div');
                    tileDiv.className = 'tile';

                    try {
                        // 兼容微信小游戏和浏览器环境
                        let img;
                        if (typeof wx !== 'undefined' && wx.createImage) {
                            img = wx.createImage();
                        } else {
                            img = new Image();
                        }

                        await new Promise((resolve, reject) => {
                            img.onload = resolve;
                            img.onerror = reject;
                            img.src = src;
                        });

                        tileDiv.style.backgroundImage = `url(${src})`;
                        tileDiv.style.backgroundSize = 'cover';
                        tileDiv.style.backgroundPosition = 'center';
                        tileDiv.innerHTML = `<div style="background: rgba(0,0,0,0.7); padding: 2px; border-radius: 2px;">${tileNames[color]}</div>`;
                    } catch (error) {
                        tileDiv.style.backgroundColor = '#ff4444';
                        tileDiv.textContent = `${tileNames[color]} 加载失败`;
                    }

                    tilePreview.appendChild(tileDiv);
                }

                // 测试背景图片
                try {
                    // 兼容微信小游戏和浏览器环境
                    let bgImg;
                    if (typeof wx !== 'undefined' && wx.createImage) {
                        bgImg = wx.createImage();
                    } else {
                        bgImg = new Image();
                    }

                    await new Promise((resolve, reject) => {
                        bgImg.onload = resolve;
                        bgImg.onerror = reject;
                        bgImg.src = 'images/background.jpg';
                    });
                    backgroundPreview.style.backgroundImage = 'url(images/background.jpg)';
                } catch (error) {
                    backgroundPreview.style.backgroundColor = '#ff4444';
                    backgroundPreview.textContent = '背景图片加载失败';
                }

                statusDiv.textContent = '✅ 资源加载完成！可以测试音效和启动游戏。';
                statusDiv.className = 'status success';

            } catch (error) {
                statusDiv.textContent = `❌ 资源加载失败: ${error.message}`;
                statusDiv.className = 'status error';
            }
        }

        function testSound(soundName) {
            // 检查是否有游戏应用实例
            if (window.gameApp && window.gameApp.playSound) {
                window.gameApp.playSound(soundName);
                console.log(`播放音效: ${soundName}`);
            } else {
                // 直接测试音频文件
                const soundConfig = {
                    bgm1: 'sounds/game_bgm1.m4a',
                    bgm2: 'sounds/game_bgm2.mp3',
                    tileSelect: 'sounds/tile_select.wav',
                    tilePlace: 'sounds/tile_place.wav',
                    scoreUpdate: 'sounds/score_update.wav',
                    roundEnd: 'sounds/round_end.wav',
                    gameWin: 'sounds/game_win.wav',
                    buttonClick: 'sounds/button_click.m4a'
                };

                if (soundConfig[soundName]) {
                    try {
                        let audio;
                        if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
                            // 微信小游戏环境
                            audio = wx.createInnerAudioContext();
                            audio.src = soundConfig[soundName];
                            audio.volume = 0.7;
                            audio.play();
                        } else {
                            // 浏览器环境
                            audio = new Audio(soundConfig[soundName]);
                            audio.volume = 0.7;
                            audio.play().catch(error => {
                                console.error(`播放音效失败 ${soundName}:`, error);
                                alert(`播放音效失败: ${soundName}`);
                            });
                        }
                    } catch (error) {
                        console.error(`播放音效失败 ${soundName}:`, error);
                        alert(`播放音效失败: ${soundName}`);
                    }
                }
            }
        }

        function stopAllSounds() {
            if (window.gameApp) {
                // 停止所有音效
                Object.keys(window.gameApp.sounds || {}).forEach(soundName => {
                    window.gameApp.stopSound(soundName);
                });
            }
        }

        function startGame() {
            // 跳转到游戏主页面
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
