<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 小羊动画测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(255, 165, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>小羊动画测试</h3>
        
        <div class="info">
            <strong>你说得对！</strong><br>
            GIF里的小羊不是旋转的，而是：<br>
            • 眨眼动作<br>
            • 轻微摆动<br>
            • 上下跳跃<br>
            • 呼吸效果<br>
            • 摆尾动作<br>
            <br>
            现在模拟这些真实的小羊动作！
        </div>
        
        <button onclick="startSheepAnimation()">🐑 开始小羊动画</button>
        <button onclick="stopAnimation()">⏹️ 停止动画</button>
        <button onclick="testBlinking()">👁️ 测试眨眼效果</button>
        <button onclick="testBreathing()">💨 测试呼吸效果</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        let animationRunning = false;
        let animationId = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function startSheepAnimation() {
            updateStatus('=== 开始小羊动画测试 ===');
            updateStatus('模拟真实的小羊动作：眨眼、摆动、跳跃、呼吸');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            animationRunning = true;
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
            let frameCount = 0;
            
            const animate = () => {
                if (!animationRunning) return;
                
                // 每帧清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.fillStyle = 'white';
                ctx.font = '28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('小羊动画效果测试', canvas.width / 2, 50);
                
                // 绘制说明
                ctx.font = '16px Arial';
                ctx.fillText('观察：眨眼、轻微摆动、上下跳跃、呼吸效果', canvas.width / 2, 80);
                
                // 绘制动画瓷砖
                colors.forEach((color, index) => {
                    const x = 80 + index * 130;
                    const y = 200;
                    const size = 100;
                    
                    // 使用游戏的renderTile方法
                    gameApp.renderTile(color, x, y, size);
                    
                    // 添加颜色标签
                    ctx.fillStyle = 'white';
                    ctx.font = '18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(color, x + size/2, y - 15);
                });
                
                // 显示动画说明
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🐑 → 😴 眨眼切换', canvas.width / 2, 350);
                ctx.fillText('轻微摆动 + 上下跳跃 + 呼吸缩放', canvas.width / 2, 370);
                
                // 显示帧计数
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`帧数: ${frameCount}`, 20, canvas.height - 20);
                
                frameCount++;
                animationId = requestAnimationFrame(animate);
            };
            
            updateStatus('✅ 小羊动画已启动');
            updateStatus('观察小羊的眨眼、摆动、跳跃效果');
            animate();
        }
        
        function stopAnimation() {
            updateStatus('=== 停止动画 ===');
            animationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            updateStatus('✅ 动画已停止');
        }
        
        function testBlinking() {
            updateStatus('=== 测试眨眼效果 ===');
            updateStatus('小羊会在 🐑 和 😴 之间切换，模拟眨眼');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空Canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('眨眼效果测试', canvas.width / 2, 100);
            
            // 绘制一个大瓷砖来观察眨眼
            gameApp.renderTile('blue', canvas.width / 2 - 75, 200, 150);
            
            updateStatus('✅ 观察大瓷砖中的小羊眨眼效果');
        }
        
        function testBreathing() {
            updateStatus('=== 测试呼吸效果 ===');
            updateStatus('瓷砖会有轻微的大小变化，模拟呼吸');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            // 启动一个简单的呼吸动画
            let breathFrames = 0;
            const breathAnimate = () => {
                if (breathFrames > 180) return; // 3秒后停止
                
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('呼吸效果测试', canvas.width / 2, 100);
                
                // 绘制呼吸瓷砖
                gameApp.renderTile('red', canvas.width / 2 - 75, 200, 150);
                
                breathFrames++;
                requestAnimationFrame(breathAnimate);
            };
            
            breathAnimate();
            updateStatus('✅ 观察瓷砖的呼吸缩放效果');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成');
                updateStatus('');
                updateStatus('🐑 关于小羊动画：');
                updateStatus('你说得对，GIF里的小羊不是旋转的！');
                updateStatus('真实的小羊动作包括：');
                updateStatus('• 眨眼动作（🐑 ↔ 😴）');
                updateStatus('• 轻微左右摆动');
                updateStatus('• 上下轻微跳跃');
                updateStatus('• 呼吸时的大小变化');
                updateStatus('• 偶尔的摆尾动作');
                updateStatus('');
                updateStatus('✨ 新的动画效果：');
                updateStatus('现在模拟这些真实的小羊行为！');
                
                // 自动开始动画
                setTimeout(() => {
                    startSheepAnimation();
                }, 2000);
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
