<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 动画和加载问题修复</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .controls button.danger {
            background: #f44336;
        }
        
        .controls button.danger:hover {
            background: #da190b;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(200, 100, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>动画和加载问题修复</h3>
        
        <div class="info">
            <strong>问题修复：</strong><br>
            1. 瓷砖动画不播放 → 改用基于时间戳的平滑动画<br>
            2. 点击后卡住加载转圈 → 添加强制重置机制
        </div>
        
        <button onclick="forceResetState()" class="danger">🔧 强制重置状态</button>
        <button onclick="initAnimations()">🎬 初始化动画系统</button>
        <button onclick="testAnimations()">🧪 测试动画效果</button>
        <button onclick="checkProcessingFlag()">⚠️ 检查处理标志位</button>
        <button onclick="clearProcessingFlag()" class="danger">🚫 清除处理标志位</button>
        
        <div class="status" id="status">等待操作...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function forceResetState() {
            updateStatus('=== 强制重置游戏状态 ===');
            
            if (gameApp && gameApp.forceResetState) {
                gameApp.forceResetState();
                updateStatus('✅ 游戏状态已强制重置');
            } else {
                updateStatus('❌ 游戏未初始化或方法不存在');
            }
        }
        
        function initAnimations() {
            updateStatus('=== 初始化动画系统 ===');
            
            if (gameApp) {
                if (gameApp.initTileAnimations) {
                    gameApp.initTileAnimations();
                    updateStatus('✅ 动画系统已初始化');
                } else {
                    updateStatus('❌ 动画初始化方法不存在');
                }
                
                // 检查动画状态
                if (gameApp.tileAnimations) {
                    updateStatus('动画配置已加载');
                    Object.entries(gameApp.tileAnimations).forEach(([color, config]) => {
                        updateStatus(`  ${color}: ${config.frames}帧, ${config.speed}ms`);
                    });
                }
                
                if (gameApp.animationTimers) {
                    const timerCount = Object.keys(gameApp.animationTimers).length;
                    updateStatus(`动画计时器数量: ${timerCount}`);
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function testAnimations() {
            updateStatus('=== 测试动画效果 ===');
            
            if (gameApp) {
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
                let animationCount = 0;
                
                // 持续绘制动画
                const drawFrame = () => {
                    // 清空Canvas
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    
                    colors.forEach((color, index) => {
                        const x = 100 + index * 120;
                        const y = 200;
                        const size = 80;
                        
                        // 使用游戏的动画渲染方法
                        if (gameApp.renderAnimatedTile) {
                            const success = gameApp.renderAnimatedTile(color, x, y, size);
                            if (!success) {
                                // 如果动画渲染失败，使用普通渲染
                                gameApp.renderTile(color, x, y, size);
                            }
                        } else {
                            gameApp.renderTile(color, x, y, size);
                        }
                        
                        // 添加标签
                        ctx.fillStyle = 'white';
                        ctx.font = '16px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(color, x + size/2, y - 10);
                    });
                    
                    animationCount++;
                    if (animationCount < 300) { // 运行5秒
                        requestAnimationFrame(drawFrame);
                    } else {
                        updateStatus('动画测试完成');
                    }
                };
                
                updateStatus('开始动画测试，观察瓷砖是否有亮度和缩放变化');
                drawFrame();
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function checkProcessingFlag() {
            updateStatus('=== 检查处理标志位 ===');
            
            if (gameApp) {
                updateStatus(`processingSelection: ${gameApp.processingSelection ? '已设置' : '未设置'}`);
                updateStatus(`lastProcessingTime: ${gameApp.lastProcessingTime || '无'}`);
                
                if (gameApp.processingSelection) {
                    updateStatus('⚠️ 检测到处理标志位已设置，可能导致点击无响应');
                    if (gameApp.lastProcessingTime) {
                        const elapsed = Date.now() - gameApp.lastProcessingTime;
                        updateStatus(`已持续时间: ${Math.round(elapsed/1000)}秒`);
                    }
                } else {
                    updateStatus('✅ 处理标志位正常');
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function clearProcessingFlag() {
            updateStatus('=== 清除处理标志位 ===');
            
            if (gameApp) {
                const wasSet = gameApp.processingSelection;
                gameApp.processingSelection = false;
                gameApp.lastProcessingTime = null;
                
                updateStatus(`标志位状态: ${wasSet ? '已清除' : '本来就是清除状态'}`);
                updateStatus('✅ 处理标志位已强制清除');
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成');
                updateStatus('可以使用以下功能：');
                updateStatus('• 强制重置状态 - 修复卡住问题');
                updateStatus('• 初始化动画系统 - 启用瓷砖动画');
                updateStatus('• 测试动画效果 - 查看动画是否工作');
                updateStatus('• 检查/清除处理标志位 - 修复点击无响应');
                
                // 自动初始化动画系统
                setTimeout(() => {
                    initAnimations();
                }, 1000);
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
