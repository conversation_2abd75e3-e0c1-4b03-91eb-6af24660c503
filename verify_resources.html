<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源验证 - 花砖物语</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .loading { background: rgba(255, 193, 7, 0.3); }
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .resource-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        .resource-item img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        .resource-item.error {
            background: rgba(244, 67, 54, 0.3);
        }
        .resource-item.success {
            background: rgba(76, 175, 80, 0.3);
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.5);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 花砖物语 - 资源验证</h1>
        
        <div id="status" class="status loading">正在验证资源文件...</div>
        
        <h2>🎨 瓷砖图片</h2>
        <div id="tileGrid" class="resource-grid"></div>
        
        <h2>🖼️ 背景图片</h2>
        <div id="backgroundGrid" class="resource-grid"></div>

        <h2>🏁 棋盘背景预览</h2>
        <div style="text-align: center; margin: 20px 0;">
            <div id="boardPreview" style="width: 300px; height: 150px; border: 2px solid rgba(255,255,255,0.5); border-radius: 8px; background-size: cover; background-position: center; margin: 0 auto; position: relative;">
                <div style="position: absolute; top: 10px; left: 10px; right: 10px; bottom: 10px; background: rgba(33, 150, 243, 0.2); border: 2px solid #2196f3; border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #2196f3; font-weight: bold;">
                    玩家面板预览
                </div>
            </div>
            <p style="margin-top: 10px;">准备区现在使用棋盘图片作为背景</p>
        </div>
        
        <h2>🎵 音效文件</h2>
        <div id="soundGrid" class="resource-grid"></div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.location.href='index.html'">启动游戏</button>
            <button onclick="location.reload()">重新验证</button>
        </div>
    </div>

    <script>
        const resources = {
            tiles: {
                'white_sheep.gif': '蓝色(白羊)',
                'yellow_sheep.gif': '黄色(黄羊)',
                'pink_sheep.gif': '红色(粉羊)',
                'black_sheep.gif': '黑色(黑羊)',
                'gray_sheep.gif': '青色(灰羊)'
            },
            backgrounds: {
                'background.jpg': '游戏背景',
                'board.png': '游戏棋盘'
            },
            sounds: {
                'game_bgm1.m4a': '背景音乐1',
                'game_bgm2.mp3': '背景音乐2',
                'tile_select.wav': '选择音效',
                'tile_place.wav': '放置音效',
                'score_update.wav': '得分音效',
                'round_end.wav': '回合结束',
                'game_win.wav': '胜利音效',
                'button_click.m4a': '按钮点击'
            }
        };

        let successCount = 0;
        let totalCount = 0;

        async function verifyResources() {
            const status = document.getElementById('status');
            const tileGrid = document.getElementById('tileGrid');
            const backgroundGrid = document.getElementById('backgroundGrid');
            const soundGrid = document.getElementById('soundGrid');

            // 计算总数
            totalCount = Object.keys(resources.tiles).length + 
                        Object.keys(resources.backgrounds).length + 
                        Object.keys(resources.sounds).length;

            // 验证瓷砖图片
            for (const [filename, name] of Object.entries(resources.tiles)) {
                await verifyImage(`images/${filename}`, name, tileGrid);
            }

            // 验证背景图片
            for (const [filename, name] of Object.entries(resources.backgrounds)) {
                await verifyImage(`images/${filename}`, name, backgroundGrid);
            }

            // 验证音效文件
            for (const [filename, name] of Object.entries(resources.sounds)) {
                await verifySound(`sounds/${filename}`, name, soundGrid);
            }

            // 更新状态
            if (successCount === totalCount) {
                status.textContent = `✅ 所有资源验证成功！(${successCount}/${totalCount})`;
                status.className = 'status success';
            } else {
                status.textContent = `⚠️ 部分资源验证失败 (${successCount}/${totalCount})`;
                status.className = 'status error';
            }
        }

        async function verifyImage(src, name, container) {
            const item = document.createElement('div');
            item.className = 'resource-item';
            
            try {
                const img = new Image();
                await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = reject;
                    img.src = src;
                });
                
                item.className = 'resource-item success';
                item.innerHTML = `
                    <img src="${src}" alt="${name}">
                    <div>✅ ${name}</div>
                `;
                successCount++;

                // 如果是棋盘图片，设置预览
                if (src.includes('board.png')) {
                    const boardPreview = document.getElementById('boardPreview');
                    if (boardPreview) {
                        boardPreview.style.backgroundImage = `url(${src})`;
                    }
                }
            } catch (error) {
                item.className = 'resource-item error';
                item.innerHTML = `
                    <div style="width: 60px; height: 60px; background: #ff4444; margin: 0 auto 5px; border-radius: 4px; display: flex; align-items: center; justify-content: center;">❌</div>
                    <div>❌ ${name}</div>
                `;
            }
            
            container.appendChild(item);
        }

        async function verifySound(src, name, container) {
            const item = document.createElement('div');
            item.className = 'resource-item';
            
            try {
                let audio;
                if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
                    audio = wx.createInnerAudioContext();
                    audio.src = src;
                } else {
                    audio = new Audio(src);
                }
                
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('加载超时'));
                    }, 3000);
                    
                    if (audio.addEventListener) {
                        audio.addEventListener('canplaythrough', () => {
                            clearTimeout(timeout);
                            resolve();
                        });
                        audio.addEventListener('error', reject);
                    } else if (audio.onCanplay) {
                        audio.onCanplay = () => {
                            clearTimeout(timeout);
                            resolve();
                        };
                        audio.onError = reject;
                    } else {
                        // 简单检查文件是否存在
                        fetch(src, { method: 'HEAD' })
                            .then(response => {
                                clearTimeout(timeout);
                                if (response.ok) resolve();
                                else reject();
                            })
                            .catch(reject);
                    }
                });
                
                item.className = 'resource-item success';
                item.innerHTML = `
                    <div style="width: 60px; height: 60px; background: #4CAF50; margin: 0 auto 5px; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 24px;">🎵</div>
                    <div>✅ ${name}</div>
                    <button onclick="testSound('${src}')">测试</button>
                `;
                successCount++;
            } catch (error) {
                item.className = 'resource-item error';
                item.innerHTML = `
                    <div style="width: 60px; height: 60px; background: #ff4444; margin: 0 auto 5px; border-radius: 4px; display: flex; align-items: center; justify-content: center;">❌</div>
                    <div>❌ ${name}</div>
                `;
            }
            
            container.appendChild(item);
        }

        function testSound(src) {
            try {
                let audio;
                if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
                    audio = wx.createInnerAudioContext();
                    audio.src = src;
                    audio.play();
                } else {
                    audio = new Audio(src);
                    audio.play();
                }
            } catch (error) {
                alert('播放失败: ' + error.message);
            }
        }

        // 页面加载完成后开始验证
        window.addEventListener('load', verifyResources);
    </script>
</body>
</html>
