<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单面板玩家布局测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 20px;
        }
        
        .feature-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .feature-info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .feature-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .feature-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
        
        .warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 单面板玩家布局测试</h1>
        
        <div class="feature-info">
            <h3>新功能特点</h3>
            <ul>
                <li>下方只显示一个玩家面板</li>
                <li>面板左侧显示所有玩家的头像</li>
                <li>点击头像可以切换查看对应玩家的面板</li>
                <li>当前回合玩家的头像有特殊边框</li>
                <li>选中的玩家头像有蓝色背景</li>
                <li>每个头像显示玩家编号和分数</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testSinglePlayerLayout()">测试单人游戏</button>
            <button onclick="testTwoPlayerLayout()">测试2人游戏</button>
            <button onclick="testThreePlayerLayout()">测试3人游戏</button>
            <button onclick="testFourPlayerLayout()">测试4人游戏</button>
            <button onclick="simulateAvatarClick()">模拟头像点击</button>
            <button onclick="showLayoutInfo()">显示布局信息</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`, 'success');
                }
            };
        }
        
        let gameApp;
        let azulGame;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('测试状态:', message);
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功', 'success');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`, 'error');
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function createTestGame(playerCount) {
            const app = initializeGameApp();
            if (app) {
                try {
                    const config = {
                        playerCount: playerCount,
                        aiCount: playerCount - 1, // 只有一个人类玩家
                        aiDifficulty: 'medium'
                    };
                    
                    azulGame = new AzulGame(config, app.sharedCanvas);
                    app.gameScreen = azulGame;
                    app.currentScreen = 'game';
                    
                    // 模拟一些游戏状态
                    const gameState = azulGame.gameState;
                    gameState.players.forEach((player, index) => {
                        player.score = Math.floor(Math.random() * 50) + index * 10;
                        // 添加一些磁砖到准备区
                        if (index === 0) {
                            player.patternLines[0] = ['blue'];
                            player.patternLines[1] = ['red', 'red'];
                        }
                    });
                    
                    azulGame.render();
                    updateStatus(`${playerCount}人游戏创建成功`, 'success');
                    
                } catch (error) {
                    updateStatus(`创建${playerCount}人游戏失败: ${error.message}`, 'error');
                    console.error('游戏创建错误:', error);
                }
            }
        }
        
        function testSinglePlayerLayout() {
            createTestGame(1);
        }
        
        function testTwoPlayerLayout() {
            createTestGame(2);
        }
        
        function testThreePlayerLayout() {
            createTestGame(3);
        }
        
        function testFourPlayerLayout() {
            createTestGame(4);
        }
        
        function simulateAvatarClick() {
            if (azulGame && azulGame.playerAvatarAreas && azulGame.playerAvatarAreas.length > 1) {
                try {
                    // 点击第二个玩家的头像
                    const secondPlayerAvatar = azulGame.playerAvatarAreas[1];
                    const centerX = secondPlayerAvatar.x + secondPlayerAvatar.width / 2;
                    const centerY = secondPlayerAvatar.y + secondPlayerAvatar.height / 2;
                    
                    updateStatus(`模拟点击玩家${secondPlayerAvatar.playerIndex + 1}的头像`, 'info');
                    azulGame.handleClick(centerX, centerY);
                    
                    updateStatus(`当前查看玩家: ${azulGame.currentViewingPlayer + 1}`, 'success');
                    
                } catch (error) {
                    updateStatus(`模拟头像点击失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('请先创建多人游戏', 'warning');
            }
        }
        
        function showLayoutInfo() {
            if (azulGame) {
                const playerCount = azulGame.gameState.players.length;
                const currentViewing = azulGame.currentViewingPlayer + 1;
                const currentPlayer = azulGame.gameState.currentPlayer + 1;
                const avatarCount = azulGame.playerAvatarAreas ? azulGame.playerAvatarAreas.length : 0;
                
                let info = `布局信息:\n`;
                info += `- 玩家总数: ${playerCount}\n`;
                info += `- 当前查看: 玩家${currentViewing}\n`;
                info += `- 当前回合: 玩家${currentPlayer}\n`;
                info += `- 头像数量: ${avatarCount}\n`;
                
                if (azulGame.playerAvatarAreas) {
                    info += `- 头像位置:\n`;
                    azulGame.playerAvatarAreas.forEach((avatar, index) => {
                        info += `  玩家${avatar.playerIndex + 1}: (${avatar.x}, ${avatar.y})\n`;
                    });
                }
                
                updateStatus(info, 'info');
            } else {
                updateStatus('请先创建游戏', 'warning');
            }
        }
        
        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateStatus('页面加载完成，准备测试单面板玩家布局...');
            }, 500);
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>
