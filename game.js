// 花砖物语 - 完整版小游戏
console.log('🎮 花砖物语小游戏启动...')

// === 排位赛UI模块开始 ===

// 花砖物语排位赛UI管理器
class RankedUI {
  constructor(sharedCanvas, multiplayerManager) {
    this.sharedCanvas = sharedCanvas;
    this.multiplayerManager = multiplayerManager;
    this.currentScreen = 'main'; // main, queue, match
    this.userRanking = null;
    this.queueStatus = null;
    this.matchData = null;
    this.callbacks = {};
  }

  // 初始化
  init(multiplayerManager) {
    // 生成唯一的实例ID，用于识别当前实例
    this.instanceId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    console.log('RankedUI实例ID:', this.instanceId);

    if (multiplayerManager) {
      this.multiplayerManager = multiplayerManager;
    }
    this.setupEventListeners();
    console.log('排位赛UI初始化完成');
  }

  // 设置事件监听器
  setupEventListeners() {
    console.log('设置RankedUI事件监听器，实例ID:', this.instanceId);

    // 先清理旧的事件监听器
    this.cleanupMultiplayerEvents();

    // 监听排位赛相关事件
    this.multiplayerManager.on('userRanking', (data) => {
      // 检查数据是否真的有变化
      if (!this.userRanking || JSON.stringify(this.userRanking) !== JSON.stringify(data)) {
        console.log('用户排位信息更新:', data);
        this.userRanking = data;
        this.updateRankingDisplay();
      }
    });

    this.multiplayerManager.on('queueJoined', (data) => {
      this.queueStatus = data;
      this.showQueueScreen();
    });

    this.multiplayerManager.on('queueUpdate', (data) => {
      this.queueStatus = { ...this.queueStatus, ...data };
      this.updateQueueDisplay();
    });

    this.multiplayerManager.on('queueLeft', () => {
      this.queueStatus = null;
      this.showMainScreen();
    });

    this.multiplayerManager.on('matchFound', (data) => {
      this.matchData = data;
      this.showMatchFoundScreen();
    });

    this.multiplayerManager.on('rankedGameStarting', (data) => {
      this.hideUI();
      this.emit('gameStarting', data);
    });

    // 注释掉自动显示排位赛结果界面，让游戏界面自己处理结束逻辑
    // this.multiplayerManager.on('rankedGameCompleted', (data) => {
    //   this.showGameResultScreen(data);
    // });
  }

  // 显示排位赛主界面
  showMainScreen() {
    this.currentScreen = 'main';
    this.hideAllScreens();

    try {
      const canvas = this.getCanvas();
      if (!canvas) {
        throw new Error('无法获取Canvas');
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('无法获取2D上下文');
      }

      const { width, height } = this.getLogicalSize();

      // 强制清空画布，确保重新绘制
      ctx.clearRect(0, 0, width, height);

      // 绘制渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#1a1a2e');
      gradient.addColorStop(0.5, '#16213e');
      gradient.addColorStop(1, '#0f1419');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);

      // 绘制装饰性元素
      this.drawDecorations(ctx, width, height);

      // 渲染BGM开关按钮
      this.renderBgmToggleButton(ctx);

      // 标题区域
      this.drawTitle(ctx, width, height);

      // 用户排位信息
      if (this.userRanking) {
        this.drawUserRankingCard(ctx, width, height);
      }

      // 按钮
      this.drawMainButtons(ctx, width, height);

      // 绑定点击事件
      this.bindMainScreenEvents();

    } catch (error) {
      console.error('❌ RankedUI显示主界面失败:', error);
      // 显示错误信息给用户
      this.showError('排位赛界面显示失败: ' + error.message);
    }
  }

  // 绘制装饰性元素
  drawDecorations(ctx, width, height) {
    // 绘制顶部装饰线条（往下移动）
    const lineGradient = ctx.createLinearGradient(0, 0, width, 0);
    lineGradient.addColorStop(0, 'rgba(255, 215, 0, 0)');
    lineGradient.addColorStop(0.5, 'rgba(255, 215, 0, 0.8)');
    lineGradient.addColorStop(1, 'rgba(255, 215, 0, 0)');

    ctx.fillStyle = lineGradient;
    ctx.fillRect(0, 90, width, 3); // 从50移动到90
    ctx.fillRect(0, 95, width, 1); // 从55移动到95

    // 绘制角落装饰（往下移动）
    ctx.strokeStyle = 'rgba(255, 215, 0, 0.3)';
    ctx.lineWidth = 2;

    // 左上角
    ctx.beginPath();
    ctx.moveTo(20, 60); // 从20移动到60
    ctx.lineTo(60, 60); // 从20移动到60
    ctx.moveTo(20, 60); // 从20移动到60
    ctx.lineTo(20, 100); // 从60移动到100
    ctx.stroke();

    // 右上角
    ctx.beginPath();
    ctx.moveTo(width - 20, 60); // 从20移动到60
    ctx.lineTo(width - 60, 60); // 从20移动到60
    ctx.moveTo(width - 20, 60); // 从20移动到60
    ctx.lineTo(width - 20, 100); // 从60移动到100
    ctx.stroke();

    // 绘制背景图案
    ctx.fillStyle = 'rgba(255, 255, 255, 0.02)';
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * width;
      const y = Math.random() * height;
      const size = Math.random() * 3 + 1;
      ctx.fillRect(x, y, size, size);
    }
  }

  // 绘制标题
  drawTitle(ctx, width, height) {
    // 主标题
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 36px Arial';
    ctx.textAlign = 'center';
    ctx.shadowColor = 'rgba(255, 215, 0, 0.5)';
    ctx.shadowBlur = 10;
    ctx.fillText('⚔️ 排位竞技 ⚔️', width / 2, 160); // 进一步下移到160

    // 清除阴影
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;

    // 副标题
    ctx.fillStyle = 'rgba(255, 215, 0, 0.8)';
    ctx.font = '16px Arial';
    ctx.fillText('证明你的实力，攀登巅峰', width / 2, 185); // 进一步下移到185
  }

  // 绘制用户排位卡片
  drawUserRankingCard(ctx, width, height) {
    // 根据屏幕宽度调整卡片尺寸
    const cardWidth = Math.min(400, width - 40);
    const cardHeight = 140;
    const cardX = (width - cardWidth) / 2;
    const cardY = 220; // 进一步下移到220

    // 卡片阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillRect(cardX + 5, cardY + 5, cardWidth, cardHeight);

    // 卡片渐变背景
    const cardGradient = ctx.createLinearGradient(cardX, cardY, cardX, cardY + cardHeight);
    cardGradient.addColorStop(0, 'rgba(45, 45, 68, 0.95)');
    cardGradient.addColorStop(0.5, 'rgba(35, 35, 58, 0.95)');
    cardGradient.addColorStop(1, 'rgba(25, 25, 48, 0.95)');
    ctx.fillStyle = cardGradient;
    ctx.fillRect(cardX, cardY, cardWidth, cardHeight);

    // 卡片边框
    const borderGradient = ctx.createLinearGradient(cardX, cardY, cardX + cardWidth, cardY);
    borderGradient.addColorStop(0, 'rgba(255, 215, 0, 0.3)');
    borderGradient.addColorStop(0.5, 'rgba(255, 215, 0, 0.8)');
    borderGradient.addColorStop(1, 'rgba(255, 215, 0, 0.3)');
    ctx.strokeStyle = borderGradient;
    ctx.lineWidth = 3;
    ctx.strokeRect(cardX, cardY, cardWidth, cardHeight);

    // 内部装饰边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    ctx.strokeRect(cardX + 10, cardY + 10, cardWidth - 20, cardHeight - 20);

    // 段位区域背景
    const tierBgSize = Math.min(100, cardWidth * 0.25);
    const tierBgGradient = ctx.createRadialGradient(cardX + tierBgSize/2 + 15, cardY + 50, 0, cardX + tierBgSize/2 + 15, cardY + 50, tierBgSize/2);
    tierBgGradient.addColorStop(0, 'rgba(255, 215, 0, 0.2)');
    tierBgGradient.addColorStop(1, 'rgba(255, 215, 0, 0)');
    ctx.fillStyle = tierBgGradient;
    ctx.fillRect(cardX + 15, cardY + 15, tierBgSize, 70);

    // 段位图标
    ctx.font = '28px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(this.userRanking.tier.icon || '🏆', cardX + tierBgSize/2 + 15, cardY + 45);

    // 段位名称
    ctx.fillStyle = this.userRanking.tier.color || '#FFD700';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(this.userRanking.tier.name || '青铜 5', cardX + tierBgSize/2 + 15, cardY + 70);

    // 右侧信息区域
    const infoStartX = cardX + tierBgSize + 25;
    const infoWidth = cardWidth - tierBgSize - 35;

    ctx.textAlign = 'left';

    // 积分标签
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.font = '12px Arial';
    ctx.fillText('积分', infoStartX, cardY + 30);

    // 积分数值（突出显示）
    ctx.fillStyle = '#FFD700';
    ctx.font = 'bold 20px Arial';
    ctx.fillText(`${this.userRanking.rating || 1200}`, infoStartX, cardY + 50);

    // 排名（如果空间足够）
    if (this.userRanking.rank && infoWidth > 180) {
      const rankX = infoStartX + 100;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.font = '12px Arial';
      ctx.fillText('排名', rankX, cardY + 30);

      ctx.fillStyle = '#4CAF50';
      ctx.font = 'bold 18px Arial';
      ctx.fillText(`#${this.userRanking.rank}`, rankX, cardY + 50);
    }

    // 统计信息行
    const statsY = cardY + 85;
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.font = '14px Arial';

    // 胜率
    const winRate = ((this.userRanking.winRate || 0) * 100).toFixed(1);
    ctx.fillText(`胜率 ${winRate}%`, infoStartX, statsY);

    // 总场次
    const totalGames = this.userRanking.totalGames || 0;
    ctx.fillText(`${totalGames}场`, infoStartX + 70, statsY);

    // 连胜（如果有且空间足够）
    if ((this.userRanking.currentStreak || 0) > 0 && infoWidth > 160) {
      ctx.fillStyle = '#4CAF50';
      ctx.font = 'bold 14px Arial';
      ctx.fillText(`🔥${this.userRanking.currentStreak}连胜`, infoStartX + 130, statsY);
    }

    // 段位进度条
    if (this.userRanking.tier.progress !== undefined) {
      this.drawProgressBar(ctx, cardX + 15, cardY + 110, cardWidth - 30, 12, this.userRanking.tier.progress);
    }
  }

  // 绘制进度条
  drawProgressBar(ctx, x, y, width, height, progress) {
    // 进度条背景（凹陷效果）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
    ctx.fillRect(x, y + 2, width, height - 2);

    ctx.fillStyle = 'rgba(26, 26, 46, 0.8)';
    ctx.fillRect(x, y, width, height - 2);

    // 进度条渐变
    const progressWidth = (width * progress) / 100;
    if (progressWidth > 0) {
      const progressGradient = ctx.createLinearGradient(x, y, x, y + height);
      progressGradient.addColorStop(0, '#FFD700');
      progressGradient.addColorStop(0.5, '#FFA500');
      progressGradient.addColorStop(1, '#FF8C00');

      ctx.fillStyle = progressGradient;
      ctx.fillRect(x + 1, y + 1, progressWidth - 2, height - 4);

      // 进度条光泽效果
      const glowGradient = ctx.createLinearGradient(x, y, x, y + height / 2);
      glowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
      glowGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
      ctx.fillStyle = glowGradient;
      ctx.fillRect(x + 1, y + 1, progressWidth - 2, height / 2 - 2);
    }

    // 边框
    ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, height);

    // 进度文字（带阴影）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${progress}%`, x + width / 2 + 1, y + height / 2 + 5);

    ctx.fillStyle = '#ffffff';
    ctx.fillText(`${progress}%`, x + width / 2, y + height / 2 + 4);
  }

  // 渲染BGM开关按钮（排位赛页面专用）
  renderBgmToggleButton(ctx) {
    const buttonSize = 50
    const margin = 20
    const x = margin
    const y = margin

    // 按钮背景
    const bgColor = window.gameApp && window.gameApp.bgmEnabled ? '#4CAF50' : '#757575'
    ctx.fillStyle = bgColor
    ctx.fillRect(x, y, buttonSize, buttonSize)

    // 按钮边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    ctx.lineWidth = 2
    ctx.strokeRect(x, y, buttonSize, buttonSize)

    // 按钮图标
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 24px Arial'
    ctx.textAlign = 'center'
    const icon = window.gameApp && window.gameApp.bgmEnabled ? '🎵' : '🔇'
    ctx.fillText(icon, x + buttonSize / 2, y + buttonSize / 2 + 8)

    // 存储按钮区域（用于点击检测）
    if (!this.buttons) this.buttons = {}
    this.buttons['bgmToggle'] = { x, y, width: buttonSize, height: buttonSize }
  }

  // 绘制主界面按钮
  drawMainButtons(ctx, width, height) {
    try {
      // 重新设计按钮布局 - 垂直排列避免重叠
      const buttonWidth = Math.min(200, width - 80); // 增加按钮宽度
      const buttonHeight = 50;
      const buttonSpacing = 15; // 按钮间距
      const startY = height - 240; // 按钮区域起始位置，往上移动60像素

      // 开始匹配按钮（主要按钮）
      const startButton = {
        x: (width - buttonWidth) / 2,
        y: startY,
        width: buttonWidth,
        height: buttonHeight,
        text: '⚔️ 开始匹配',
        color: '#FF6B35',
        glowColor: '#FF8C42',
        textColor: '#ffffff',
        isPrimary: true
      };

      // 排行榜按钮
      const leaderboardButton = {
        x: (width - buttonWidth) / 2,
        y: startY + buttonHeight + buttonSpacing,
        width: buttonWidth,
        height: buttonHeight,
        text: '🏆 排行榜',
        color: '#4A90E2',
        glowColor: '#5BA0F2',
        textColor: '#ffffff',
        isPrimary: false
      };

      // 返回按钮
      const backButton = {
        x: (width - buttonWidth) / 2,
        y: startY + (buttonHeight + buttonSpacing) * 2,
        width: buttonWidth,
        height: buttonHeight,
        text: '← 返回',
        color: '#6C757D',
        glowColor: '#868E96',
        textColor: '#ffffff',
        isPrimary: false
      };

      this.drawButton(ctx, startButton);
      this.drawButton(ctx, leaderboardButton);

      this.drawButton(ctx, backButton);

      // 存储按钮位置用于点击检测（保留已有的bgmToggle按钮）
      if (!this.buttons) this.buttons = {};
      this.buttons.startMatch = startButton;
      this.buttons.leaderboard = leaderboardButton;
      this.buttons.back = backButton;

    } catch (error) {
      console.error('RankedUI绘制主界面按钮失败:', error);
      this.showError('按钮绘制失败: ' + error.message);
    }
  }

  // 绘制按钮
  drawButton(ctx, button) {
    try {
      // 按钮阴影
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.fillRect(button.x + 3, button.y + 3, button.width, button.height);

      // 按钮渐变背景
      const buttonGradient = ctx.createLinearGradient(
        button.x, button.y,
        button.x, button.y + button.height
      );

      if (button.isPrimary) {
        buttonGradient.addColorStop(0, button.glowColor || button.color);
        buttonGradient.addColorStop(0.5, button.color);
        buttonGradient.addColorStop(1, this.darkenColor(button.color, 0.2));
      } else {
        buttonGradient.addColorStop(0, this.lightenColor(button.color, 0.1));
        buttonGradient.addColorStop(1, button.color);
      }

      ctx.fillStyle = buttonGradient;
      ctx.fillRect(button.x, button.y, button.width, button.height);

      // 按钮高光效果
      const highlightGradient = ctx.createLinearGradient(
        button.x, button.y,
        button.x, button.y + button.height / 2
      );
      highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
      highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
      ctx.fillStyle = highlightGradient;
      ctx.fillRect(button.x + 2, button.y + 2, button.width - 4, button.height / 2 - 2);

      // 按钮边框
      ctx.strokeStyle = button.isPrimary ?
        'rgba(255, 255, 255, 0.8)' :
        'rgba(255, 255, 255, 0.4)';
      ctx.lineWidth = button.isPrimary ? 2 : 1;
      ctx.strokeRect(button.x, button.y, button.width, button.height);

      // 按钮文字阴影
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      ctx.font = button.isPrimary ? 'bold 16px Arial' : 'bold 14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        button.text,
        button.x + button.width / 2 + 1,
        button.y + button.height / 2 + 7
      );

      // 按钮文字
      ctx.fillStyle = button.textColor || '#ffffff';
      ctx.fillText(
        button.text,
        button.x + button.width / 2,
        button.y + button.height / 2 + 6
      );

    } catch (error) {
      console.error('RankedUI绘制按钮失败:', button.text, error);
    }
  }

  // 颜色工具函数
  darkenColor(color, factor) {
    // 简单的颜色变暗函数
    if (color.startsWith('#')) {
      const r = parseInt(color.substr(1, 2), 16);
      const g = parseInt(color.substr(3, 2), 16);
      const b = parseInt(color.substr(5, 2), 16);
      return `rgb(${Math.floor(r * (1 - factor))}, ${Math.floor(g * (1 - factor))}, ${Math.floor(b * (1 - factor))})`;
    }
    return color;
  }

  lightenColor(color, factor) {
    // 简单的颜色变亮函数
    if (color.startsWith('#')) {
      const r = parseInt(color.substr(1, 2), 16);
      const g = parseInt(color.substr(3, 2), 16);
      const b = parseInt(color.substr(5, 2), 16);
      return `rgb(${Math.min(255, Math.floor(r * (1 + factor)))}, ${Math.min(255, Math.floor(g * (1 + factor)))}, ${Math.min(255, Math.floor(b * (1 + factor)))})`;
    }
    return color;
  }

  // 绘制加载动画
  drawLoadingAnimation(ctx, centerX, centerY) {
    const time = Date.now() / 50; // 更快的动画速度
    const radius = 25;
    const dotCount = 8;

    // 外圈旋转点
    for (let i = 0; i < dotCount; i++) {
      const angle = (i * Math.PI * 2) / dotCount + time * 0.05;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;

      // 基于位置的透明度变化
      const alpha = (Math.sin(time * 0.05 + i * 0.8) + 1) / 2;
      const size = 2 + alpha * 3;

      ctx.fillStyle = `rgba(76, 175, 80, ${0.3 + alpha * 0.7})`;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();

      // 添加光晕效果
      ctx.fillStyle = `rgba(76, 175, 80, ${alpha * 0.2})`;
      ctx.beginPath();
      ctx.arc(x, y, size + 2, 0, Math.PI * 2);
      ctx.fill();
    }

    // 内圈反向旋转点
    const innerRadius = 12;
    const innerDotCount = 6;
    for (let i = 0; i < innerDotCount; i++) {
      const angle = (i * Math.PI * 2) / innerDotCount - time * 0.08;
      const x = centerX + Math.cos(angle) * innerRadius;
      const y = centerY + Math.sin(angle) * innerRadius;

      const alpha = (Math.sin(time * 0.08 + i * 1.2) + 1) / 2;
      const size = 1.5 + alpha * 2;

      ctx.fillStyle = `rgba(33, 150, 243, ${0.4 + alpha * 0.6})`;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
    }

    // 中心脉冲效果
    const pulseAlpha = (Math.sin(time * 0.15) + 1) / 2;
    const pulseSize = 6 + pulseAlpha * 6;

    // 外层脉冲
    ctx.fillStyle = `rgba(255, 215, 0, ${pulseAlpha * 0.3})`;
    ctx.beginPath();
    ctx.arc(centerX, centerY, pulseSize + 4, 0, Math.PI * 2);
    ctx.fill();

    // 内层脉冲
    ctx.fillStyle = `rgba(255, 215, 0, ${pulseAlpha * 0.8})`;
    ctx.beginPath();
    ctx.arc(centerX, centerY, pulseSize, 0, Math.PI * 2);
    ctx.fill();
  }

  // 绑定队列界面事件
  bindQueueEvents() {

    // 清理之前的事件监听器
    this.cleanupQueueEvents();

    // 微信小游戏环境的事件处理
    if (typeof wx !== 'undefined') {
      // 初始化队列界面的防抖变量
      this.lastQueueTouchTime = 0;
      this.lastQueueTouchCoords = { x: 0, y: 0 };
      this.queueTouchDebounceDelay = 500; // 队列界面使用更长的防抖延迟

      this.queueTouchHandler = (e) => {
        // 检查是否是当前活跃的实例
        console.log('队列事件检查:', {
          activeInstance: window.activeRankedUIInstance,
          thisInstance: this.instanceId,
          currentScreen: this.currentScreen
        });

        if (window.activeRankedUIInstance !== this.instanceId) {
          console.log('队列事件被过滤：实例ID不匹配');
          return; // 静默忽略
        }

        // 只在队列界面处理事件
        if (this.currentScreen !== 'queue') {
          console.log('队列事件被过滤：不在队列界面');
          return;
        }

        const now = Date.now();
        let x, y;
        const touch = e.touches && e.touches[0] ? e.touches[0] : e;
        x = touch.clientX || touch.x || 0;
        y = touch.clientY || touch.y || 0;

        // 严格的防抖检查
        const timeDiff = now - this.lastQueueTouchTime;
        const coordDiff = Math.abs(x - this.lastQueueTouchCoords.x) + Math.abs(y - this.lastQueueTouchCoords.y);

        if (timeDiff < this.queueTouchDebounceDelay && coordDiff < 10) {
          return; // 静默忽略重复点击
        }

        // 更新防抖记录
        this.lastQueueTouchTime = now;
        this.lastQueueTouchCoords = { x, y };

        console.log('队列UI触摸事件:', x, y);

        // 首先检查BGM按钮
        if (this.buttons && this.buttons.bgmToggle) {
          if (this.isPointInButton(x, y, this.buttons.bgmToggle)) {
            console.log('点击BGM开关按钮');
            if (window.gameApp) {
              window.gameApp.toggleBgm();
            }
            return; // 处理完BGM按钮后直接返回
          }
        }

        // 检查取消按钮
        if (this.buttons && this.buttons.cancel) {
          console.log('取消按钮区域:', this.buttons.cancel);
          console.log('点击检测:', {
            x: x,
            y: y,
            buttonX: this.buttons.cancel.x,
            buttonY: this.buttons.cancel.y,
            buttonWidth: this.buttons.cancel.width,
            buttonHeight: this.buttons.cancel.height,
            inButton: this.isPointInButton(x, y, this.buttons.cancel)
          });

          if (this.isPointInButton(x, y, this.buttons.cancel)) {
            console.log('点击取消匹配按钮');
            try {
              this.cancelRankedMatch();
            } catch (error) {
              console.error('取消匹配时出错:', error);
            }
          } else {
            console.log('点击位置不在取消按钮范围内');
          }
        } else {
          console.log('取消按钮不存在:', this.buttons);
        }
      };

      wx.onTouchStart(this.queueTouchHandler);
    }
  }

  // 清理队列事件
  cleanupQueueEvents() {
    if (this.queueTouchHandler && typeof wx !== 'undefined') {
      wx.offTouchStart(this.queueTouchHandler);
      this.queueTouchHandler = null;
    }
  }

  // 绘制排行榜列表
  drawLeaderboardList(ctx, width, height, leaderboard) {
    const startY = 120;
    const itemHeight = Math.min(50, (height - 200) / 8); // 最多显示8个，最小高度50
    const maxItems = Math.min(leaderboard.length, Math.floor((height - 200) / itemHeight));
    const listWidth = width - 40;
    const listX = 20;

    console.log(`绘制排行榜列表，共${leaderboard.length}个玩家，显示前${maxItems}个`);

    for (let i = 0; i < maxItems; i++) {
      const player = leaderboard[i];
      const y = startY + i * itemHeight;

      // 排名背景
      const isTopThree = i < 3;
      let bgColor, rankColor;

      if (i === 0) {
        bgColor = 'rgba(255, 215, 0, 0.2)'; // 金色
        rankColor = '#FFD700';
      } else if (i === 1) {
        bgColor = 'rgba(192, 192, 192, 0.2)'; // 银色
        rankColor = '#C0C0C0';
      } else if (i === 2) {
        bgColor = 'rgba(205, 127, 50, 0.2)'; // 铜色
        rankColor = '#CD7F32';
      } else {
        bgColor = i % 2 === 0 ? 'rgba(45, 45, 68, 0.6)' : 'rgba(35, 35, 58, 0.6)';
        rankColor = '#ffffff';
      }

      // 绘制背景
      ctx.fillStyle = bgColor;
      ctx.fillRect(listX, y - 5, listWidth, itemHeight - 5);

      // 绘制边框（前三名特殊处理）
      if (isTopThree) {
        ctx.strokeStyle = rankColor;
        ctx.lineWidth = 2;
        ctx.strokeRect(listX, y - 5, listWidth, itemHeight - 5);
      }

      // 排名
      ctx.fillStyle = rankColor;
      ctx.font = isTopThree ? 'bold 18px Arial' : 'bold 16px Arial';
      ctx.textAlign = 'center';

      const rankX = listX + 30;
      if (i < 3) {
        const medals = ['🥇', '🥈', '🥉'];
        ctx.font = '20px Arial';
        ctx.fillText(medals[i], rankX, y + itemHeight/2 + 6);
      } else {
        ctx.fillText(`#${i + 1}`, rankX, y + itemHeight/2 + 6);
      }

      // 玩家名称
      ctx.fillStyle = '#ffffff';
      ctx.font = isTopThree ? 'bold 16px Arial' : '14px Arial';
      ctx.textAlign = 'left';
      let playerName = player.nickname || '未知玩家';
      if (playerName.length > 6) {
        playerName = playerName.substring(0, 6) + '...';
      }
      ctx.fillText(playerName, listX + 60, y + itemHeight/2 + 6);

      // 段位信息（简化显示）
      if (player.tier && listWidth > 300) {
        ctx.font = '12px Arial';
        ctx.fillStyle = player.tier.color || '#FFD700';
        const tierText = `${player.tier.icon || '🏆'}`;
        ctx.fillText(tierText, listX + 160, y + itemHeight/2 + 6);
      }

      // 积分
      ctx.fillStyle = '#4CAF50';
      ctx.font = isTopThree ? 'bold 16px Arial' : 'bold 14px Arial';
      ctx.textAlign = 'right';
      ctx.fillText(`${player.rating || 1200}`, listX + listWidth - 80, y + itemHeight/2 + 6);

      // 胜率（简化显示）
      const winRate = ((player.win_rate || 0) * 100).toFixed(0);
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.font = '12px Arial';
      ctx.fillText(`${winRate}%`, listX + listWidth - 30, y + itemHeight/2 + 6);
    }

    // 如果没有数据
    if (leaderboard.length === 0) {
      ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
      ctx.font = '18px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('暂无排行榜数据', width / 2, startY + 100);
    }
  }

  // 绑定排行榜事件
  bindLeaderboardEvents() {

    // 清理之前的事件监听器
    this.cleanupLeaderboardEvents();

    // 微信小游戏环境的事件处理
    if (typeof wx !== 'undefined') {
      this.leaderboardTouchHandler = (e) => {
        // 只在排行榜界面处理事件
        if (this.currentScreen !== 'leaderboard') {
          return;
        }

        let x, y;
        const touch = e.touches && e.touches[0] ? e.touches[0] : e;
        x = touch.clientX || touch.x || 0;
        y = touch.clientY || touch.y || 0;
        console.log('排行榜UI触摸事件:', x, y);

        // 首先检查BGM按钮
        if (this.buttons && this.buttons.bgmToggle) {
          if (this.isPointInButton(x, y, this.buttons.bgmToggle)) {
            console.log('点击BGM开关按钮');
            if (window.gameApp) {
              window.gameApp.toggleBgm();
            }
            return; // 处理完BGM按钮后直接返回
          }
        }

        // 检查返回按钮
        if (this.buttons && this.buttons.back) {
          if (this.isPointInButton(x, y, this.buttons.back)) {
            console.log('点击返回按钮');
            this.showMainScreen();
          }
        }
      };

      wx.onTouchStart(this.leaderboardTouchHandler);
    }
  }

  // 清理排行榜事件
  cleanupLeaderboardEvents() {
    if (this.leaderboardTouchHandler && typeof wx !== 'undefined') {
      wx.offTouchStart(this.leaderboardTouchHandler);
      this.leaderboardTouchHandler = null;
    }
  }

  // 获取Canvas
  getCanvas() {
    // 优先使用共享Canvas（与MultiplayerUI保持一致）
    if (this.sharedCanvas) {
      return this.sharedCanvas;
    }

    // 优先使用全局canvas变量（微信小游戏环境）
    if (typeof canvas !== 'undefined' && canvas) {
      return canvas;
    }

    // 尝试从wx创建Canvas
    if (typeof wx !== 'undefined' && wx.createCanvas) {
      return wx.createCanvas();
    }

    // 浏览器环境
    if (typeof document !== 'undefined') {
      return document.getElementById('gameCanvas');
    }

    console.error('RankedUI无法获取Canvas');
    return null;
  }

  // 获取逻辑尺寸
  getLogicalSize() {
    try {
      // 优先使用共享Canvas的逻辑尺寸（与MultiplayerUI保持一致）
      if (this.sharedCanvas) {
        const width = this.sharedCanvas._logicalWidth || this.sharedCanvas.width || 800;
        const height = this.sharedCanvas._logicalHeight || this.sharedCanvas.height || 600;
        return { width, height };
      }

      // 尝试从全局canvas获取尺寸
      if (typeof canvas !== 'undefined' && canvas) {
        const width = canvas._logicalWidth || canvas.width || 800;
        const height = canvas._logicalHeight || canvas.height || 600;
        return { width, height };
      }

      // 微信小游戏环境
      if (typeof wx !== 'undefined') {
        const info = wx.getSystemInfoSync();
        return { width: info.windowWidth, height: info.windowHeight };
      }

      // 默认尺寸
      return { width: 800, height: 600 };
    } catch (error) {
      console.error('RankedUI获取逻辑尺寸失败:', error);
      return { width: 800, height: 600 };
    }
  }

  // 隐藏所有界面
  hideAllScreens() {
    // 保留BGM按钮，清理其他按钮
    const bgmButton = this.buttons && this.buttons.bgmToggle ? this.buttons.bgmToggle : null;
    this.buttons = {};
    if (bgmButton) {
      this.buttons.bgmToggle = bgmButton;
    }

    // 清理定时器
    if (this.queueTimer) {
      clearInterval(this.queueTimer);
      this.queueTimer = null;
    }

    if (this.animationTimer) {
      clearInterval(this.animationTimer);
      this.animationTimer = null;
    }

    // 注意：不调用cleanupMainEvents()，保持活跃实例ID
  }

  // 隐藏UI
  hideUI() {
    this.currentScreen = 'hidden';
    this.cleanup();
  }

  // 清理资源
  cleanup() {
    console.log('清理RankedUI资源，实例ID:', this.instanceId);

    // 清理事件监听器等
    this.buttons = {};

    // 清理所有事件监听器
    this.cleanupMainEvents();
    this.cleanupQueueEvents();
    this.cleanupLeaderboardEvents();

    // 清理MultiplayerManager事件监听器
    this.cleanupMultiplayerEvents();

    // 重置事件绑定标志
    this.queueEventsbound = false;

    // 清理定时器
    if (this.queueTimer) {
      clearInterval(this.queueTimer);
      this.queueTimer = null;
    }

    if (this.animationTimer) {
      clearInterval(this.animationTimer);
      this.animationTimer = null;
    }

    if (this.updateRankingTimer) {
      clearTimeout(this.updateRankingTimer);
      this.updateRankingTimer = null;
    }

    if (this.matchCountdownTimer) {
      clearInterval(this.matchCountdownTimer);
      this.matchCountdownTimer = null;
    }
  }

  // 清理MultiplayerManager事件监听器
  cleanupMultiplayerEvents() {
    if (!this.multiplayerManager) {
      return;
    }

    console.log('清理MultiplayerManager事件监听器，实例ID:', this.instanceId);

    // 清理所有在setupEventListeners中绑定的事件监听器
    const eventTypes = [
      'userRanking', 'queueJoined', 'queueUpdate', 'queueLeft',
      'matchFound', 'rankedGameStarting', 'rankedGameCompleted'
    ];

    eventTypes.forEach(eventType => {
      // 清理该事件类型的所有监听器
      if (this.multiplayerManager.callbacks && this.multiplayerManager.callbacks[eventType]) {
        const originalCount = this.multiplayerManager.callbacks[eventType].length;
        // 简单粗暴的方法：清空所有监听器（因为每次都会重新创建RankedUI实例）
        this.multiplayerManager.callbacks[eventType] = [];
        console.log(`清理事件 ${eventType} 的 ${originalCount} 个监听器`);
      }
    });
  }

  // 事件发射器
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }

  // 事件监听器
  on(event, callback) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
  }

  // 简化版本的其他方法
  showQueueScreen() {
    console.log('显示匹配队列界面');
    this.currentScreen = 'queue';
    this.hideAllScreens();

    // 初始化队列开始时间
    if (!this.queueStartTime) {
      this.queueStartTime = Date.now();
    }

    try {
      const canvas = this.getCanvas();
      if (!canvas) {
        throw new Error('无法获取Canvas');
      }

      const ctx = canvas.getContext('2d');
      const { width, height } = this.getLogicalSize();

      // 绘制渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#1a1a2e');
      gradient.addColorStop(0.5, '#16213e');
      gradient.addColorStop(1, '#0f1419');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);

      // 绘制装饰性元素
      this.drawDecorations(ctx, width, height);

      // 更新队列显示内容
      this.updateQueueDisplay();

      // 启动定时更新
      this.startQueueTimer();

      console.log('✅ 匹配队列界面显示完成');

    } catch (error) {
      console.error('❌ 显示匹配队列界面失败:', error);
      this.showError('匹配队列界面显示失败: ' + error.message);
    }
  }

  // 启动队列定时器
  startQueueTimer() {
    // 清除之前的定时器
    if (this.queueTimer) {
      clearInterval(this.queueTimer);
    }
    if (this.animationTimer) {
      clearInterval(this.animationTimer);
    }

    // 每秒更新一次等待时间
    this.queueTimer = setInterval(() => {
      if (this.currentScreen === 'queue') {
        // 更新本地等待时间
        if (this.queueStartTime) {
          const waitTime = Math.floor((Date.now() - this.queueStartTime) / 1000);
          if (!this.queueStatus) {
            this.queueStatus = {};
          }
          this.queueStatus.waitTime = waitTime;
        }

        this.updateQueueDisplay();
      } else {
        // 如果不在队列界面，清除定时器
        clearInterval(this.queueTimer);
        this.queueTimer = null;
      }
    }, 1000);

    // 高频率动画更新（60fps）
    this.animationTimer = setInterval(() => {
      if (this.currentScreen === 'queue') {
        this.updateQueueAnimation();
      } else {
        clearInterval(this.animationTimer);
        this.animationTimer = null;
      }
    }, 16); // 约60fps
  }

  // 更新队列动画（只更新动画部分，不重绘整个界面）
  updateQueueAnimation() {
    try {
      const canvas = this.getCanvas();
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      const { width, height } = this.getLogicalSize();
      const centerY = height / 2;
      const animationY = centerY + 100;

      // 只清除动画区域
      const animationSize = 80;
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#1a1a2e');
      gradient.addColorStop(0.5, '#16213e');
      gradient.addColorStop(1, '#0f1419');
      ctx.fillStyle = gradient;
      ctx.fillRect(
        width / 2 - animationSize / 2,
        animationY - animationSize / 2,
        animationSize,
        animationSize
      );

      // 重新绘制加载动画
      this.drawLoadingAnimation(ctx, width / 2, animationY);

    } catch (error) {
      console.error('更新队列动画失败:', error);
    }
  }

  updateQueueDisplay() {
    if (this.currentScreen !== 'queue') return;

    try {
      const canvas = this.getCanvas();
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      const { width, height } = this.getLogicalSize();

      // 清空中间区域（保留背景和装饰）
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#1a1a2e');
      gradient.addColorStop(0.5, '#16213e');
      gradient.addColorStop(1, '#0f1419');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 100, width, height - 200);

      // 渲染BGM开关按钮
      this.renderBgmToggleButton(ctx);

      // 标题
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 28px Arial';
      ctx.textAlign = 'center';
      ctx.shadowColor = 'rgba(255, 215, 0, 0.5)';
      ctx.shadowBlur = 10;
      ctx.fillText('🔍 正在匹配...', width / 2, 150);

      // 清除阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;

      // 匹配状态信息
      const centerY = height / 2;

      // 队列位置
      if (this.queueStatus && this.queueStatus.position !== undefined) {
        ctx.fillStyle = '#4CAF50';
        ctx.font = 'bold 24px Arial';
        ctx.fillText(`队列位置: ${this.queueStatus.position}`, width / 2, centerY - 40);
      }

      // 等待时间
      const waitTime = this.queueStatus ? Math.floor(this.queueStatus.waitTime || 0) : 0;
      ctx.fillStyle = '#2196F3';
      ctx.font = '20px Arial';
      ctx.fillText(`等待时间: ${waitTime}秒`, width / 2, centerY);

      // 预计等待时间
      if (this.queueStatus && this.queueStatus.estimatedWaitTime) {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = '18px Arial';
        ctx.fillText(`预计等待: ${this.queueStatus.estimatedWaitTime}秒`, width / 2, centerY + 40);
      }

      // 加载动画
      this.drawLoadingAnimation(ctx, width / 2, centerY + 100);

      // 取消按钮（调整位置避免和导航条重合）
      const cancelButton = {
        x: (width - 160) / 2,
        y: height - 180, // 从height-120调整到height-180，往上移动60像素
        width: 160,
        height: 50,
        text: '取消匹配',
        color: '#f44336',
        glowColor: '#f66356',
        textColor: '#ffffff',
        isPrimary: true
      };

      this.drawButton(ctx, cancelButton);

      // 保留BGM按钮
      const bgmButton = this.buttons && this.buttons.bgmToggle ? this.buttons.bgmToggle : null;
      this.buttons = { cancel: cancelButton };
      if (bgmButton) {
        this.buttons.bgmToggle = bgmButton;
      }

      // 只在第一次显示队列界面时绑定事件
      if (!this.queueEventsbound) {
        this.bindQueueEvents();
        this.queueEventsbound = true;
      }

    } catch (error) {
      console.error('更新队列显示失败:', error);
    }
  }

  showMatchFoundScreen() {
    console.log('显示匹配成功界面');
    this.currentScreen = 'matchFound';
    this.hideAllScreens();

    try {
      const canvas = this.getCanvas();
      if (!canvas) {
        throw new Error('无法获取Canvas');
      }

      const ctx = canvas.getContext('2d');
      const { width, height } = this.getLogicalSize();

      // 绘制渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#1a1a2e');
      gradient.addColorStop(0.5, '#16213e');
      gradient.addColorStop(1, '#0f1419');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);

      // 绘制装饰性元素
      this.drawDecorations(ctx, width, height);

      // 标题
      ctx.fillStyle = '#4CAF50';
      ctx.font = 'bold 32px Arial';
      ctx.textAlign = 'center';
      ctx.shadowColor = 'rgba(76, 175, 80, 0.5)';
      ctx.shadowBlur = 10;
      ctx.fillText('🎉 匹配成功！', width / 2, 120);

      // 清除阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;

      // 副标题
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.font = '18px Arial';
      ctx.fillText('正在准备游戏...', width / 2, 150);

      // 显示匹配信息
      if (this.matchData) {
        this.drawMatchInfo(ctx, width, height);
      }

      // 倒计时显示
      this.drawCountdown(ctx, width, height);

      // 启动倒计时更新
      this.startMatchCountdown();

      console.log('✅ 匹配成功界面显示完成');

    } catch (error) {
      console.error('❌ 显示匹配成功界面失败:', error);
      this.showError('匹配成功界面显示失败: ' + error.message);
    }
  }

  // 启动匹配倒计时
  startMatchCountdown() {
    // 清除之前的定时器
    if (this.matchCountdownTimer) {
      clearInterval(this.matchCountdownTimer);
    }

    // 每秒更新倒计时
    this.matchCountdownTimer = setInterval(() => {
      if (this.currentScreen === 'matchFound') {
        const canvas = this.getCanvas();
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const { width, height } = this.getLogicalSize();

        // 只更新倒计时区域
        const countdownY = height - 150;
        const radius = 40;
        const clearSize = radius * 2 + 20;

        // 清除倒计时区域
        const gradient = ctx.createLinearGradient(0, countdownY - radius - 10, 0, countdownY + radius + 70);
        gradient.addColorStop(0, '#1a1a2e');
        gradient.addColorStop(0.5, '#16213e');
        gradient.addColorStop(1, '#0f1419');
        ctx.fillStyle = gradient;
        ctx.fillRect(width / 2 - clearSize / 2, countdownY - radius - 10, clearSize, radius * 2 + 80);

        // 重新绘制倒计时
        this.drawCountdown(ctx, width, height);

        // 检查倒计时是否结束
        const startTime = this.matchData ? this.matchData.estimatedStartTime : Date.now() + 10000;
        const remainingTime = Math.max(0, Math.ceil((startTime - Date.now()) / 1000));

        if (remainingTime <= 0) {
          // 倒计时结束后再等待1秒，然后清除定时器
          setTimeout(() => {
            clearInterval(this.matchCountdownTimer);
            this.matchCountdownTimer = null;
          }, 1000);
        }
      } else {
        // 如果不在匹配成功界面，清除定时器
        clearInterval(this.matchCountdownTimer);
        this.matchCountdownTimer = null;
      }
    }, 1000);
  }

  // 绘制匹配信息
  drawMatchInfo(ctx, width, height) {
    if (!this.matchData || !this.matchData.players) return;

    const startY = 180;
    const playerHeight = 40;
    const cardWidth = Math.min(350, width - 40);
    const cardX = (width - cardWidth) / 2;

    // 匹配信息卡片背景
    ctx.fillStyle = 'rgba(45, 45, 68, 0.8)';
    ctx.fillRect(cardX, startY, cardWidth, this.matchData.players.length * playerHeight + 40);

    // 卡片边框
    ctx.strokeStyle = 'rgba(76, 175, 80, 0.6)';
    ctx.lineWidth = 2;
    ctx.strokeRect(cardX, startY, cardWidth, this.matchData.players.length * playerHeight + 40);

    // 标题
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('对战玩家', width / 2, startY + 25);

    // 绘制玩家列表
    this.matchData.players.forEach((player, index) => {
      const y = startY + 40 + index * playerHeight;

      // 玩家背景
      if (index % 2 === 0) {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.fillRect(cardX + 5, y - 15, cardWidth - 10, playerHeight - 5);
      }

      // 玩家名称
      ctx.fillStyle = '#ffffff';
      ctx.font = '14px Arial';
      ctx.textAlign = 'left';
      const playerName = player.nickName || '未知玩家';
      ctx.fillText(playerName, cardX + 15, y);

      // 玩家积分
      ctx.fillStyle = '#FFD700';
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'right';
      ctx.fillText(`${player.rating || 1200}分`, cardX + cardWidth - 15, y);

      // 段位图标
      if (player.tier && player.tier.icon) {
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(player.tier.icon, cardX + cardWidth - 60, y);
      }
    });
  }

  // 绘制倒计时
  drawCountdown(ctx, width, height) {
    const countdownY = height - 150;

    // 计算倒计时时间（假设10秒倒计时）
    const startTime = this.matchData ? this.matchData.estimatedStartTime : Date.now() + 10000;
    const remainingTime = Math.max(0, Math.ceil((startTime - Date.now()) / 1000));

    // 倒计时背景圆圈
    const centerX = width / 2;
    const radius = 40;

    ctx.strokeStyle = 'rgba(76, 175, 80, 0.3)';
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.arc(centerX, countdownY, radius, 0, Math.PI * 2);
    ctx.stroke();

    // 倒计时进度圆圈
    const progress = (10 - remainingTime) / 10;
    ctx.strokeStyle = '#4CAF50';
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.arc(centerX, countdownY, radius, -Math.PI / 2, -Math.PI / 2 + progress * Math.PI * 2);
    ctx.stroke();

    // 倒计时数字
    ctx.fillStyle = '#4CAF50';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(remainingTime.toString(), centerX, countdownY + 8);

    // 倒计时文字
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.font = '14px Arial';
    ctx.fillText('秒后开始游戏', centerX, countdownY + 60);

    // 如果倒计时结束，显示"即将开始"
    if (remainingTime <= 0) {
      ctx.fillStyle = '#FFD700';
      ctx.font = 'bold 18px Arial';
      ctx.fillText('即将开始...', centerX, countdownY + 80);
    }
  }

  // 启动匹配倒计时
  startMatchCountdown() {
    // 清除之前的倒计时
    if (this.matchCountdownTimer) {
      clearInterval(this.matchCountdownTimer);
    }

    // 每秒更新倒计时显示
    this.matchCountdownTimer = setInterval(() => {
      if (this.currentScreen === 'matchFound') {
        // 只更新倒计时区域
        this.updateCountdownDisplay();
      } else {
        // 如果不在匹配成功界面，清除定时器
        clearInterval(this.matchCountdownTimer);
        this.matchCountdownTimer = null;
      }
    }, 1000);
  }

  // 更新倒计时显示
  updateCountdownDisplay() {
    try {
      const canvas = this.getCanvas();
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      const { width, height } = this.getLogicalSize();

      // 只清除倒计时区域
      const countdownY = height - 150;
      const clearRadius = 80;

      // 用背景色清除倒计时区域
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#1a1a2e');
      gradient.addColorStop(0.5, '#16213e');
      gradient.addColorStop(1, '#0f1419');
      ctx.fillStyle = gradient;
      ctx.fillRect(
        width / 2 - clearRadius,
        countdownY - clearRadius,
        clearRadius * 2,
        clearRadius * 2
      );

      // 重新绘制倒计时
      this.drawCountdown(ctx, width, height);

    } catch (error) {
      console.error('更新倒计时显示失败:', error);
    }
  }

  showGameResultScreen(result) {
    console.log('显示游戏结果界面', result);

    // 调用rankedUI显示结果界面
    if (this.rankedUI) {
      this.rankedUI.showGameResultScreen(result);
    } else {
      console.error('rankedUI未初始化，无法显示游戏结果');
    }
  }

  bindMainScreenEvents() {
    // 清理之前的事件监听器
    this.cleanupMainEvents();

    console.log('绑定主界面事件，实例ID:', this.instanceId);

    // 微信小游戏环境的事件处理
    if (typeof wx !== 'undefined') {
      // 初始化防抖相关变量
      this.lastTouchTime = 0;
      this.lastTouchCoords = { x: 0, y: 0 };
      this.touchDebounceDelay = 300; // 300ms防抖延迟

      this.rankedTouchHandler = (e) => {
        // 检查是否是当前活跃的实例
        if (window.activeRankedUIInstance !== this.instanceId) {
          return; // 静默忽略，不输出日志
        }

        // 只在主界面处理事件
        if (this.currentScreen !== 'main') {
          return;
        }

        const now = Date.now();
        let x, y;
        const touch = e.touches && e.touches[0] ? e.touches[0] : e;
        x = touch.clientX || touch.x || 0;
        y = touch.clientY || touch.y || 0;

        // 严格的防抖检查：时间间隔和坐标都要检查
        const timeDiff = now - this.lastTouchTime;
        const coordDiff = Math.abs(x - this.lastTouchCoords.x) + Math.abs(y - this.lastTouchCoords.y);

        if (timeDiff < this.touchDebounceDelay && coordDiff < 10) {
          // 相同位置的重复点击，忽略
          return;
        }

        // 更新防抖记录
        this.lastTouchTime = now;
        this.lastTouchCoords = { x, y };

        console.log('排位赛UI触摸事件:', x, y);
        this.handleClick(x, y);
      };

      // 设置当前实例为活跃实例
      window.activeRankedUIInstance = this.instanceId;
      wx.onTouchStart(this.rankedTouchHandler);
      console.log('设置活跃排位赛UI实例:', this.instanceId);
    }
  }

  // 清理主界面事件
  cleanupMainEvents() {
    if (typeof wx !== 'undefined') {
      // 清除当前实例的活跃状态
      if (window.activeRankedUIInstance === this.instanceId) {
        window.activeRankedUIInstance = null;
        console.log('清除活跃排位赛UI实例:', this.instanceId);
      }

      this.rankedTouchHandler = null;
      console.log('清理排位赛UI主界面事件监听器');
    }
  }

  // 处理点击事件
  handleClick(x, y) {
    if (!this.buttons) return;

    console.log('排位赛UI点击坐标:', x, y);
    console.log('可用按钮:', Object.keys(this.buttons));

    for (const [buttonId, button] of Object.entries(this.buttons)) {
      if (this.isPointInButton(x, y, button)) {
        console.log('点击按钮:', buttonId);
        this.handleButtonClick(buttonId);
        break;
      }
    }
  }

  // 检查点是否在按钮内
  isPointInButton(x, y, button) {
    return x >= button.x && x <= button.x + button.width &&
           y >= button.y && y <= button.y + button.height;
  }

  // 处理按钮点击
  async handleButtonClick(buttonId) {
    console.log('排位赛UI按钮点击:', buttonId);

    try {
      switch (buttonId) {
        case 'bgmToggle':
          console.log('切换BGM开关');
          if (window.gameApp) {
            window.gameApp.toggleBgm();
          }
          break;
        case 'startMatch':
          await this.startRankedMatch();
          break;
        case 'leaderboard':
          await this.showLeaderboard();
          break;
        case 'back':
          this.emit('back');
          break;
      }
    } catch (error) {
      console.error('按钮处理失败:', error);
      this.showError(error.message);
    }
  }

  // 开始排位匹配
  async startRankedMatch() {
    try {
      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      // 如果还没有用户排位信息，先加载
      if (!this.userRanking) {
        await this.loadUserRanking();
      }

      // 加入排位队列
      await this.multiplayerManager.joinRankedQueue();

    } catch (error) {
      console.error('开始排位匹配失败:', error);
      this.showError('开始匹配失败: ' + error.message);
    }
  }

  // 加载用户排位信息
  async loadUserRanking() {
    try {
      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      console.log('正在加载用户排位信息...');
      await this.multiplayerManager.getUserRanking();
      console.log('用户排位信息加载完成');

    } catch (error) {
      console.error('加载用户排位信息失败:', error);
      // 不阻塞界面显示，使用默认值
      this.userRanking = {
        rating: 1200,
        tier: { name: '青铜 5', icon: '🥉', color: '#CD7F32', progress: 0 },
        rank: null,
        totalGames: 0,
        wins: 0,
        losses: 0,
        winRate: 0,
        currentStreak: 0,
        highestRating: 1200
      };
      console.log('使用默认排位信息');
      throw error; // 重新抛出错误，让调用者知道加载失败
    }
  }

  // 取消排位匹配
  async cancelRankedMatch() {
    try {
      console.log('取消排位匹配...');

      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      // 离开排位队列
      await this.multiplayerManager.leaveRankedQueue();

      // 清理定时器
      if (this.queueTimer) {
        clearInterval(this.queueTimer);
        this.queueTimer = null;
      }

      if (this.animationTimer) {
        clearInterval(this.animationTimer);
        this.animationTimer = null;
      }

      // 重置队列状态
      this.queueStartTime = null;
      this.queueStatus = null;
      this.queueEventsbound = false;

      // 清理队列事件
      this.cleanupQueueEvents();

      // 返回主界面
      this.showMainScreen();

      console.log('✅ 取消匹配成功');

    } catch (error) {
      console.error('取消排位匹配失败:', error);
      this.showError('取消匹配失败: ' + error.message);
    }
  }

  // 显示排行榜
  async showLeaderboard() {
    try {
      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      console.log('正在获取排行榜数据...');
      const leaderboard = await this.multiplayerManager.getLeaderboard();
      console.log('获取到排行榜:', leaderboard);

      // 显示排行榜界面
      this.showLeaderboardScreen(leaderboard);

    } catch (error) {
      console.error('获取排行榜失败:', error);
      this.showError('获取排行榜失败: ' + error.message);
    }
  }

  // 显示排行榜界面
  showLeaderboardScreen(leaderboardData) {
    console.log('显示排行榜界面...');
    this.currentScreen = 'leaderboard';
    this.hideAllScreens();

    try {
      const canvas = this.getCanvas();
      if (!canvas) {
        throw new Error('无法获取Canvas');
      }

      const ctx = canvas.getContext('2d');
      const { width, height } = this.getLogicalSize();

      // 绘制渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#1a1a2e');
      gradient.addColorStop(0.5, '#16213e');
      gradient.addColorStop(1, '#0f1419');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);

      // 绘制装饰性元素
      this.drawDecorations(ctx, width, height);

      // 渲染BGM开关按钮
      this.renderBgmToggleButton(ctx);

      // 标题
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 32px Arial';
      ctx.textAlign = 'center';
      ctx.shadowColor = 'rgba(255, 215, 0, 0.5)';
      ctx.shadowBlur = 10;
      ctx.fillText('🏆 排行榜 🏆', width / 2, 80);

      // 清除阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;

      // 副标题
      ctx.fillStyle = 'rgba(255, 215, 0, 0.8)';
      ctx.font = '16px Arial';
      ctx.fillText('顶尖玩家榜单', width / 2, 105);

      // 绘制排行榜列表
      this.drawLeaderboardList(ctx, width, height, leaderboardData.leaderboard || []);

      // 返回按钮
      const backButton = {
        x: 20,
        y: height - 60,
        width: 100,
        height: 40,
        text: '← 返回',
        color: '#6C757D',
        glowColor: '#868E96',
        textColor: '#ffffff',
        isPrimary: false
      };

      this.drawButton(ctx, backButton);

      // 保留BGM按钮
      const bgmButton = this.buttons && this.buttons.bgmToggle ? this.buttons.bgmToggle : null;
      this.buttons = { back: backButton };
      if (bgmButton) {
        this.buttons.bgmToggle = bgmButton;
      }

      // 绑定事件
      this.bindLeaderboardEvents();

      console.log('✅ 排行榜界面显示完成');

    } catch (error) {
      console.error('❌ 显示排行榜界面失败:', error);
      this.showError('排行榜界面显示失败: ' + error.message);
    }
  }

  // 显示错误信息
  showError(message) {
    console.error('排位赛UI错误:', message);

    // 简单的错误提示
    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });
    } else {
      alert(message);
    }
  }

  updateRankingDisplay() {
    // 防抖机制，避免频繁重绘
    if (this.updateRankingTimer) {
      clearTimeout(this.updateRankingTimer);
    }

    this.updateRankingTimer = setTimeout(() => {
      if (this.currentScreen === 'main') {
        this.showMainScreen();
      }
      this.updateRankingTimer = null;
    }, 100); // 100ms防抖
  }
}

// === 排位赛UI模块结束 ===

// === 联机模块开始 ===

// 微信小游戏WebSocket联机管理器
class MultiplayerManager {
  constructor() {
    this.socket = null
    this.serverUrl = null
    this.roomId = null
    this.playerId = null
    this.playerInfo = null
    this.isHost = false
    this.connected = false
    this.gameState = null
    this.callbacks = {}
    this.reconnectTimer = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
  }

  // 初始化WebSocket连接
  async init(config = {}) {
    try {
      this.serverUrl = config.serverUrl || 'ws://localhost:3000'
      const isRankedMode = config.isRankedMode || false
      await this.getUserInfo(isRankedMode)
      await this.connect()
      return true
    } catch (error) {
      console.error('多人游戏初始化失败:', error)
      this.emit('error', { message: '初始化失败', error })
      return false
    }
  }

  // 建立WebSocket连接
  async connect() {
    return new Promise((resolve, reject) => {
      try {
        if (typeof wx !== 'undefined' && wx.connectSocket) {
          console.log('使用微信小游戏WebSocket连接...')
          this.socket = wx.connectSocket({
            url: this.serverUrl,
            protocols: ['game-protocol']
          })

          // 微信小游戏的WebSocket事件处理
          this.socket.onOpen(() => {
            console.log('微信WebSocket连接成功')
            this.connected = true
            this.reconnectAttempts = 0

            this.sendMessage({
              type: 'auth',
              data: {
                playerId: this.playerId,
                playerInfo: this.playerInfo
              }
            })

            this.emit('connected')
            resolve()
          })

          this.socket.onMessage((event) => {
            try {
              const message = JSON.parse(event.data)
              this.handleServerMessage(message)
            } catch (error) {
              console.error('解析服务器消息失败:', error)
            }
          })

          this.socket.onClose((event) => {
            console.log('微信WebSocket连接关闭:', event)
            this.connected = false
            this.emit('disconnected')

            if (this.reconnectAttempts < this.maxReconnectAttempts) {
              this.scheduleReconnect()
            }
          })

          this.socket.onError((error) => {
            console.error('微信WebSocket连接错误:', error)
            this.emit('error', { message: '连接错误', error })
            reject(error)
          })
        } else {
          console.log('使用标准WebSocket连接...')
          this.socket = new WebSocket(this.serverUrl, ['game-protocol'])

          // 标准WebSocket事件处理
          this.socket.onopen = () => {
            console.log('标准WebSocket连接成功')
            this.connected = true
            this.reconnectAttempts = 0

            this.sendMessage({
              type: 'auth',
              data: {
                playerId: this.playerId,
                playerInfo: this.playerInfo
              }
            })

            this.emit('connected')
            resolve()
          }

          this.socket.onmessage = (event) => {
            try {
              const message = JSON.parse(event.data)
              this.handleServerMessage(message)
            } catch (error) {
              console.error('解析服务器消息失败:', error)
            }
          }

          this.socket.onclose = (event) => {
            console.log('标准WebSocket连接关闭:', event)
            this.connected = false
            this.emit('disconnected')

            if (this.reconnectAttempts < this.maxReconnectAttempts) {
              this.scheduleReconnect()
            }
          }

          this.socket.onerror = (error) => {
            console.error('标准WebSocket连接错误:', error)
            this.emit('error', { message: '连接错误', error })
            reject(error)
          }
        }

      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        reject(error)
      }
    })
  }

  // 获取用户信息
  async getUserInfo(isRankedMode = false) {
    try {
      if (typeof wx !== 'undefined') {
        console.log('开始获取微信用户信息...', isRankedMode ? '(排位赛模式)' : '(普通模式)')

        try {
          const desc = isRankedMode ?
            '用于排位赛匹配' :
            '用于显示玩家信息'

          const userInfo = await new Promise((resolve, reject) => {
            wx.getUserProfile({
              desc: desc,
              success: (res) => {
                console.log('getUserProfile成功:', res)
                resolve(res)
              },
              fail: (err) => {
                console.log('getUserProfile失败:', err)
                reject(err)
              }
            })
          })

          // 获取openId（如果可用）
          let openId = null
          try {
            const loginRes = await new Promise((resolve, reject) => {
              wx.login({
                success: resolve,
                fail: reject
              })
            })
            console.log('wx.login成功:', loginRes)
            openId = loginRes.code // 注意：这里是code，不是openId，实际openId需要服务器换取
          } catch (loginError) {
            console.warn('wx.login失败:', loginError)
          }

          // 使用持久的玩家ID，而不是每次变化的code
          this.playerId = this.getPersistentPlayerId()
          this.playerInfo = {
            id: this.playerId,
            nickName: userInfo.userInfo.nickName,
            avatarUrl: userInfo.userInfo.avatarUrl,
            joinTime: Date.now()
          }

          console.log('✅ 微信用户信息获取成功:', this.playerInfo)
          console.log('📸 头像URL:', this.playerInfo.avatarUrl)
        } catch (profileError) {
          console.error('❌ 获取用户资料失败:', profileError)
          console.log('🔍 错误详情:', {
            message: profileError.message,
            errCode: profileError.errCode,
            errMsg: profileError.errMsg
          })

          // 如果是排位赛模式，用户拒绝授权就抛出错误
          if (isRankedMode) {
            console.log('排位赛模式下用户拒绝授权，抛出错误')
            throw new Error('排位赛需要用户授权才能使用')
          }

          // 普通模式下，用户拒绝授权或其他错误，使用默认信息
          console.log('普通模式下使用默认信息')
          this.playerId = this.getPersistentPlayerId()
          this.playerInfo = {
            id: this.playerId,
            nickName: '玩家' + this.playerId.slice(-4),
            avatarUrl: '',
            joinTime: Date.now()
          }
          console.log('🔄 使用默认玩家信息:', this.playerInfo)
        }
      } else {
        // 其他环境，使用默认信息
        this.playerId = this.getPersistentPlayerId()
        this.playerInfo = {
          id: this.playerId,
          nickName: '玩家' + this.playerId.slice(-4),
          avatarUrl: '',
          joinTime: Date.now()
        }
      }

      console.log('🎯 最终用户信息:', this.playerInfo)
      console.log('🔍 头像URL检查:', {
        hasAvatarUrl: !!this.playerInfo.avatarUrl,
        avatarUrlLength: this.playerInfo.avatarUrl ? this.playerInfo.avatarUrl.length : 0,
        avatarUrlPreview: this.playerInfo.avatarUrl ? this.playerInfo.avatarUrl.substring(0, 50) + '...' : '空'
      })
    } catch (error) {
      console.error('获取用户信息过程中发生错误:', error)

      // 如果是排位赛模式的授权错误，重新抛出错误
      if (isRankedMode && error.message && error.message.includes('排位赛需要用户授权')) {
        console.log('重新抛出排位赛授权错误')
        throw error
      }

      // 其他情况，确保总是有默认信息
      this.playerId = this.getPersistentPlayerId()
      this.playerInfo = {
        id: this.playerId,
        nickName: '玩家' + this.playerId.slice(-4),
        avatarUrl: '',
        joinTime: Date.now()
      }
    }
  }

  // 获取持久的玩家ID
  getPersistentPlayerId() {
    try {
      // 尝试从本地存储获取已有的玩家ID
      let persistentId = null

      if (typeof wx !== 'undefined' && wx.getStorageSync) {
        persistentId = wx.getStorageSync('azul_player_id')
      } else {
        persistentId = localStorage.getItem('azul_player_id')
      }

      if (persistentId) {
        console.log('使用已有的持久玩家ID:', persistentId)
        return persistentId
      }

      // 如果没有已有ID，生成新的持久ID
      const newId = 'player_' + Date.now() + '_' + Math.floor(Math.random() * 1000)

      // 保存到本地存储
      if (typeof wx !== 'undefined' && wx.setStorageSync) {
        wx.setStorageSync('azul_player_id', newId)
      } else {
        localStorage.setItem('azul_player_id', newId)
      }

      console.log('生成新的持久玩家ID:', newId)
      return newId
    } catch (error) {
      console.error('生成持久玩家ID失败:', error)
      // 如果本地存储失败，使用临时ID
      return 'temp_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
    }
  }

  // 生成临时玩家ID
  generatePlayerId() {
    return 'player_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
  }

  // 生成房间ID
  generateRoomId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
  }

  // 事件监听
  on(event, callback) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = []
    }
    this.callbacks[event].push(callback)
  }

  // 触发事件
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件回调执行错误:', error)
        }
      })
    }
  }

  // 发送消息到服务器
  sendMessage(message) {
    if (!this.connected || !this.socket) {
      console.warn('WebSocket未连接，无法发送消息')
      return
    }

    try {
      const messageStr = JSON.stringify(message)

      if (typeof wx !== 'undefined' && this.socket.send) {
        this.socket.send({ data: messageStr })
      } else {
        this.socket.send(messageStr)
      }

      console.log('发送消息:', message)
    } catch (error) {
      console.error('发送消息失败:', error)
      this.emit('error', { message: '发送消息失败', error })
    }
  }

  // 处理服务器消息
  handleServerMessage(message) {
    console.log('收到服务器消息:', message)

    switch (message.type) {
      case 'roomCreated':
        this.emit('roomCreated', message.data)
        break
      case 'roomJoined':
        this.emit('roomJoined', message.data)
        break
      case 'playerJoined':
        console.log('有玩家加入，触发playerJoined事件:', message.data)
        this.emit('playerJoined', message.data)
        break
      case 'playerLeft':
        console.log('有玩家离开，触发playerLeft事件:', message.data)
        this.emit('playerLeft', message.data)
        break
      case 'gameStarted':
        this.gameState = message.data.gameState
        this.emit('gameStarted', message.data)
        break
      case 'gameStateUpdate':
        this.gameState = message.data.gameState
        this.emit('gameStateUpdate', message.data)
        break
      case 'gameAction':
        this.emit('playerAction', message.data)
        break
      case 'gameEnded':
        this.emit('gameEnded', message.data)
        break
      case 'roundScoring':
        console.log('收到回合计分事件:', message.data)
        this.emit('roundScoring', message.data)
        break
      case 'roomList':
        this.emit('roomListUpdate', message.data)
        break
      case 'playerTimeout':
        this.emit('playerTimeout', message.data)
        break
      case 'remainingTime':
        this.emit('remainingTime', message.data)
        break
      case 'coldReconnect':
        console.log('收到冷启动重连信息:', message.data)
        this.emit('coldReconnect', message.data)
        break
      case 'authSuccess':
        console.log('认证成功:', message.data)
        this.emit('authSuccess', message.data)
        break
      case 'error':
        this.emit('error', message.data)
        break


      // 排位赛相关事件
      case 'queueJoined':
        this.emit('queueJoined', message.data);
        break;

      case 'queueUpdate':
        this.emit('queueUpdate', message.data);
        break;

      case 'queueLeft':
        this.emit('queueLeft', message.data);
        break;

      case 'matchFound':
        this.emit('matchFound', message.data);
        break;

      case 'rankedGameStarting':
        this.emit('rankedGameStarting', message.data);
        break;

      case 'rankedGameCompleted':
        this.emit('rankedGameCompleted', message.data);
        break;

      case 'userRanking':
        this.emit('userRanking', message.data);
        break;

      case 'leaderboard':
        this.emit('leaderboard', message.data);
        break;

      case 'userHistory':
        this.emit('userHistory', message.data);
        break;

      case 'roomClosed':
        this.handleRoomClosed(message.data);
        break;

      default:
        console.warn('未知的消息类型:', message.type)
        break
    }
  }

  // 创建房间
  async createRoom(roomConfig) {
    if (!this.connected) {
      throw new Error('未连接到服务器')
    }

    try {
      this.roomId = this.generateRoomId()
      this.isHost = true

      const message = {
        type: 'createRoom',
        data: {
          roomId: this.roomId,
          hostId: this.playerId,
          hostInfo: this.playerInfo,
          config: roomConfig
        }
      }

      this.sendMessage(message)
      console.log('发送创建房间请求:', this.roomId)
      return this.roomId
    } catch (error) {
      console.error('创建房间失败:', error)
      this.emit('error', { message: '创建房间失败', error })
      throw error
    }
  }

  // 加入房间
  async joinRoom(roomId) {
    if (!this.connected) {
      throw new Error('未连接到服务器')
    }

    try {
      this.roomId = roomId
      this.isHost = false

      const message = {
        type: 'joinRoom',
        data: {
          roomId: roomId,
          playerId: this.playerId,
          playerInfo: this.playerInfo
        }
      }

      this.sendMessage(message)
      console.log('发送加入房间请求:', roomId)
    } catch (error) {
      console.error('加入房间失败:', error)
      this.emit('error', { message: '加入房间失败: ' + error.message, error })
      throw error
    }
  }

  // 获取房间列表
  getRoomList() {
    if (!this.connected) {
      console.warn('未连接到服务器')
      return
    }

    const message = {
      type: 'getRoomList',
      data: {}
    }

    this.sendMessage(message)
  }

  // 移除自定义心跳机制，使用WebSocket自带心跳

  // 开始游戏（仅房主可调用）
  startGame(initialGameState) {
    if (!this.isHost) {
      throw new Error('只有房主可以开始游戏')
    }

    if (!this.connected || !this.roomId) {
      throw new Error('未连接到房间')
    }

    try {
      const message = {
        type: 'startGame',
        data: {
          roomId: this.roomId,
          gameState: initialGameState,
          startTime: Date.now()
        }
      }

      this.sendMessage(message)
      console.log('发送开始游戏请求')
    } catch (error) {
      console.error('开始游戏失败:', error)
      this.emit('error', { message: '开始游戏失败', error })
      throw error
    }
  }

  // 离开房间
  leaveRoom() {
    if (!this.connected || !this.roomId) {
      console.warn('未连接到房间，无法离开')
      return
    }

    try {
      const message = {
        type: 'leaveRoom',
        data: {
          roomId: this.roomId,
          playerId: this.playerId
        }
      }

      this.sendMessage(message)

      // 清理本地状态
      this.cleanup()

      console.log('发送离开房间请求')
      this.emit('roomLeft')
    } catch (error) {
      console.error('离开房间失败:', error)
      this.emit('error', { message: '离开房间失败', error })
    }
  }

  // 清理资源
  cleanup() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    this.roomId = null
    this.isHost = false
    this.gameState = null
  }

  // 发送游戏动作
  sendGameAction(actionData) {
    if (!this.connected || !this.roomId) {
      console.warn('未连接到房间，无法发送游戏动作')
      return
    }

    try {
      const message = {
        type: 'gameAction',
        data: {
          roomId: this.roomId,
          playerId: this.playerId,
          action: actionData,
          timestamp: Date.now()
        }
      }

      console.log('发送游戏动作:', actionData)
      console.log('完整消息结构:', JSON.stringify(message, null, 2))

      this.sendMessage(message)
      console.log('游戏动作发送成功')
    } catch (error) {
      console.error('发送游戏动作失败:', error)
      this.emit('error', { message: '发送游戏动作失败', error })
    }
  }

  // 断开连接
  disconnect() {
    this.cleanup()

    if (this.socket) {
      if (typeof wx !== 'undefined' && this.socket.close) {
        this.socket.close()
      } else if (this.socket.close) {
        this.socket.close()
      }
    }

    this.connected = false
    this.emit('disconnected')
  }



  // 安排重连
  scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000)
    this.reconnectAttempts++

    console.log(`${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`)

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        console.error('重连失败:', error)
      }
    }, delay)
  }

  // ==================== 排位赛相关方法 ====================

  // 加入排位队列
  async joinRankedQueue() {
    if (!this.connected) {
      throw new Error('未连接到服务器');
    }

    if (!this.playerId || !this.playerInfo) {
      throw new Error('玩家信息不完整');
    }

    const message = {
      type: 'joinRankedQueue',
      data: {
        playerId: this.playerId,
        playerInfo: this.playerInfo
      }
    };

    console.log('🚀 加入排位队列:', this.playerInfo.nickName);
    console.log('📤 发送的玩家信息:', {
      id: this.playerInfo.id,
      nickName: this.playerInfo.nickName,
      avatarUrl: this.playerInfo.avatarUrl || '空头像URL',
      hasAvatarUrl: !!this.playerInfo.avatarUrl
    });
    this.sendMessage(message);
  }

  // 离开排位队列
  async leaveRankedQueue() {
    if (!this.connected) {
      throw new Error('未连接到服务器');
    }

    const message = {
      type: 'leaveRankedQueue',
      data: {
        playerId: this.playerId
      }
    };

    console.log('离开排位队列');
    this.sendMessage(message);
  }

  // 获取用户排位信息
  async getUserRanking() {
    if (!this.connected) {
      throw new Error('未连接到服务器');
    }

    const message = {
      type: 'getUserRanking',
      data: {
        playerId: this.playerId
      }
    };

    console.log('获取用户排位信息');
    this.sendMessage(message);
  }

  // 获取排行榜
  async getLeaderboard(limit = 50, offset = 0) {
    if (!this.connected) {
      throw new Error('未连接到服务器');
    }

    return new Promise((resolve, reject) => {
      const message = {
        type: 'getLeaderboard',
        data: {
          limit: limit,
          offset: offset
        }
      };

      // 创建一个一次性的事件监听器
      const handleLeaderboard = (data) => {
        // 移除监听器
        this.off('leaderboard', handleLeaderboard);
        clearTimeout(timeoutId);
        resolve(data);
      };

      // 添加临时监听器
      this.on('leaderboard', handleLeaderboard);

      // 设置超时
      const timeoutId = setTimeout(() => {
        this.off('leaderboard', handleLeaderboard);
        reject(new Error('获取排行榜超时'));
      }, 10000);

      console.log('获取排行榜');
      this.sendMessage(message);
    });
  }

  // 移除事件监听器
  off(event, callback) {
    if (this.callbacks[event] && Array.isArray(this.callbacks[event])) {
      const index = this.callbacks[event].indexOf(callback);
      if (index !== -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  // 获取用户历史记录
  async getUserHistory(limit = 20) {
    if (!this.connected) {
      throw new Error('未连接到服务器');
    }

    const message = {
      type: 'getUserHistory',
      data: {
        playerId: this.playerId,
        limit: limit
      }
    };

    console.log('获取用户历史记录');
    this.sendMessage(message);
  }

  // 处理房间关闭
  handleRoomClosed(data) {
    console.log('收到房间关闭消息:', data)

    // 检查是否是当前房间
    if (this.roomId) {
      console.log('当前房间ID:', this.roomId)

      // 如果是当前房间被关闭，需要处理
      if (data.roomId === this.roomId || !data.roomId) {
        console.log('当前游戏房间被关闭，原因:', data.reason)

        // 触发房间关闭事件，让Game类处理
        this.emit('roomClosed', data)
      } else {
        console.log('收到其他房间的关闭消息，忽略')
      }
    } else {
      console.log('当前没有活跃房间，忽略房间关闭消息')
    }
  }
}

// 微信小游戏联机界面管理器
class MultiplayerUI {
  constructor(canvas, multiplayerManager) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.multiplayer = multiplayerManager
    this.currentScreen = 'menu'
    this.roomList = []
    this.currentRoom = null
    this.buttons = {}
    this.inputText = ''
    this.showingKeyboard = false

    this.setupEventListeners()
  }

  setupEventListeners() {
    this.multiplayer.on('roomCreated', (data) => {
      this.currentRoom = data
      this.currentScreen = 'room'
      this.render()
    })

    this.multiplayer.on('roomJoined', (data) => {
      console.log('加入房间成功:', data)
      this.currentRoom = data

      // 检查是否是重连到正在进行的游戏
      if (data.isGameStarted && data.gameState) {
        console.log('重连到正在进行的游戏')

        // 直接启动游戏界面
        this.startMultiplayerGame(data)
      } else {
        // 正常加入房间，显示房间界面
        this.currentScreen = 'room'
        this.render()
      }
    })

    this.multiplayer.on('playerJoined', (data) => {
      console.log('有玩家加入房间:', data)
      if (this.currentRoom) {
        // 更新玩家列表
        this.currentRoom.players = data.players
        this.render()

        // 显示加入提示
        if (data.player) {
          this.showPlayerJoinedNotification(data.player.nickName)
        }
      }
    })

    this.multiplayer.on('playerLeft', (data) => {
      console.log('有玩家离开房间:', data)
      if (this.currentRoom) {
        // 更新玩家列表
        this.currentRoom.players = data.players
        this.render()

        // 如果有新房主，更新房主信息
        if (data.newHostId) {
          this.currentRoom.hostId = data.newHostId
        }
      }
    })

    this.multiplayer.on('playersUpdate', (data) => {
      console.log('玩家列表更新:', data)
      if (this.currentRoom) {
        this.currentRoom.players = data.players
        this.render()
      }
    })

    this.multiplayer.on('gameStarted', (data) => {
      this.currentScreen = 'game'
      if (this.onGameStart) {
        this.onGameStart(data.gameState)
      }
    })

    this.multiplayer.on('error', (data) => {
      this.showError(data.message)
    })

    this.multiplayer.on('roomListUpdate', (data) => {
      console.log('收到房间列表更新:', data)
      this.roomList = data.rooms || []
      // 只有在加入房间界面时才重新渲染
      if (this.currentScreen === 'join') {
        console.log('更新加入房间界面')
        this.render()
      }
    })

    // 设置触摸事件处理
    this.bindTouchEvents()
  }

  // 绑定触摸事件
  bindTouchEvents() {
    console.log('MultiplayerUI绑定触摸事件')

    // 微信小游戏环境的事件处理
    if (typeof wx !== 'undefined') {
      // 先清理可能存在的旧事件处理器
      wx.onTouchStart(() => {})
      console.log('清理旧的事件处理器')

      // 短暂延迟后设置新的触摸处理器，确保清理完成
      setTimeout(() => {
        this.multiplayerTouchHandler = (e) => {
          let x, y
          const touch = e.touches && e.touches[0] ? e.touches[0] : e
          x = touch.clientX || touch.x || 0
          y = touch.clientY || touch.y || 0
          console.log('MultiplayerUI触摸事件:', x, y)
          this.handleClick(x, y)
        }

        wx.onTouchStart(this.multiplayerTouchHandler)
        console.log('MultiplayerUI事件处理器设置完成')
      }, 100)
    } else {
      // 浏览器环境的事件处理
      this.touchHandler = (e) => {
        if (e.preventDefault && typeof e.preventDefault === 'function') {
          e.preventDefault()
        }
        const touch = e.touches[0]
        const rect = this.canvas.getBoundingClientRect()
        const x = touch.clientX - rect.left
        const y = touch.clientY - rect.top
        console.log('MultiplayerUI触摸事件:', x, y)
        this.handleClick(x, y)
      }

      this.clickHandler = (e) => {
        const rect = this.canvas.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top
        console.log('MultiplayerUI点击事件:', x, y)
        this.handleClick(x, y)
      }

      this.canvas.addEventListener('touchstart', this.touchHandler)
      this.canvas.addEventListener('click', this.clickHandler)
    }
  }

  setGameStartCallback(callback) {
    this.onGameStart = callback
  }

  showMainMenu() {
    this.currentScreen = 'menu'

    // 重新绑定触摸事件，确保事件处理器正确设置
    this.bindTouchEvents()

    this.render()
  }

  handleClick(x, y) {
    // 如果已经清理，不处理任何点击事件
    if (this.isCleanedUp) {
      // 静默忽略，不输出日志避免干扰
      return
    }

    console.log('MultiplayerUI点击坐标:', x, y)
    console.log('可用按钮:', Object.keys(this.buttons))

    for (const [buttonId, button] of Object.entries(this.buttons)) {
      if (this.isPointInRect(x, y, button)) {
        console.log('点击了按钮:', buttonId, '按钮区域:', button)
        this.handleButtonClick(buttonId)
        break
      }
    }
  }

  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height
  }

  async handleButtonClick(buttonId) {
    console.log('MultiplayerUI按钮点击:', buttonId, '当前界面:', this.currentScreen)

    try {
      switch (buttonId) {
        case 'bgmToggle':
          console.log('切换BGM开关')
          if (window.gameApp) {
            window.gameApp.toggleBgm()
          }
          break
        case 'createRoom':
          console.log('切换到创建房间界面')
          this.currentScreen = 'create'
          this.render()
          break
        case 'joinRoom':
          console.log('切换到加入房间界面')
          this.currentScreen = 'join'
          this.render() // 先渲染界面
          this.loadRoomList() // 然后加载房间列表
          break
        case 'confirmCreate':
          console.log('确认创建房间')
          await this.createRoom()
          break
        case 'startGame':
          console.log('开始游戏')
          if (this.multiplayer.isHost) {
            await this.startGame()
          }
          break
        case 'leaveRoom':
          console.log('离开房间')
          await this.leaveRoom()
          break
        case 'back':
          console.log('返回上一级，当前界面:', this.currentScreen)
          this.goBack()
          break
        case 'refresh':
          console.log('刷新房间列表')
          this.loadRoomList()
          break
        default:
          if (buttonId.startsWith('room_')) {
            const roomId = buttonId.replace('room_', '')
            console.log('加入指定房间:', roomId)
            await this.joinSpecificRoom(roomId)
          } else {
            console.log('未知按钮:', buttonId)
          }
          break
      }

      console.log('按钮处理完成，当前界面:', this.currentScreen)
    } catch (error) {
      console.error('按钮处理错误:', error)
      this.showError(error.message)
    }
  }

  async createRoom() {
    const roomConfig = {
      playerCount: 4,
      aiCount: 0,
      gameMode: 'multiplayer'
    }
    await this.multiplayer.createRoom(roomConfig)
  }

  loadRoomList() {
    this.multiplayer.getRoomList()
  }

  async joinSpecificRoom(roomId) {
    await this.multiplayer.joinRoom(roomId)
  }

  async startGame() {
    const initialGameState = this.createInitialGameState()
    await this.multiplayer.startGame(initialGameState)
  }

  createInitialGameState() {
    return {
      currentPlayer: 0,
      round: 1,
      phase: 'selection',
      players: this.currentRoom.players.map((player, index) => ({
        id: player.id,
        name: player.nickName,
        index: index,
        score: 0
      }))
    }
  }

  async leaveRoom() {
    await this.multiplayer.leaveRoom()
    this.currentRoom = null
    this.currentScreen = 'menu'
    this.render()
  }

  goBack() {
    console.log('goBack调用，当前界面:', this.currentScreen)

    switch (this.currentScreen) {
      case 'create':
      case 'join':
        console.log('从', this.currentScreen, '返回到主菜单')
        this.currentScreen = 'menu'
        break
      case 'room':
        console.log('从房间界面离开房间')
        this.leaveRoom()
        return // leaveRoom会处理界面切换，不需要继续执行
      default:
        console.log('从未知界面返回到主菜单')
        this.currentScreen = 'menu'
        break
    }

    console.log('goBack完成，新界面:', this.currentScreen)
    this.render()
  }

  showError(message) {
    console.error('错误:', message)

    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      })
    } else {
      alert(message)
    }
  }

  // 显示玩家加入通知
  showPlayerJoinedNotification(playerName) {
    console.log('显示玩家加入通知:', playerName)

    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: `${playerName} 加入了房间`,
        icon: 'success',
        duration: 2000
      })
    } else {
      // 在浏览器环境中可以用其他方式显示通知
      console.log(`🎉 ${playerName} 加入了房间`)
    }
  }

  render() {
    console.log('MultiplayerUI渲染界面:', this.currentScreen)

    // 清空画布和按钮
    this.clearCanvas()
    this.renderBackground()

    // 重要：清空按钮列表，防止重复（但保留BGM按钮）
    const bgmButton = this.buttons && this.buttons.bgmToggle ? this.buttons.bgmToggle : null
    this.buttons = {}

    // 渲染BGM开关按钮
    this.renderBgmToggleButton()

    // 如果之前有BGM按钮，确保它被保留
    if (bgmButton) {
      this.buttons.bgmToggle = bgmButton
    }

    switch (this.currentScreen) {
      case 'menu':
        console.log('渲染主菜单')
        this.renderMainMenu()
        break
      case 'create':
        console.log('渲染创建房间界面')
        this.renderCreateRoom()
        break
      case 'join':
        console.log('渲染加入房间界面')
        this.renderJoinRoom()
        break
      case 'room':
        console.log('渲染房间界面')
        this.renderRoomScreen()
        break
      default:
        console.log('未知界面状态:', this.currentScreen, '渲染主菜单')
        this.currentScreen = 'menu'
        this.renderMainMenu()
        break
    }

    console.log('界面渲染完成，当前按钮:', Object.keys(this.buttons))
  }

  clearCanvas() {
    const width = this.canvas.width
    const height = this.canvas.height
    this.ctx.clearRect(0, 0, width, height)
  }

  renderBackground() {
    const width = this.canvas.width
    const height = this.canvas.height

    const gradient = this.ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#667eea')
    gradient.addColorStop(1, '#764ba2')
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, width, height)
  }

  // 渲染BGM开关按钮（联机页面专用）
  renderBgmToggleButton() {
    const buttonSize = 50
    const margin = 20
    const x = margin
    const y = margin

    // 按钮背景
    const bgColor = window.gameApp && window.gameApp.bgmEnabled ? '#4CAF50' : '#757575'
    this.ctx.fillStyle = bgColor
    this.ctx.fillRect(x, y, buttonSize, buttonSize)

    // 按钮边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(x, y, buttonSize, buttonSize)

    // 按钮图标
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    const icon = window.gameApp && window.gameApp.bgmEnabled ? '🎵' : '🔇'
    this.ctx.fillText(icon, x + buttonSize / 2, y + buttonSize / 2 + 8)

    // 存储按钮区域（用于点击检测）
    this.buttons['bgmToggle'] = { x, y, width: buttonSize, height: buttonSize }
  }

  renderMainMenu() {
    const width = this.canvas.width
    const height = this.canvas.height

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 32px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('🎮 花砖物语', width / 2, 100)
    this.ctx.font = '18px Arial'
    this.ctx.fillText('好友联机', width / 2, 130)

    this.renderButton('createRoom', '创建房间', width / 2 - 100, 200, 200, 60, '#4CAF50')
    this.renderButton('joinRoom', '加入房间', width / 2 - 100, 280, 200, 60, '#2196F3')
  }

  renderCreateRoom() {
    const width = this.canvas.width

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('创建房间', width / 2, 80)

    this.ctx.font = '16px Arial'
    this.ctx.fillText('创建4人联机房间', width / 2, 120)
    this.ctx.fillText('邀请好友一起游戏', width / 2, 145)

    this.renderButton('confirmCreate', '创建房间', width / 2 - 100, 200, 200, 60, '#4CAF50')
    this.renderButton('back', '返回', width / 2 - 50, 280, 100, 50, '#757575')
  }

  renderJoinRoom() {
    const width = this.canvas.width

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('加入房间', width / 2, 80)

    this.renderButton('refresh', '刷新', width - 80, 50, 60, 40, '#FF9800')

    let y = 120
    if (this.roomList.length === 0) {
      this.ctx.font = '16px Arial'
      this.ctx.fillText('暂无可用房间', width / 2, y + 50)
    } else {
      this.roomList.forEach((room, index) => {
        const roomY = y + index * 70
        this.renderRoomItem(room, roomY)
      })
    }

    this.renderButton('back', '返回', width / 2 - 50, 500, 100, 50, '#757575')
  }

  renderRoomItem(room, y) {
    const width = this.canvas.width
    const roomWidth = width - 40
    const roomHeight = 60
    const x = 20

    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.ctx.fillRect(x, y, roomWidth, roomHeight)

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '16px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.fillText(`房主: ${room.hostName}`, x + 10, y + 25)
    this.ctx.fillText(`人数: ${room.playerCount}/${room.maxPlayers}`, x + 10, y + 45)

    const joinBtnWidth = 80
    const joinBtnX = x + roomWidth - joinBtnWidth - 10
    this.renderButton(`room_${room.roomId}`, '加入', joinBtnX, y + 10, joinBtnWidth, 40, '#4CAF50')
  }

  renderRoomScreen() {
    const width = this.canvas.width

    // 标题
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('🎮 游戏房间', width / 2, 70)

    // 房间号（更显眼）
    this.ctx.fillStyle = '#FFD700' // 金色
    this.ctx.font = 'bold 20px Arial'
    this.ctx.fillText(`房间号: ${this.currentRoom.roomId}`, width / 2, 100)

    // 邀请说明
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.font = '14px Arial'
    this.ctx.fillText('告诉朋友这个房间号，让他们加入游戏', width / 2, 125)

    // 玩家列表
    let y = 160
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '18px Arial'
    this.ctx.fillText(`玩家列表 (${this.currentRoom.players.length}/4):`, width / 2, y)

    this.currentRoom.players.forEach((player, index) => {
      const playerY = y + 40 + index * 35
      this.ctx.textAlign = 'left'
      this.ctx.font = '16px Arial'
      const isHost = player.id === this.currentRoom.hostId
      const prefix = isHost ? '👑 ' : '👤 '
      const status = isHost ? ' (房主)' : ''
      this.ctx.fillText(`${prefix}${player.nickName}${status}`, 50, playerY)
    })

    // 等待更多玩家的提示
    if (this.currentRoom.players.length < 2) {
      this.ctx.fillStyle = '#FFA500' // 橙色
      this.ctx.font = '16px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('等待更多玩家加入...', width / 2, y + 40 + this.currentRoom.players.length * 35 + 20)
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'
      this.ctx.font = '14px Arial'
      this.ctx.fillText('至少需要2名玩家才能开始游戏', width / 2, y + 40 + this.currentRoom.players.length * 35 + 45)
    }

    // 按钮区域
    const buttonY = 400
    if (this.multiplayer.isHost) {
      const canStart = this.currentRoom.players.length >= 2
      const startBtnColor = canStart ? '#4CAF50' : '#757575'
      const startBtnText = canStart ? '开始游戏' : `等待玩家 (${this.currentRoom.players.length}/2)`
      this.renderButton('startGame', startBtnText, width / 2 - 100, buttonY, 200, 60, startBtnColor)
    } else {
      // 非房主显示等待信息
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
      this.ctx.font = '16px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('等待房主开始游戏...', width / 2, buttonY + 30)
    }

    this.renderButton('leaveRoom', '离开房间', width / 2 - 50, buttonY + 80, 100, 50, '#f44336')
  }

  renderButton(id, text, x, y, width, height, color) {
    console.log('渲染按钮:', id, '位置:', {x, y, width, height})

    this.ctx.fillStyle = color
    this.ctx.fillRect(x, y, width, height)

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText(text, x + width / 2, y + height / 2 + 6)

    // 清空之前的按钮（防止累积）
    if (!this.buttons) this.buttons = {}
    this.buttons[id] = { x, y, width, height }
  }

  // 清理事件监听器
  cleanup() {
    console.log('MultiplayerUI清理事件监听器')

    // 标记为已清理，防止继续处理事件
    this.isCleanedUp = true

    if (typeof wx !== 'undefined') {
      // 在微信小游戏中，清理触摸事件监听器
      console.log('清理微信小游戏触摸事件监听器')
      // 清空触摸事件处理器引用
      this.multiplayerTouchHandler = null
      console.log('已清理微信小游戏触摸事件处理器引用')
    } else {
      if (this.touchHandler) {
        this.canvas.removeEventListener('touchstart', this.touchHandler)
        this.touchHandler = null
        console.log('移除touchstart事件监听器')
      }
      if (this.clickHandler) {
        this.canvas.removeEventListener('click', this.clickHandler)
        this.clickHandler = null
        console.log('移除click事件监听器')
      }
    }

    console.log('MultiplayerUI事件监听器清理完成')
  }
}

// === 联机模块结束 ===

// 检查联机模块是否可用
function checkMultiplayerModules() {
  return true // 现在模块已经内嵌，总是可用
}

// 游戏配置
const GAME_CONFIG = {
  COLORS: ['blue', 'yellow', 'red', 'black', 'teal'],
  COLOR_MAP: {
    blue: '#3498db',
    yellow: '#f1c40f',
    red: '#e74c3c',
    black: '#2c3e50',
    teal: '#1abc9c'
  },
  // 瓷砖图片资源映射
  TILE_IMAGES: {
    blue: 'images/white_sheep.gif',    // 蓝色用白羊
    yellow: 'images/yellow_sheep.gif',  // 黄色用黄羊
    red: 'images/pink_sheep.gif',     // 红色用粉羊
    black: 'images/black_sheep.gif',   // 黑色用黑羊
    teal: 'images/gray_sheep.gif'     // 青色用灰羊
  },
  // 背景图片
  BACKGROUND_IMAGE: 'images/background.jpg',
  BOARD_IMAGE: 'images/board.png',
  FACTORY_COUNT: 5,
  TILES_PER_COLOR: 20,
  TILES_PER_FACTORY: 4
}

// 游戏应用类 - 管理配置界面和游戏界面
class GameApp {
  constructor() {
    this.currentScreen = 'menu' // 'menu', 'config', 'multiplayer', 'ranked', 'game'
    this.configScreen = null
    this.gameScreen = null
    this.multiplayerManager = null
    this.multiplayerUI = null
    this.rankedUI = null
    this.gameConfig = null
    this.sharedCanvas = null // 共享的Canvas实例
    this.isMultiplayerMode = false
    this.isRankedMode = false

    // 资源管理
    this.images = {}
    this.sounds = {}
    this.resourcesLoaded = false

    // BGM控制
    this.bgmEnabled = true // BGM开关状态
    this.currentBgm = null // 当前播放的BGM名称
    this.lastBgmToggleTime = 0 // BGM开关防抖时间戳

    // 加载动画控制
    this.loadingAnimationId = null
    this.loadingAnimationStopped = false
    this.loadingAnimationStartTime = null
    this.currentLoadingMessage = null

    // 设置全局引用，方便其他组件访问
    window.gameApp = this

    this.init()
  }

  async init() {
    console.log('初始化游戏应用...')

    // 在微信小游戏中获取Canvas的多种方式
    this.sharedCanvas = this.getSharedCanvas()

    if (this.sharedCanvas) {
      console.log('GameApp获取到Canvas成功')
      console.log('Canvas尺寸:', this.sharedCanvas.width, 'x', this.sharedCanvas.height)

      // 立即显示初始加载界面
      this.showInitialLoadingScreen()

      // 启动加载动画循环
      this.startLoadingAnimation()
    } else {
      console.error('GameApp无法获取Canvas')
    }

    // 预加载游戏资源
    await this.preloadResources()

    // 停止加载动画
    this.stopLoadingAnimation()

    // 延迟显示主菜单，等待可能的冷启动重连检查
    this.pendingColdReconnectCheck = true
    setTimeout(() => {
      if (this.pendingColdReconnectCheck) {
        console.log('冷启动重连检查超时，显示主菜单')
        this.pendingColdReconnectCheck = false
        this.showMainMenu()
      }
    }, 2000) // 等待2秒，如果没有重连消息就显示主菜单
  }

  // 预加载游戏资源
  async preloadResources() {
    console.log('🖼️ 开始预加载游戏资源...')

    try {
      // 更新加载状态
      this.updateLoadingStatus('正在加载图片资源...')

      // 预加载图片
      await this.preloadImages()

      // 更新加载状态
      this.updateLoadingStatus('正在加载音效资源...')

      // 预加载音效
      await this.preloadSounds()

      // 更新加载状态
      this.updateLoadingStatus('资源加载完成！')

      this.resourcesLoaded = true
      console.log('✅ 游戏资源预加载完成')

      // 短暂显示完成状态
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.warn('⚠️ 资源预加载失败，使用默认资源:', error)
      this.resourcesLoaded = false
      this.updateLoadingStatus('资源加载失败，使用默认资源')
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  // 更新加载状态文本
  updateLoadingStatus(message) {
    this.currentLoadingMessage = message
    console.log('加载状态:', message)
  }

  // 预加载图片资源
  async preloadImages() {
    const imagePromises = []

    // 预加载瓷砖图片
    Object.entries(GAME_CONFIG.TILE_IMAGES).forEach(([color, src]) => {
      const promise = new Promise((resolve, reject) => {
        // 根据环境创建图片对象
        const img = this.createImage()
        if (!img) {
          console.warn(`⚠️ 无法创建图片对象: ${color}`)
          resolve()
          return
        }

        img.onload = () => {
          this.images[color] = img
          console.log(`✅ 加载瓷砖图片: ${color}`)
          resolve()
        }
        img.onerror = () => {
          console.warn(`⚠️ 加载瓷砖图片失败: ${color}`)
          resolve() // 继续加载其他资源
        }
        img.src = src
      })
      imagePromises.push(promise)
    })

    // 预加载背景图片
    const backgroundPromise = new Promise((resolve) => {
      const img = this.createImage()
      if (!img) {
        console.warn('⚠️ 无法创建背景图片对象')
        resolve()
        return
      }

      img.onload = () => {
        this.images.background = img
        console.log('✅ 加载背景图片')
        resolve()
      }
      img.onerror = () => {
        console.warn('⚠️ 加载背景图片失败')
        resolve()
      }
      img.src = GAME_CONFIG.BACKGROUND_IMAGE
    })
    imagePromises.push(backgroundPromise)

    // 预加载棋盘图片
    const boardPromise = new Promise((resolve) => {
      const img = this.createImage()
      if (!img) {
        console.warn('⚠️ 无法创建棋盘图片对象')
        resolve()
        return
      }

      img.onload = () => {
        this.images.board = img
        console.log('✅ 加载棋盘图片')
        resolve()
      }
      img.onerror = () => {
        console.warn('⚠️ 加载棋盘图片失败')
        resolve()
      }
      img.src = GAME_CONFIG.BOARD_IMAGE
    })
    imagePromises.push(boardPromise)

    await Promise.all(imagePromises)
  }

  // 创建图片对象（兼容微信小游戏和浏览器）
  createImage() {
    try {
      if (typeof wx !== 'undefined' && wx.createImage) {
        // 微信小游戏环境
        return wx.createImage()
      } else if (typeof Image !== 'undefined') {
        // 浏览器环境
        return new Image()
      } else {
        console.warn('当前环境不支持图片加载')
        return null
      }
    } catch (error) {
      console.warn('创建图片对象失败:', error)
      return null
    }
  }

  // 预加载音效资源
  async preloadSounds() {
    const soundConfig = {
      bgm1: 'sounds/game_bgm1.m4a',
      bgm2: 'sounds/game_bgm2.mp3',
      tileSelect: 'sounds/tile_select.wav',
      tilePlace: 'sounds/tile_place.wav',
      scoreUpdate: 'sounds/score_update.wav',
      roundEnd: 'sounds/round_end.wav',
      gameWin: 'sounds/game_win.wav',
      buttonClick: 'sounds/button_click.m4a'
    }

    Object.entries(soundConfig).forEach(([name, src]) => {
      try {
        if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
          // 微信小游戏环境
          const audio = wx.createInnerAudioContext()
          audio.src = src
          audio.volume = 0.7
          audio.autoplay = false
          // 预加载音频
          audio.onCanplay = () => {
            console.log(`✅ 微信音效预加载完成: ${name}`)
          }
          audio.onError = (error) => {
            console.warn(`⚠️ 微信音效加载失败: ${name}`, error)
          }
          this.sounds[name] = audio
          console.log(`📱 创建微信音效: ${name}`)
        } else if (typeof Audio !== 'undefined') {
          // 浏览器环境
          const audio = new Audio(src)
          audio.volume = 0.7
          audio.preload = 'auto'
          audio.addEventListener('canplaythrough', () => {
            console.log(`✅ 浏览器音效预加载完成: ${name}`)
          })
          audio.addEventListener('error', (error) => {
            console.warn(`⚠️ 浏览器音效加载失败: ${name}`, error)
          })
          this.sounds[name] = audio
          console.log(`🌐 创建浏览器音效: ${name}`)
        } else {
          console.warn(`⚠️ 当前环境不支持音频: ${name}`)
        }
      } catch (error) {
        console.warn(`⚠️ 加载音效失败: ${name}`, error)
      }
    })
  }

  // 播放音效
  playSound(soundName, loop = false) {
    if (!this.sounds[soundName]) {
      console.warn(`音效不存在: ${soundName}`)
      return
    }

    try {
      const sound = this.sounds[soundName]
      if (typeof wx !== 'undefined' && sound.play && sound.stop) {
        // 微信小游戏环境
        sound.loop = loop
        sound.play()
        console.log(`🎵 播放微信音效: ${soundName}`)
      } else if (sound.play && typeof sound.play === 'function') {
        // 浏览器环境
        sound.loop = loop
        sound.currentTime = 0
        const playPromise = sound.play()
        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log(`🎵 播放浏览器音效: ${soundName}`)
          }).catch(error => {
            console.warn(`播放音效失败: ${soundName}`, error)
          })
        }
      }
    } catch (error) {
      console.warn(`播放音效失败: ${soundName}`, error)
    }
  }

  // 停止音效
  stopSound(soundName) {
    if (!this.sounds[soundName]) {
      return
    }

    try {
      const sound = this.sounds[soundName]
      if (typeof wx !== 'undefined' && sound.stop) {
        // 微信小游戏环境
        sound.stop()
        console.log(`🔇 停止微信音效: ${soundName}`)
      } else if (sound.pause && typeof sound.pause === 'function') {
        // 浏览器环境
        sound.pause()
        sound.currentTime = 0
        console.log(`🔇 停止浏览器音效: ${soundName}`)
      }
    } catch (error) {
      console.warn(`停止音效失败: ${soundName}`, error)
    }
  }

  // 获取共享Canvas的方法
  getSharedCanvas() {
    // 方法1: 检查全局canvas变量
    if (typeof canvas !== 'undefined' && canvas) {
      console.log('使用全局canvas变量')
      return canvas
    }

    // 方法2: 使用wx.createCanvas()
    if (typeof wx !== 'undefined' && wx.createCanvas) {
      console.log('使用wx.createCanvas()创建Canvas')
      return wx.createCanvas()
    }

    // 方法3: 查找页面中的Canvas元素
    if (typeof document !== 'undefined') {
      const canvasElement = document.querySelector('canvas')
      if (canvasElement) {
        console.log('使用页面中的Canvas元素')
        return canvasElement
      }
    }

    console.error('所有Canvas获取方法都失败了')
    return null
  }

  // 显示主菜单
  showMainMenu() {
    console.log('显示主菜单...')
    this.currentScreen = 'menu'

    // 播放背景音乐
    this.playBackgroundMusic()

    this.renderMainMenu()
    this.bindMainMenuEvents()
  }

  // 播放背景音乐
  playBackgroundMusic() {
    if (!this.bgmEnabled) {
      console.log('BGM已关闭，跳过播放')
      return
    }

    if (this.sounds.bgm1 || this.sounds.bgm2) {
      // 随机选择一个BGM
      const bgmName = Math.random() > 0.5 ? 'bgm1' : 'bgm2'
      if (this.sounds[bgmName]) {
        this.playSound(bgmName, true) // 循环播放
        this.currentBgm = bgmName
        console.log(`🎵 开始播放背景音乐: ${bgmName}`)
      }
    }
  }

  // 停止背景音乐
  stopBackgroundMusic() {
    if (this.currentBgm) {
      this.stopSound(this.currentBgm)
      console.log(`🔇 停止背景音乐: ${this.currentBgm}`)
      this.currentBgm = null
    }
  }

  // 切换BGM开关
  toggleBgm() {
    // 添加防抖机制，防止重复点击
    const now = Date.now()
    if (this.lastBgmToggleTime && (now - this.lastBgmToggleTime) < 500) {
      console.log('BGM开关防抖：忽略重复点击')
      return
    }
    this.lastBgmToggleTime = now

    this.bgmEnabled = !this.bgmEnabled
    console.log(`BGM开关: ${this.bgmEnabled ? '开启' : '关闭'}`)

    if (this.bgmEnabled) {
      // 开启BGM
      this.playBackgroundMusic()
    } else {
      // 关闭BGM
      this.stopBackgroundMusic()
    }

    // 只更新BGM按钮状态，不重新渲染整个界面
    this.updateBgmButtonState()
  }

  // 更新BGM按钮状态（不重新渲染整个界面）
  updateBgmButtonState() {
    // 对于排位赛界面，直接重新渲染BGM按钮区域
    if (this.currentScreen === 'ranked' && this.rankedUI && this.rankedUI.sharedCanvas) {
      const ctx = this.rankedUI.sharedCanvas.getContext('2d')

      // 清除BGM按钮区域
      const buttonSize = 50
      const margin = 20
      ctx.clearRect(margin - 2, margin - 2, buttonSize + 4, buttonSize + 4)

      // 重新渲染BGM按钮
      this.rankedUI.renderBgmToggleButton(ctx)

      // 如果当前在队列界面或排行榜界面，也需要更新BGM按钮
      if (this.rankedUI.currentScreen === 'queue' || this.rankedUI.currentScreen === 'leaderboard') {
        // 对于队列和排行榜界面，也只更新BGM按钮区域
        this.rankedUI.renderBgmToggleButton(ctx)
      }
    }
    // 对于其他界面，可以类似处理或者简单重新渲染
    else {
      // 对于非排位赛界面，仍然使用完整刷新（这些界面没有循环问题）
      switch (this.currentScreen) {
        case 'menu':
          this.renderMainMenu()
          break
        case 'config':
          if (this.configScreen) {
            this.configScreen.render()
          }
          break
        case 'game':
          if (this.gameScreen) {
            this.gameScreen.render()
          }
          break
        case 'multiplayer':
          if (this.multiplayerUI) {
            this.multiplayerUI.render()
          }
          break
      }
    }
  }



  // 渲染主菜单
  renderMainMenu() {
    if (!this.sharedCanvas) {
      console.error('Canvas未初始化')
      return
    }

    const ctx = this.sharedCanvas.getContext('2d')
    const width = this.sharedCanvas.width
    const height = this.sharedCanvas.height

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#667eea')
    gradient.addColorStop(1, '#764ba2')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // 渲染BGM开关按钮
    this.renderBgmToggleButton(ctx)

    // 标题
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 32px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🎮 花砖物语', width / 2, 120)

    // 单人游戏按钮
    this.renderMenuButton(ctx, 'single', '单人游戏', width / 2 - 100, 180, 200, 50, '#4CAF50')

    // 联机游戏按钮
    this.renderMenuButton(ctx, 'multiplayer', '好友联机', width / 2 - 100, 250, 200, 50, '#2196F3')

    // 排位赛按钮
    this.renderMenuButton(ctx, 'ranked', '排位赛', width / 2 - 100, 320, 200, 50, '#FF6B6B')

    // 说明文字
    ctx.font = '14px Arial'
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    ctx.fillText('单人游戏：与AI对战', width / 2, 400)
    ctx.fillText('好友联机：邀请微信好友一起游戏', width / 2, 420)
    ctx.fillText('排位赛：竞技匹配，提升段位', width / 2, 440)
  }

  // 渲染菜单按钮
  renderMenuButton(ctx, id, text, x, y, width, height, color) {
    // 按钮背景
    ctx.fillStyle = color
    ctx.fillRect(x, y, width, height)

    // 按钮边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.lineWidth = 2
    ctx.strokeRect(x, y, width, height)

    // 按钮文字
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(text, x + width / 2, y + height / 2 + 6)

    // 存储按钮区域（用于点击检测）
    if (!this.menuButtons) this.menuButtons = {}
    this.menuButtons[id] = { x, y, width, height }
  }

  // 渲染BGM开关按钮（左上角）
  renderBgmToggleButton(ctx) {
    const buttonSize = 50
    const margin = 20
    const x = margin
    const y = margin

    // 按钮背景
    const bgColor = this.bgmEnabled ? '#4CAF50' : '#757575'
    ctx.fillStyle = bgColor
    ctx.fillRect(x, y, buttonSize, buttonSize)

    // 按钮边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    ctx.lineWidth = 2
    ctx.strokeRect(x, y, buttonSize, buttonSize)

    // 按钮图标
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 24px Arial'
    ctx.textAlign = 'center'
    const icon = this.bgmEnabled ? '🎵' : '🔇'
    ctx.fillText(icon, x + buttonSize / 2, y + buttonSize / 2 + 8)

    // 存储按钮区域（用于点击检测）
    if (!this.menuButtons) this.menuButtons = {}
    this.menuButtons['bgmToggle'] = { x, y, width: buttonSize, height: buttonSize }
  }

  showConfigScreen() {
    console.log('显示配置界面')
    this.currentScreen = 'config'

    // 创建配置界面，传入共享Canvas
    this.configScreen = new GameConfigScreen((config) => {
      this.gameConfig = config
      this.showGameScreen()
    }, this.sharedCanvas)
  }

  // 绑定主菜单事件
  bindMainMenuEvents() {
    if (!this.sharedCanvas) return

    // 清理之前的事件监听器
    this.cleanupMainMenuEvents()

    // 创建事件处理函数
    this.mainMenuTouchHandler = (e) => {
      // 在微信小游戏中，e可能没有preventDefault方法
      if (e.preventDefault && typeof e.preventDefault === 'function') {
        e.preventDefault()
      }

      let x, y

      if (typeof wx !== 'undefined') {
        // 微信小游戏环境：直接使用触摸坐标
        const touch = e.touches && e.touches[0] ? e.touches[0] : e
        x = touch.clientX || touch.x || 0
        y = touch.clientY || touch.y || 0
      } else {
        // 浏览器环境
        const touch = e.touches[0]
        const rect = this.sharedCanvas.getBoundingClientRect()
        x = touch.clientX - rect.left
        y = touch.clientY - rect.top
      }

      this.handleMainMenuClick(x, y)
    }

    this.mainMenuClickHandler = (e) => {
      let x, y

      if (typeof wx !== 'undefined') {
        // 微信小游戏环境
        x = e.clientX || e.x || 0
        y = e.clientY || e.y || 0
      } else {
        // 浏览器环境
        const rect = this.sharedCanvas.getBoundingClientRect()
        x = e.clientX - rect.left
        y = e.clientY - rect.top
      }

      this.handleMainMenuClick(x, y)
    }

    // 绑定事件
    if (typeof wx !== 'undefined') {
      // 微信小游戏环境
      wx.onTouchStart(this.mainMenuTouchHandler)
    } else {
      // 浏览器环境
      this.sharedCanvas.addEventListener('touchstart', this.mainMenuTouchHandler)
      this.sharedCanvas.addEventListener('click', this.mainMenuClickHandler)
    }
  }

  // 清理主菜单事件
  cleanupMainMenuEvents() {
    if (typeof wx !== 'undefined') {
      if (this.mainMenuTouchHandler) {
        wx.offTouchStart(this.mainMenuTouchHandler)
      }
    } else {
      if (this.sharedCanvas) {
        if (this.mainMenuTouchHandler) {
          this.sharedCanvas.removeEventListener('touchstart', this.mainMenuTouchHandler)
        }
        if (this.mainMenuClickHandler) {
          this.sharedCanvas.removeEventListener('click', this.mainMenuClickHandler)
        }
      }
    }
  }

  // 处理主菜单点击
  handleMainMenuClick(x, y) {
    if (!this.menuButtons) return

    for (const [buttonId, button] of Object.entries(this.menuButtons)) {
      if (this.isPointInRect(x, y, button)) {
        this.handleMainMenuButtonClick(buttonId)
        break
      }
    }
  }

  // 检查点是否在矩形内
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height
  }

  // 处理主菜单按钮点击
  handleMainMenuButtonClick(buttonId) {
    console.log('点击按钮:', buttonId)

    switch (buttonId) {
      case 'single':
        this.startSinglePlayerGame()
        break
      case 'multiplayer':
        this.showMultiplayerMenu()
        break
      case 'ranked':
        this.showRankedMenu()
        break
      case 'bgmToggle':
        this.toggleBgm()
        break
    }
  }

  // 启动单人游戏
  startSinglePlayerGame() {
    console.log('启动单人游戏...')
    this.isMultiplayerMode = false
    this.cleanupMainMenuEvents()
    this.showConfigScreen()
  }

  // 显示联机菜单
  async showMultiplayerMenu() {
    console.log('显示联机菜单...')
    this.isMultiplayerMode = true
    this.currentScreen = 'multiplayer'

    // 清理主菜单事件
    this.cleanupMainMenuEvents()

    // 清理旧的游戏实例
    if (this.gameScreen) {
      console.log('清理旧的游戏实例')
      if (typeof this.gameScreen.cleanup === 'function') {
        this.gameScreen.cleanup()
      }
      this.gameScreen = null
    }

    // 检查联机模块是否可用
    if (!checkMultiplayerModules()) {
      this.showMultiplayerSetupGuide()
      return
    }

    // 显示加载界面
    this.showLoadingScreen('正在初始化联机功能...')

    try {
      // 初始化多人游戏管理器
      if (!this.multiplayerManager) {
        console.log('创建MultiplayerManager实例...')
        this.multiplayerManager = new MultiplayerManager()

        console.log('开始初始化联机管理器...')

        // 添加超时处理
        const initTimeout = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('初始化超时，请检查网络连接和服务器状态'))
          }, 10000) // 10秒超时
        })

        // 使用Promise.race来添加超时控制
        const initPromise = this.multiplayerManager.init({
          serverUrl: 'ws://localhost:3000'
        })

        const initSuccess = await Promise.race([initPromise, initTimeout])

        console.log('联机管理器初始化结果:', initSuccess)
        if (!initSuccess) {
          throw new Error('无法连接到游戏服务器，请确保服务器已启动')
        }

        // 监听冷启动重连事件
        this.multiplayerManager.on('coldReconnect', (data) => {
          this.handleColdReconnect(data)
        })
      } else {
        console.log('MultiplayerManager已存在，跳过初始化')
      }

      // 初始化多人游戏UI
      if (!this.multiplayerUI) {
        console.log('创建MultiplayerUI实例...')
        this.multiplayerUI = new MultiplayerUI(this.sharedCanvas, this.multiplayerManager)
        this.multiplayerUI.setGameStartCallback((gameState) => {
          this.startMultiplayerGame(gameState)
        })
      }

      // 显示联机UI
      console.log('显示联机主菜单...')
      this.multiplayerUI.showMainMenu()
    } catch (error) {
      console.error('联机功能初始化失败:', error)

      // 显示详细的错误信息
      let errorMessage = '联机功能初始化失败'
      if (error.message.includes('超时')) {
        errorMessage = '连接超时，请检查服务器是否启动（cd server && npm start）'
      } else if (error.message.includes('连接')) {
        errorMessage = '无法连接到服务器，请确保游戏服务器已启动'
      } else if (error.message.includes('授权')) {
        errorMessage = '需要用户授权才能使用联机功能'
      } else {
        errorMessage = '联机功能初始化失败: ' + error.message
      }

      this.showError(errorMessage)

      // 返回主菜单
      setTimeout(() => {
        this.showMainMenu()
      }, 3000)
    }
  }

  // 显示排位赛菜单
  async showRankedMenu() {
    console.log('显示排位赛菜单...')

    // 如果正在处理冷启动重连，延迟显示菜单
    if (this.isHandlingColdReconnect) {
      console.log('正在处理冷启动重连，延迟显示排位赛菜单')
      setTimeout(() => {
        this.showRankedMenu()
      }, 1000)
      return
    }

    // 直接继续初始化排位赛，授权检查在MultiplayerManager中进行
    this.continueRankedMenuInit()
  }

  // 继续排位赛菜单初始化（授权成功后调用）
  async continueRankedMenuInit() {
    console.log('继续排位赛菜单初始化...')

    this.isRankedMode = true
    this.currentScreen = 'ranked'

    // 清理主菜单事件
    this.cleanupMainMenuEvents()

    // 清理旧的游戏实例
    if (this.gameScreen) {
      console.log('清理旧的游戏实例')
      this.gameScreen.cleanup()
      this.gameScreen = null
    }

    // 强制清理旧的排位赛UI实例，防止事件监听器累积
    if (this.rankedUI) {
      console.log('强制清理旧的排位赛UI实例')
      this.rankedUI.cleanup()
      this.rankedUI = null
    }

    // 显示加载界面
    this.showLoadingScreen('正在初始化排位赛功能...')

    try {
      // 初始化多人游戏管理器（排位赛也需要网络连接）
      if (!this.multiplayerManager) {
        console.log('创建MultiplayerManager实例...')
        this.multiplayerManager = new MultiplayerManager()

        console.log('开始初始化联机管理器...')

        // 添加超时处理
        const initTimeout = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('连接超时'))
          }, 10000)
        })

        // 使用Promise.race来添加超时控制
        const initPromise = this.multiplayerManager.init({
          serverUrl: 'ws://localhost:3000', // 排位赛服务器端口
          isRankedMode: true // 标记为排位赛模式
        })

        const initSuccess = await Promise.race([initPromise, initTimeout])

        console.log('联机管理器初始化结果:', initSuccess)
        if (!initSuccess) {
          throw new Error('无法连接到排位赛服务器，请确保服务器已启动')
        }

        // 监听冷启动重连事件
        this.multiplayerManager.on('coldReconnect', (data) => {
          this.handleColdReconnect(data)
        })
        this.coldReconnectListenerBound = true
      } else {
        console.log('MultiplayerManager已存在，跳过初始化')

        // 确保冷启动重连监听器已设置（避免重复绑定）
        if (!this.coldReconnectListenerBound) {
          this.multiplayerManager.on('coldReconnect', (data) => {
            this.handleColdReconnect(data)
          })
          this.coldReconnectListenerBound = true
        }
      }

      // 初始化排位赛UI（每次都创建新实例，确保事件监听器干净）
      console.log('创建新的RankedUI实例...')

      // 动态加载RankedUI类
      if (typeof RankedUI === 'undefined') {
        throw new Error('RankedUI类未找到，请确保已引入utils/rankedUI.js')
      }

      // 传入sharedCanvas参数，与MultiplayerUI保持一致
      this.rankedUI = new RankedUI(this.sharedCanvas, this.multiplayerManager)
      this.rankedUI.init(this.multiplayerManager)

      // 监听排位赛事件
      this.rankedUI.on('gameStarting', (data) => {
        this.startRankedGame(data)
      })

      this.rankedUI.on('back', () => {
        this.showMainMenu()
      })

      // 显示排位赛UI
      console.log('显示排位赛主菜单...')

      // 主动加载用户排位信息
      this.rankedUI.loadUserRanking().then(() => {
        this.rankedUI.showMainScreen()
      }).catch((error) => {
        console.warn('加载用户排位信息失败，使用默认值:', error)
        this.rankedUI.showMainScreen()
      })

    } catch (error) {
      console.error('排位赛功能初始化失败:', error)

      // 特别处理授权失败的情况
      if (error.message && (
          error.message.includes('排位赛需要用户授权') ||
          error.message.includes('getUserProfile:fail auth deny') ||
          error.message.includes('用户拒绝授权') ||
          error.message.includes('初始化失败') && error.errMsg && error.errMsg.includes('auth deny')
      )) {
        console.log('检测到授权失败，显示授权要求对话框')
        this.showRankedAuthorizationRequiredDialog()
        return
      }

      // 显示详细的错误信息
      let errorMessage = '排位赛功能初始化失败'
      if (error.message.includes('超时')) {
        errorMessage = '连接超时，请检查排位赛服务器是否启动（cd server && npm start）'
      } else if (error.message.includes('连接')) {
        errorMessage = '无法连接到排位赛服务器，请确保游戏服务器已启动'
      } else if (error.message.includes('RankedUI')) {
        errorMessage = '排位赛UI组件未找到，请确保已引入utils/rankedUI.js文件'
      } else {
        errorMessage = '排位赛功能初始化失败: ' + error.message
      }

      this.showError(errorMessage)

      // 返回主菜单
      setTimeout(() => {
        this.showMainMenu()
      }, 3000)
    }
  }

  // 启动排位赛游戏
  startRankedGame(gameData) {
    console.log('启动排位赛游戏...', gameData)

    // 防重复调用
    if (this.startingRankedGame) {
      console.log('排位赛游戏正在启动中，跳过重复调用')
      return
    }
    this.startingRankedGame = true

    this.currentScreen = 'game'
    this.isRankedMode = true

    try {
      // 清理旧的游戏实例
      if (this.gameScreen) {
        console.log('清理旧的游戏实例')
        this.gameScreen.cleanup()
        this.gameScreen = null
      }

      // 清理配置界面
      if (this.configScreen) {
        this.configScreen.cleanup()
        this.configScreen = null
      }

      // 清理排位赛UI的事件监听器
      if (this.rankedUI) {
        console.log('清理排位赛UI事件监听器')
        this.rankedUI.cleanup()
        this.rankedUI = null
      }

      // 创建排位赛游戏配置
      const rankedConfig = this.createRankedGameConfig(gameData)
      console.log('排位赛游戏配置:', rankedConfig)

      // 延迟创建游戏界面，确保清理完成
      setTimeout(() => {
        console.log('开始创建排位赛游戏界面...')

        try {
          // 创建排位赛版游戏界面
          this.gameScreen = new AzulMultiplayerGame(
            rankedConfig,
            this.sharedCanvas,
            this.multiplayerManager
          )
          console.log('排位赛游戏界面创建完成')

          // 重置启动标志
          this.startingRankedGame = false

          // 验证游戏界面是否正常工作
          setTimeout(() => {
            if (this.gameScreen && this.gameScreen.ctx && !this.gameScreen.destroyed) {
              console.log('✅ 排位赛游戏界面运行正常')
            } else {
              console.error('❌ 排位赛游戏界面可能有问题')
            }
          }, 500)

        } catch (error) {
          console.error('创建排位赛游戏界面时出错：', error)
          // 重置启动标志
          this.startingRankedGame = false
          this.showError('排位赛游戏创建失败: ' + error.message)
          // 返回排位赛菜单
          setTimeout(() => {
            this.showRankedMenu()
          }, 2000)
        }
      }, 300)

    } catch (error) {
      console.error('启动排位赛游戏时出错：', error)
      this.showError('排位赛游戏启动失败')
      // 返回排位赛菜单
      setTimeout(() => {
        this.showRankedMenu()
      }, 2000)
    }
  }

  // 创建排位赛游戏配置
  createRankedGameConfig(gameData) {
    console.log('创建排位赛游戏配置，原始数据:', gameData);

    // 确保玩家信息完整
    const players = (gameData.players || []).map((player, index) => ({
      id: player.id,
      nickName: player.nickName || `玩家${index + 1}`,
      avatarUrl: player.avatarUrl || '',
      rating: player.rating || 1200,
      tier: player.tier || { name: '青铜 5', icon: '🥉', color: '#CD7F32' },
      division: player.division || 5
    }));

    console.log('处理后的玩家信息:', players);

    return {
      playerCount: 4, // 排位赛固定4人
      aiCount: 0,
      players: players,
      gameState: gameData.gameState || {},
      isMultiplayer: true,
      isRanked: true,
      matchId: gameData.matchId,
      roomId: gameData.matchId, // 使用matchId作为roomId
      gameMode: 'ranked'
    }
  }

  // 显示初始加载界面
  showInitialLoadingScreen() {
    if (!this.sharedCanvas) return

    const ctx = this.sharedCanvas.getContext('2d')
    const width = this.sharedCanvas.width
    const height = this.sharedCanvas.height

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#667eea')
    gradient.addColorStop(1, '#764ba2')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // 标题
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 32px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🎮 花砖物语', width / 2, height / 2 - 80)

    // 副标题
    ctx.font = '18px Arial'
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    ctx.fillText('正在加载游戏资源...', width / 2, height / 2 - 20)

    console.log('显示初始加载界面')
  }

  // 启动加载动画
  startLoadingAnimation() {
    if (this.loadingAnimationId) {
      this.stopLoadingAnimation()
    }

    this.loadingAnimationStartTime = Date.now()

    const animate = () => {
      if (!this.sharedCanvas || this.loadingAnimationStopped) {
        return
      }

      this.renderLoadingAnimation()
      this.loadingAnimationId = requestAnimationFrame(animate)
    }

    this.loadingAnimationId = requestAnimationFrame(animate)
    console.log('启动加载动画')
  }

  // 停止加载动画
  stopLoadingAnimation() {
    if (this.loadingAnimationId) {
      cancelAnimationFrame(this.loadingAnimationId)
      this.loadingAnimationId = null
    }
    this.loadingAnimationStopped = true
    console.log('停止加载动画')
  }

  // 渲染加载动画
  renderLoadingAnimation() {
    if (!this.sharedCanvas) return

    const ctx = this.sharedCanvas.getContext('2d')
    const width = this.sharedCanvas.width
    const height = this.sharedCanvas.height

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#667eea')
    gradient.addColorStop(1, '#764ba2')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // 标题
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 32px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🎮 花砖物语', width / 2, height / 2 - 80)

    // 动态加载状态文本
    ctx.font = '18px Arial'
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    const loadingMessage = this.currentLoadingMessage || '正在加载游戏资源...'
    ctx.fillText(loadingMessage, width / 2, height / 2 - 20)

    // 绘制旋转的加载动画
    this.drawAdvancedLoadingAnimation(ctx, width / 2, height / 2 + 40)
  }

  // 绘制高级加载动画
  drawAdvancedLoadingAnimation(ctx, centerX, centerY) {
    const time = Date.now() / 100
    const radius = 30
    const dotCount = 8

    // 外圈旋转点
    for (let i = 0; i < dotCount; i++) {
      const angle = (i * Math.PI * 2) / dotCount + time * 0.1
      const x = centerX + Math.cos(angle) * radius
      const y = centerY + Math.sin(angle) * radius

      // 基于位置的透明度和大小变化
      const alpha = (Math.sin(time * 0.1 + i * 0.8) + 1) / 2
      const size = 3 + alpha * 4

      // 主点
      ctx.fillStyle = `rgba(76, 175, 80, ${0.4 + alpha * 0.6})`
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fill()

      // 光晕效果
      ctx.fillStyle = `rgba(76, 175, 80, ${alpha * 0.3})`
      ctx.beginPath()
      ctx.arc(x, y, size + 3, 0, Math.PI * 2)
      ctx.fill()
    }

    // 内圈反向旋转点
    const innerRadius = 15
    const innerDotCount = 6
    for (let i = 0; i < innerDotCount; i++) {
      const angle = (i * Math.PI * 2) / innerDotCount - time * 0.15
      const x = centerX + Math.cos(angle) * innerRadius
      const y = centerY + Math.sin(angle) * innerRadius

      const alpha = (Math.sin(time * 0.15 + i * 1.2) + 1) / 2
      const size = 2 + alpha * 2

      ctx.fillStyle = `rgba(255, 255, 255, ${0.3 + alpha * 0.5})`
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fill()
    }

    // 中心脉动点
    const centerAlpha = (Math.sin(time * 0.2) + 1) / 2
    const centerSize = 4 + centerAlpha * 3
    ctx.fillStyle = `rgba(255, 255, 255, ${0.5 + centerAlpha * 0.5})`
    ctx.beginPath()
    ctx.arc(centerX, centerY, centerSize, 0, Math.PI * 2)
    ctx.fill()
  }

  // 显示排位赛授权要求对话框（用户拒绝授权后显示）
  showRankedAuthorizationRequiredDialog() {
    console.log('显示排位赛授权要求对话框')

    if (typeof wx !== 'undefined' && wx.showModal) {
      wx.showModal({
        title: '需要授权',
        content: '排位赛需要获取用户信息用于匹配。\n\n必须同意授权才能进入排位赛。',
        confirmText: '重新授权',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            console.log('用户选择重新授权')
            // 用户选择重新授权，再次尝试进入排位赛
            this.showRankedMenu()
          } else {
            console.log('用户选择返回主菜单')
            // 用户选择返回，显示主菜单
            this.showMainMenu()
          }
        },
        fail: (err) => {
          console.error('显示授权要求对话框失败:', err)
          // 对话框显示失败，直接返回主菜单
          this.showMainMenu()
        }
      })
    } else {
      // 非微信环境或不支持showModal，使用Toast提示
      if (typeof wx !== 'undefined' && wx.showToast) {
        wx.showToast({
          title: '排位赛需要用户授权',
          icon: 'none',
          duration: 3000
        })
      } else {
        alert('排位赛功能需要用户授权才能使用')
      }

      // 延迟返回主菜单
      setTimeout(() => {
        this.showMainMenu()
      }, 3000)
    }
  }

  // 显示加载界面（保留原有方法用于其他地方）
  showLoadingScreen(message) {
    if (!this.sharedCanvas) return

    const ctx = this.sharedCanvas.getContext('2d')
    const width = this.sharedCanvas.width
    const height = this.sharedCanvas.height

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#667eea')
    gradient.addColorStop(1, '#764ba2')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // 标题
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 24px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🎮 花砖物语', width / 2, height / 2 - 50)

    // 加载信息
    ctx.font = '16px Arial'
    ctx.fillText(message, width / 2, height / 2)

    // 加载动画（简单的点点点）
    const dotCount = Math.abs(Math.floor(Date.now() / 500) % 4)
    const dots = '.'.repeat(dotCount)
    ctx.fillText('加载中' + dots, width / 2, height / 2 + 30)
  }

  // 显示联机设置指南
  showMultiplayerSetupGuide() {
    if (!this.sharedCanvas) return

    const ctx = this.sharedCanvas.getContext('2d')
    const width = this.sharedCanvas.width
    const height = this.sharedCanvas.height

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#667eea')
    gradient.addColorStop(1, '#764ba2')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // 标题
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 24px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🔧 联机功能设置', width / 2, 80)

    // 说明文字
    ctx.font = '16px Arial'
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)'

    const instructions = [
      '联机功能需要额外的模块支持',
      '',
      '请按以下步骤设置:',
      '',
      '1. 确保服务器已启动',
      '   运行: cd server && npm start',
      '',
      '2. 在微信开发者工具中添加以下文件:',
      '   - utils/multiplayerManager.js',
      '   - utils/multiplayerUI.js',
      '',
      '3. 重新编译项目',
      '',
      '设置完成后即可使用联机功能'
    ]

    let y = 120
    instructions.forEach(line => {
      if (line === '') {
        y += 10
      } else {
        ctx.fillText(line, width / 2, y)
        y += 25
      }
    })

    // 返回按钮
    this.renderMenuButton(ctx, 'backFromGuide', '返回主菜单', width / 2 - 75, height - 100, 150, 50, '#f44336')

    // 绑定返回事件
    this.bindGuideEvents()
  }

  // 绑定设置指南事件
  bindGuideEvents() {
    if (!this.sharedCanvas) return

    // 清理之前的事件监听器
    this.cleanupGuideEvents()

    // 创建事件处理函数
    this.guideTouchHandler = (e) => {
      // 在微信小游戏中，e可能没有preventDefault方法
      if (e.preventDefault && typeof e.preventDefault === 'function') {
        e.preventDefault()
      }

      let x, y

      if (typeof wx !== 'undefined') {
        // 微信小游戏环境：直接使用触摸坐标
        const touch = e.touches && e.touches[0] ? e.touches[0] : e
        x = touch.clientX || touch.x || 0
        y = touch.clientY || touch.y || 0
      } else {
        // 浏览器环境
        const touch = e.touches[0]
        const rect = this.sharedCanvas.getBoundingClientRect()
        x = touch.clientX - rect.left
        y = touch.clientY - rect.top
      }

      this.handleGuideClick(x, y)
    }

    this.guideClickHandler = (e) => {
      let x, y

      if (typeof wx !== 'undefined') {
        // 微信小游戏环境
        x = e.clientX || e.x || 0
        y = e.clientY || e.y || 0
      } else {
        // 浏览器环境
        const rect = this.sharedCanvas.getBoundingClientRect()
        x = e.clientX - rect.left
        y = e.clientY - rect.top
      }

      this.handleGuideClick(x, y)
    }

    // 绑定事件
    if (typeof wx !== 'undefined') {
      wx.onTouchStart(this.guideTouchHandler)
    } else {
      this.sharedCanvas.addEventListener('touchstart', this.guideTouchHandler)
      this.sharedCanvas.addEventListener('click', this.guideClickHandler)
    }
  }

  // 清理设置指南事件
  cleanupGuideEvents() {
    if (typeof wx !== 'undefined') {
      if (this.guideTouchHandler) {
        wx.offTouchStart(this.guideTouchHandler)
      }
    } else {
      if (this.sharedCanvas) {
        if (this.guideTouchHandler) {
          this.sharedCanvas.removeEventListener('touchstart', this.guideTouchHandler)
        }
        if (this.guideClickHandler) {
          this.sharedCanvas.removeEventListener('click', this.guideClickHandler)
        }
      }
    }
  }

  // 处理设置指南点击
  handleGuideClick(x, y) {
    if (!this.menuButtons) return

    for (const [buttonId, button] of Object.entries(this.menuButtons)) {
      if (this.isPointInRect(x, y, button)) {
        if (buttonId === 'backFromGuide') {
          this.cleanupGuideEvents()
          this.showMainMenu()
        }
        break
      }
    }
  }

  // 处理冷启动重连
  handleColdReconnect(data) {
    console.log('🔄 handleColdReconnect 被调用:', data)
    console.log('🔄 房间ID:', data.roomId, '房间状态:', data.roomStatus)
    console.log('🔄 是否有游戏状态:', !!data.gameState, '是否游戏已开始:', data.isGameStarted)

    // 取消主菜单的延迟显示，因为我们要处理重连
    if (this.pendingColdReconnectCheck) {
      console.log('🔄 取消主菜单延迟显示，处理冷启动重连')
      this.pendingColdReconnectCheck = false
    }

    // 停止加载动画并显示重连检查状态
    this.stopLoadingAnimation()
    this.showLoadingScreen('检测到之前的游戏，准备重连...')

    // 标记正在处理重连，防止UI初始化干扰
    this.isHandlingColdReconnect = true

    const message = `检测到您之前在房间 ${data.roomId} 中，是否要重新连接？`
    console.log('🔄 准备显示重连对话框:', message)

    // 延迟一下显示对话框，确保UI初始化完成
    setTimeout(() => {
      if (typeof wx !== 'undefined' && wx.showModal) {
        console.log('🔄 显示微信重连对话框')
        wx.showModal({
          title: '重新连接',
          content: message,
          confirmText: '重连',
          cancelText: '取消',
          success: (res) => {
            console.log('🔄 用户重连选择:', res.confirm ? '重连' : '取消')
            this.isHandlingColdReconnect = false
            if (res.confirm) {
              this.performColdReconnect(data)
            } else {
              console.log('用户选择不重连')
              // 用户选择不重连，根据房间类型显示对应界面
              if (data.roomId && data.roomId.startsWith('ranked_')) {
                console.log('不重连排位赛，显示排位赛主界面')
                this.showRankedMenu()
              } else {
                console.log('不重连普通游戏，显示主菜单')
                this.showMainMenu()
              }
            }
          },
          fail: (err) => {
            console.error('🔄 显示重连对话框失败:', err)
            this.isHandlingColdReconnect = false
            // 对话框显示失败，默认不重连
            if (data.roomId && data.roomId.startsWith('ranked_')) {
              this.showRankedMenu()
            } else {
              this.showMainMenu()
            }
          }
        })
      } else {
        // 非微信环境，直接询问
        console.log('🔄 显示浏览器重连对话框')
        const shouldReconnect = confirm(message)
        this.isHandlingColdReconnect = false
        if (shouldReconnect) {
          this.performColdReconnect(data)
        } else {
          // 用户选择不重连，根据房间类型显示对应界面
          if (data.roomId && data.roomId.startsWith('ranked_')) {
            console.log('不重连排位赛，显示排位赛主界面')
            this.showRankedMenu()
          } else {
            console.log('不重连普通游戏，显示主菜单')
            this.showMainMenu()
          }
        }
      }
    }, 500) // 延迟500ms，让UI初始化完成
  }

  // 执行冷启动重连
  performColdReconnect(data) {
    console.log('执行冷启动重连:', data)

    try {
      if (data.roomStatus === 'finished') {
        // 游戏已结束，不应该重连，直接返回大厅
        console.log('重连到已结束的游戏，返回大厅')

        // 显示提示
        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: '上次游戏已结束',
            icon: 'none',
            duration: 2000
          })
        }

        // 延迟一下再返回对应界面，让用户看到提示
        setTimeout(() => {
          // 如果是排位赛，返回排位赛界面；否则返回联机大厅
          if (data.config && data.config.isRanked) {
            this.showRankedMenu()
          } else {
            this.showMultiplayerMenu()
          }
        }, 1000)

        return // 不继续执行后续逻辑
      } else if (data.isGameStarted && data.gameState && data.roomStatus === 'playing') {
        // 重连到正在进行的游戏
        console.log('重连到正在进行的游戏')

        // 创建游戏配置，包含重连的游戏状态
        const reconnectConfig = {
          playerCount: data.config.playerCount,
          aiCount: data.config.aiCount || 0,
          players: data.players,
          gameState: data.gameState,
          isMultiplayer: true,
          roomId: data.roomId,
          remainingTime: data.remainingTime,
          isRanked: data.config.isRanked || false // 添加排位赛标志
        }

        // 直接启动游戏
        this.startMultiplayerGame(reconnectConfig)

        // 显示重连成功提示
        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: '重连成功',
            icon: 'success'
          })
        }
      } else {
        // 重连到房间等待界面
        console.log('重连到房间等待界面')

        // 模拟 roomJoined 事件
        this.multiplayerUI.currentRoom = data
        this.multiplayerUI.currentScreen = 'room'
        this.multiplayerUI.render()

        // 显示重连成功提示
        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: '重连成功',
            icon: 'success'
          })
        }
      }
    } catch (error) {
      console.error('执行冷启动重连失败:', error)

      if (typeof wx !== 'undefined' && wx.showToast) {
        wx.showToast({
          title: '重连失败: ' + error.message,
          icon: 'none',
          duration: 3000
        })
      }
    }
  }

  // 启动联机游戏
  startMultiplayerGame(gameState) {
    console.log('启动联机游戏...', gameState)

    // 防重复调用
    if (this.startingMultiplayerGame) {
      console.log('联机游戏正在启动中，跳过重复调用')
      return
    }
    this.startingMultiplayerGame = true

    this.currentScreen = 'game'
    this.isMultiplayerMode = true

    try {
      // 清理旧的游戏实例
      if (this.gameScreen) {
        console.log('清理旧的游戏实例')
        if (typeof this.gameScreen.cleanup === 'function') {
          this.gameScreen.cleanup()
        }
        this.gameScreen = null
      }

      // 清理配置界面
      if (this.configScreen) {
        console.log('清理配置界面')
        if (typeof this.configScreen.cleanup === 'function') {
          this.configScreen.cleanup()
        }
        this.configScreen = null
      }

      // 清理联机UI的事件监听器
      if (this.multiplayerUI) {
        console.log('清理联机UI事件监听器')
        this.multiplayerUI.cleanup()
        // 清空MultiplayerUI引用，防止继续处理事件
        this.multiplayerUI = null
      }

      // 创建联机游戏配置
      const multiplayerConfig = this.createMultiplayerGameConfig(gameState)
      console.log('联机游戏配置:', multiplayerConfig)

      // 延迟创建游戏界面，确保清理完成
      setTimeout(() => {
        console.log('开始创建联机游戏界面...')

        try {
          // 创建联机版游戏界面
          this.gameScreen = new AzulMultiplayerGame(
            multiplayerConfig,
            this.sharedCanvas,
            this.multiplayerManager
          )
          console.log('联机游戏界面创建完成')

          // 重置启动标志
          this.startingMultiplayerGame = false

          // 验证游戏界面是否正常工作
          setTimeout(() => {
            if (this.gameScreen && this.gameScreen.ctx && !this.gameScreen.destroyed) {
              console.log('✅ 联机游戏界面运行正常')
            } else {
              console.error('❌ 联机游戏界面可能有问题')
            }
          }, 500)

        } catch (error) {
          console.error('创建联机游戏界面时出错：', error)
          // 重置启动标志
          this.startingMultiplayerGame = false
          this.showError('联机游戏创建失败: ' + error.message)
          // 返回联机菜单
          setTimeout(() => {
            this.showMultiplayerMenu()
          }, 2000)
        }
      }, 300) // 增加延迟时间，确保清理完成

    } catch (error) {
      console.error('启动联机游戏时出错：', error)
      this.showError('联机游戏启动失败')
      // 返回联机菜单
      setTimeout(() => {
        this.showMultiplayerMenu()
      }, 2000)
    }
  }

  // 创建联机游戏配置
  createMultiplayerGameConfig(gameState) {
    console.log('创建联机游戏配置，输入数据:', gameState)

    // 如果gameState本身就是一个完整的配置对象（重连时），直接返回
    if (gameState.isMultiplayer && gameState.players && gameState.gameState) {
      console.log('使用重连配置，保留roomId等属性')
      console.log('重连配置中的isRanked:', gameState.isRanked)
      return gameState
    }

    // 从gameState中提取玩家信息（正常游戏开始时）
    const players = gameState.players || []

    // 创建类似单人游戏的配置，但适配联机模式
    const config = {
      playerCount: players.length,
      aiCount: 0, // 联机模式不需要AI
      players: players,
      gameState: gameState,
      isMultiplayer: true,
      currentPlayer: gameState.currentPlayer || 0,
      round: gameState.round || 1,
      phase: gameState.phase || 'collect',
      roomId: gameState.roomId,
      isRanked: gameState.isRanked || false // 保留排位赛标志
    }

    console.log('创建的联机游戏配置:', config)
    return config
  }

  // 显示错误信息
  showError(message) {
    console.error('错误:', message)

    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      })
    } else {
      alert(message)
    }
  }

  showGameScreen() {
    console.log('显示游戏界面，配置：', this.gameConfig)
    this.currentScreen = 'game'

    try {
      // 清理配置界面的事件监听器
      if (this.configScreen && this.configScreen.canvas) {
        console.log('清理配置界面事件监听器')

        if (typeof wx !== 'undefined') {
          // 微信小游戏环境：使用offTouchStart清理
          if (this.configScreen.touchHandler) {
            wx.offTouchStart(this.configScreen.touchHandler)
            console.log('清理微信小游戏触摸事件')
          }
        } else {
          // 浏览器环境：使用removeEventListener清理
          const canvas = this.configScreen.canvas
          if (this.configScreen.touchHandler) {
            canvas.removeEventListener('touchstart', this.configScreen.touchHandler)
          }
          if (this.configScreen.clickHandler) {
            canvas.removeEventListener('click', this.configScreen.clickHandler)
          }
          console.log('清理浏览器事件监听器')
        }

        // 在微信小游戏中，不要清空画布，因为游戏界面会重用同一个Canvas
        if (typeof wx === 'undefined') {
          // 只在非微信小游戏环境中清空画布
          const ctx = canvas.getContext('2d')
          ctx.clearRect(0, 0, canvas.width, canvas.height)
        }
        console.log('配置界面清理完成')
      }

      // 销毁配置界面
      this.configScreen = null

      // 延迟创建游戏界面，确保清理完成
      setTimeout(() => {
        console.log('开始创建游戏界面...')
        console.log('传递的配置参数：', this.gameConfig)

        try {
          // 创建游戏界面，传入配置和共享Canvas
          this.gameScreen = new AzulGame(this.gameConfig, this.sharedCanvas)
          console.log('游戏界面创建完成')

          // 验证游戏界面是否正常工作
          setTimeout(() => {
            if (this.gameScreen && this.gameScreen.ctx && !this.gameScreen.destroyed) {
              console.log('✅ 游戏界面运行正常')
            } else {
              console.error('❌ 游戏界面可能有问题')
            }
          }, 500)

        } catch (error) {
          console.error('创建游戏界面时出错：', error)
          wx.showToast({
            title: '游戏创建失败: ' + error.message,
            icon: 'none',
            duration: 3000
          })
        }
      }, 100)

    } catch (error) {
      console.error('切换到游戏界面时出错：', error)
      wx.showToast({
        title: '游戏启动失败',
        icon: 'none',
        duration: 2000
      })
    }
  }
}

// 配置界面类
class GameConfigScreen {
  constructor(onStartGame, sharedCanvas = null) {
    this.canvas = null
    this.ctx = null
    this.onStartGame = onStartGame
    this.sharedCanvas = sharedCanvas // 接收共享Canvas
    this.config = {
      playerCount: 2,
      aiCount: 1,
      aiDifficulty: 'medium'
    }
    this.difficulties = [
      { value: 'easy', name: '简单', desc: 'AI会做出基础决策' },
      { value: 'medium', name: '中等', desc: 'AI会考虑策略性决策' },
      { value: 'hard', name: '困难', desc: 'AI会做出最优决策' }
    ]
    this.init()
  }

  init() {
    console.log('初始化配置界面...')

    // 获取Canvas
    this.canvas = this.getCanvas()
    if (!this.canvas) {
      console.error('无法获取Canvas')
      return
    }

    this.ctx = this.canvas.getContext('2d')
    this.setupCanvas()
    this.bindEvents()
    this.render()

    console.log('✅ 配置界面初始化完成！')
  }

  // 获取逻辑尺寸的辅助方法
  getLogicalSize() {
    return {
      width: this.canvas._logicalWidth || this.canvas.width,
      height: this.canvas._logicalHeight || this.canvas.height
    }
  }

  getCanvas() {
    // 优先使用共享Canvas
    if (this.sharedCanvas) {
      console.log('配置界面使用共享Canvas')
      return this.sharedCanvas
    }

    // 尝试多种方式获取Canvas
    // 方法1: 全局canvas变量
    if (typeof canvas !== 'undefined' && canvas) {
      console.log('配置界面使用全局canvas变量')
      return canvas
    }

    // 方法2: wx.createCanvas()
    if (typeof wx !== 'undefined' && wx.createCanvas) {
      console.log('配置界面使用wx.createCanvas()创建Canvas')
      return wx.createCanvas()
    }

    // 方法3: 查找DOM中的Canvas
    if (typeof document !== 'undefined') {
      const canvasElement = document.querySelector('canvas')
      if (canvasElement) {
        console.log('配置界面使用DOM中的Canvas')
        return canvasElement
      }
    }

    console.error('配置界面无法获取Canvas')
    return null
  }

  setupCanvas() {
    if (typeof wx !== 'undefined') {
      const systemInfo = wx.getSystemInfoSync()

      // 在微信小游戏中，直接使用系统提供的尺寸，不进行DPI缩放
      // 因为微信小游戏会自动处理DPI适配
      const logicalWidth = systemInfo.windowWidth
      const logicalHeight = systemInfo.windowHeight

      // 检查Canvas是否需要设置尺寸
      if (this.canvas.width !== logicalWidth || this.canvas.height !== logicalHeight) {
        // 直接设置逻辑尺寸
        this.canvas.width = logicalWidth
        this.canvas.height = logicalHeight

        // 存储逻辑尺寸
        this.canvas._logicalWidth = logicalWidth
        this.canvas._logicalHeight = logicalHeight
        this.canvas._dpiSetup = true

        console.log(`配置界面Canvas设置: ${logicalWidth}x${logicalHeight}`)
      } else {
        console.log(`配置界面Canvas尺寸无需调整: ${this.canvas.width}x${this.canvas.height}`)
      }

      // 设置抗锯齿
      this.ctx.imageSmoothingEnabled = true
      if (this.ctx.imageSmoothingQuality) {
        this.ctx.imageSmoothingQuality = 'high'
      }
    }
  }

  bindEvents() {
    // 微信小游戏环境使用全局事件监听器
    if (typeof wx !== 'undefined') {
      console.log('配置界面：使用微信小游戏事件绑定')

      this.touchHandler = (e) => {
        if (e.touches && e.touches.length > 0) {
          const touch = e.touches[0]
          this.handleClick(touch.clientX, touch.clientY)
        }
      }

      // 使用微信小游戏的全局事件监听器
      wx.onTouchStart(this.touchHandler)
      // 微信小游戏通常不需要click事件，触摸事件已经足够

    } else {
      console.log('配置界面：使用浏览器事件绑定')

      // 保存事件处理器的引用，以便后续清理
      this.touchHandler = (e) => {
        if (e.preventDefault && typeof e.preventDefault === 'function') {
          e.preventDefault()
        }
        const touch = e.touches[0]
        const rect = this.canvas.getBoundingClientRect()
        const x = touch.clientX - rect.left
        const y = touch.clientY - rect.top
        this.handleClick(x, y)
      }

      this.clickHandler = (e) => {
        const rect = this.canvas.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top
        this.handleClick(x, y)
      }

      // 使用浏览器的addEventListener
      this.canvas.addEventListener('touchstart', this.touchHandler)
      this.canvas.addEventListener('click', this.clickHandler)
    }
  }

  handleClick(x, y) {
    // 检测BGM开关按钮点击
    if (this.bgmToggleButton && this.isPointInRect(x, y, this.bgmToggleButton)) {
      if (window.gameApp) {
        window.gameApp.toggleBgm()
      }
      return
    }

    // 检测各种按钮点击
    if (this.isPointInRect(x, y, this.playerCountMinusBtn)) {
      this.decreasePlayerCount()
    } else if (this.isPointInRect(x, y, this.playerCountPlusBtn)) {
      this.increasePlayerCount()
    } else if (this.isPointInRect(x, y, this.aiCountMinusBtn)) {
      this.decreaseAiCount()
    } else if (this.isPointInRect(x, y, this.aiCountPlusBtn)) {
      this.increaseAiCount()
    } else if (this.isPointInRect(x, y, this.startGameBtn)) {
      this.startGame()
    } else {
      // 检测难度选择
      this.difficulties.forEach((diff, index) => {
        const diffBtn = this.difficultyBtns[index]
        if (diffBtn && this.isPointInRect(x, y, diffBtn)) {
          this.selectDifficulty(diff.value)
        }
      })
    }
  }

  isPointInRect(x, y, rect) {
    if (!rect) return false
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height
  }

  decreasePlayerCount() {
    if (this.config.playerCount > 2) {
      this.config.playerCount--
      // 确保AI数量不超过玩家数-1
      this.config.aiCount = Math.min(this.config.aiCount, this.config.playerCount - 1)
      this.render()
    }
  }

  increasePlayerCount() {
    if (this.config.playerCount < 4) {
      this.config.playerCount++
      this.render()
    }
  }

  decreaseAiCount() {
    if (this.config.aiCount > 0) {
      this.config.aiCount--
      this.render()
    }
  }

  increaseAiCount() {
    const maxAi = this.config.playerCount - 1
    if (this.config.aiCount < maxAi) {
      this.config.aiCount++
      this.render()
    }
  }

  selectDifficulty(difficulty) {
    this.config.aiDifficulty = difficulty
    this.render()
  }

  startGame() {
    // 验证配置
    // 移除了"需要至少一个AI对手"的限制，允许全人类对局

    if (this.config.aiCount >= this.config.playerCount) {
      wx.showToast({
        title: '需要至少一个人类玩家',
        icon: 'none',
        duration: 2000
      })
      return
    }

    console.log('开始游戏，配置：', this.config)

    // 调用回调函数开始游戏
    if (this.onStartGame) {
      this.onStartGame(this.config)
    }
  }

  render() {
    this.clearCanvas()
    this.renderBackground()
    this.renderBgmToggleButton()
    this.renderHeader()
    this.renderPlayerCountSection()
    this.renderAiCountSection()
    this.renderDifficultySection()
    this.renderPreview()
    this.renderStartButton()
  }

  // 渲染BGM开关按钮（配置页面专用）
  renderBgmToggleButton() {
    const buttonSize = 50
    const margin = 20
    const x = margin
    const y = margin

    // 按钮背景
    const bgColor = window.gameApp && window.gameApp.bgmEnabled ? '#4CAF50' : '#757575'
    this.ctx.fillStyle = bgColor
    this.ctx.fillRect(x, y, buttonSize, buttonSize)

    // 按钮边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(x, y, buttonSize, buttonSize)

    // 按钮图标
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    const icon = window.gameApp && window.gameApp.bgmEnabled ? '🎵' : '🔇'
    this.ctx.fillText(icon, x + buttonSize / 2, y + buttonSize / 2 + 8)

    // 存储按钮区域（用于点击检测）
    this.bgmToggleButton = { x, y, width: buttonSize, height: buttonSize }
  }

  clearCanvas() {
    const { width, height } = this.getLogicalSize()
    this.ctx.clearRect(0, 0, width, height)
  }

  renderBackground() {
    const { width, height } = this.getLogicalSize()
    const gradient = this.ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#667eea')
    gradient.addColorStop(1, '#764ba2')
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, width, height)
  }

  renderHeader() {
    const { width } = this.getLogicalSize()
    const safeAreaTop = 40

    // 标题
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 32px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('🎮 花砖物语', width / 2, 60 + safeAreaTop)

    // 副标题
    this.ctx.font = '18px Arial'
    this.ctx.fillText('游戏配置', width / 2, 90 + safeAreaTop)
  }

  renderPlayerCountSection() {
    const { width } = this.getLogicalSize()
    const y = 160  // 从180减少到160，节省20px

    // 标题
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('玩家数量', width / 2, y)

    // 计数器
    this.renderCounter(width / 2, y + 35, this.config.playerCount, '人',
                     this.config.playerCount <= 2, this.config.playerCount >= 4)

    // 存储按钮区域
    const btnSize = 50
    this.playerCountMinusBtn = {
      x: width / 2 - 120 - btnSize / 2,
      y: y + 35 - btnSize / 2,
      width: btnSize,
      height: btnSize
    }
    this.playerCountPlusBtn = {
      x: width / 2 + 120 - btnSize / 2,
      y: y + 35 - btnSize / 2,
      width: btnSize,
      height: btnSize
    }
  }

  renderAiCountSection() {
    const { width } = this.getLogicalSize()
    const y = 240  // 从300减少到240，节省60px

    // 标题
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('AI对手数量', width / 2, y)

    // 计数器
    this.renderCounter(width / 2, y + 35, this.config.aiCount, '个',
                     this.config.aiCount <= 0, this.config.aiCount >= this.config.playerCount - 1)

    // 存储按钮区域
    const btnSize = 50
    this.aiCountMinusBtn = {
      x: width / 2 - 120 - btnSize / 2,
      y: y + 35 - btnSize / 2,
      width: btnSize,
      height: btnSize
    }
    this.aiCountPlusBtn = {
      x: width / 2 + 120 - btnSize / 2,
      y: y + 35 - btnSize / 2,
      width: btnSize,
      height: btnSize
    }

    // 显示人类vs AI信息
    this.ctx.font = '14px Arial'  // 从16px减少到14px
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.fillText(`人类玩家：${this.config.playerCount - this.config.aiCount}人  AI玩家：${this.config.aiCount}个`,
                     width / 2, y + 70)  // 从y+80减少到y+70
  }

  renderDifficultySection() {
    const { width } = this.getLogicalSize()
    const y = 340  // 从450减少到340，节省110px

    // 只有当有AI玩家时才显示难度选择
    if (this.config.aiCount === 0) {
      // 显示无AI提示
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)'
      this.ctx.font = '16px Arial'  // 从18px减少到16px
      this.ctx.textAlign = 'center'
      this.ctx.fillText('全人类对局模式', width / 2, y + 20)
      this.ctx.font = '12px Arial'  // 从14px减少到12px
      this.ctx.fillText('所有玩家都由人类控制', width / 2, y + 40)  // 从y+45减少到y+40

      // 清空难度按钮数组
      this.difficultyBtns = []
      return
    }

    // 标题
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('AI难度', width / 2, y)

    // 难度选项
    this.difficultyBtns = []
    const btnWidth = 90   // 从100减少到90
    const btnHeight = 50  // 从60减少到50
    const spacing = 15    // 从20减少到15
    const totalWidth = this.difficulties.length * btnWidth + (this.difficulties.length - 1) * spacing
    const startX = (width - totalWidth) / 2

    this.difficulties.forEach((diff, index) => {
      const x = startX + index * (btnWidth + spacing)
      const isSelected = this.config.aiDifficulty === diff.value

      // 按钮背景
      this.ctx.fillStyle = isSelected ? '#ffffff' : 'rgba(255, 255, 255, 0.3)'
      this.ctx.fillRect(x, y + 20, btnWidth, btnHeight)

      // 按钮文字
      this.ctx.fillStyle = isSelected ? '#667eea' : '#ffffff'
      this.ctx.font = 'bold 14px Arial'  // 从16px减少到14px
      this.ctx.textAlign = 'center'
      this.ctx.fillText(diff.name, x + btnWidth / 2, y + 20 + btnHeight / 2 + 5)

      // 存储按钮区域
      this.difficultyBtns[index] = {
        x: x,
        y: y + 20,
        width: btnWidth,
        height: btnHeight
      }
    })

    // 显示选中难度的描述
    const selectedDiff = this.difficulties.find(d => d.value === this.config.aiDifficulty)
    if (selectedDiff) {
      this.ctx.font = '12px Arial'  // 从14px减少到12px
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(selectedDiff.desc, width / 2, y + 85)  // 从y+110减少到y+85
    }
  }

  renderCounter(centerX, centerY, value, unit, minusDisabled, plusDisabled) {
    const btnSize = 50

    // 减号按钮
    this.ctx.fillStyle = minusDisabled ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.8)'
    this.ctx.fillRect(centerX - 120 - btnSize / 2, centerY - btnSize / 2, btnSize, btnSize)
    this.ctx.fillStyle = minusDisabled ? 'rgba(255, 255, 255, 0.5)' : '#667eea'
    this.ctx.font = 'bold 30px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('-', centerX - 120, centerY + 10)

    // 数值显示
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 36px Arial'
    this.ctx.fillText(`${value}${unit}`, centerX, centerY + 12)

    // 加号按钮
    this.ctx.fillStyle = plusDisabled ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.8)'
    this.ctx.fillRect(centerX + 120 - btnSize / 2, centerY - btnSize / 2, btnSize, btnSize)
    this.ctx.fillStyle = plusDisabled ? 'rgba(255, 255, 255, 0.5)' : '#667eea'
    this.ctx.font = 'bold 30px Arial'
    this.ctx.fillText('+', centerX + 120, centerY + 10)
  }

  renderPreview() {
    const { width } = this.getLogicalSize()
    const y = 450  // 从600减少到450，节省150px

    // 预览框背景
    const boxWidth = width - 60
    const boxHeight = 100  // 从120减少到100
    const boxX = 30

    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.ctx.fillRect(boxX, y, boxWidth, boxHeight)

    // 预览标题
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'  // 从18px减少到16px
    this.ctx.textAlign = 'center'
    this.ctx.fillText('配置预览', width / 2, y + 22)

    // 预览内容
    this.ctx.font = '14px Arial'  // 从16px减少到14px
    this.ctx.textAlign = 'left'

    this.ctx.fillText(`总玩家数：${this.config.playerCount}人`, boxX + 20, y + 45)
    this.ctx.fillText(`人类玩家：${this.config.playerCount - this.config.aiCount}人`, boxX + 20, y + 65)

    if (this.config.aiCount > 0) {
      // 有AI时显示AI信息
      this.ctx.fillText(`AI对手：${this.config.aiCount}个`, boxX + 20, y + 85)

      const selectedDiff = this.difficulties.find(d => d.value === this.config.aiDifficulty)
      this.ctx.textAlign = 'right'
      this.ctx.fillText(`AI难度：${selectedDiff ? selectedDiff.name : '未知'}`, boxX + boxWidth - 20, y + 65)
    } else {
      // 无AI时显示全人类模式
      this.ctx.fillText(`游戏模式：全人类对局`, boxX + 20, y + 85)
    }
  }

  renderStartButton() {
    const { width, height } = this.getLogicalSize()
    const btnWidth = 200
    const btnHeight = 60
    const x = (width - btnWidth) / 2
    const y = height - 80  // 从height-120改为height-80，减少底部间距

    // 按钮背景
    this.ctx.fillStyle = '#ff6b6b'
    this.ctx.fillRect(x, y, btnWidth, btnHeight)

    // 按钮文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('开始游戏', x + btnWidth / 2, y + btnHeight / 2 + 8)

    // 存储按钮区域
    this.startGameBtn = {
      x: x,
      y: y,
      width: btnWidth,
      height: btnHeight
    }
  }
}

// 花砖物语游戏类
class AzulGame {
  constructor(gameConfig = null, sharedCanvas = null) {
    this.canvas = null
    this.ctx = null
    this.gameState = null
    this.selectedTiles = null
    this.selectedSource = null
    this.gameConfig = gameConfig || { playerCount: 2, aiCount: 1, aiDifficulty: 'medium' }
    this.sharedCanvas = sharedCanvas // 接收共享Canvas
    this.init()
  }

  // 初始化游戏
  init() {
    console.log('初始化花砖物语游戏...')

    try {
      // 获取Canvas
      this.canvas = this.getCanvas()
      if (!this.canvas) {
        throw new Error('无法获取Canvas')
      }
      console.log('Canvas获取成功')

      this.ctx = this.canvas.getContext('2d')
      if (!this.ctx) {
        throw new Error('无法获取Canvas 2D上下文')
      }
      console.log('Canvas 2D上下文获取成功')

      this.setupCanvas()
      console.log('Canvas设置完成')

      this.initGameState()
      console.log('游戏状态初始化完成')

      this.bindEvents()
      console.log('事件绑定完成')

      // 先渲染一次，确保画面显示
      this.render()
      console.log('首次渲染完成')

      // 然后启动游戏循环
      this.gameLoop()
      console.log('游戏循环启动')

      console.log('✅ 花砖物语游戏初始化完成！')
      wx.showToast({ title: '游戏启动成功', icon: 'success' })

    } catch (error) {
      console.error('游戏初始化失败：', error)
      wx.showToast({
        title: '游戏初始化失败: ' + error.message,
        icon: 'none',
        duration: 3000
      })

      // 在Canvas上显示错误信息 - 使用逻辑尺寸
      if (this.canvas && this.ctx) {
        const width = this.canvas._logicalWidth || this.canvas.width
        const height = this.canvas._logicalHeight || this.canvas.height
        this.ctx.fillStyle = '#ff0000'
        this.ctx.font = '20px Arial'
        this.ctx.textAlign = 'center'
        this.ctx.fillText('游戏初始化失败', width / 2, height / 2)
        this.ctx.fillText(error.message, width / 2, height / 2 + 30)
      }
    }
  }

  // 获取Canvas（微信小游戏专用）
  getCanvas() {
    // 优先使用共享Canvas
    if (this.sharedCanvas) {
      console.log('AzulGame使用共享Canvas')
      return this.sharedCanvas
    }

    // 尝试多种方式获取Canvas
    // 方法1: 全局canvas变量
    if (typeof canvas !== 'undefined' && canvas) {
      console.log('AzulGame使用全局canvas变量')
      return canvas
    }

    // 方法2: wx.createCanvas()
    if (typeof wx !== 'undefined' && wx.createCanvas) {
      console.log('AzulGame使用wx.createCanvas()创建Canvas')
      return wx.createCanvas()
    }

    // 方法3: 查找DOM中的Canvas
    if (typeof document !== 'undefined') {
      const canvasElement = document.querySelector('canvas')
      if (canvasElement) {
        console.log('AzulGame使用DOM中的Canvas')
        return canvasElement
      }
    }

    console.error('AzulGame无法获取Canvas')
    return null
  }

  // 获取逻辑尺寸的辅助方法
  getLogicalSize() {
    return {
      width: this.canvas._logicalWidth || this.canvas.width,
      height: this.canvas._logicalHeight || this.canvas.height
    }
  }

  // 设置Canvas
  setupCanvas() {
    if (typeof wx !== 'undefined') {
      const systemInfo = wx.getSystemInfoSync()

      // 在微信小游戏中，直接使用系统提供的尺寸，不进行DPI缩放
      // 因为微信小游戏会自动处理DPI适配
      const logicalWidth = systemInfo.windowWidth
      const logicalHeight = systemInfo.windowHeight

      // 检查Canvas是否需要设置尺寸
      if (this.canvas.width !== logicalWidth || this.canvas.height !== logicalHeight) {
        // 直接设置逻辑尺寸
        this.canvas.width = logicalWidth
        this.canvas.height = logicalHeight

        // 存储逻辑尺寸
        this.canvas._logicalWidth = logicalWidth
        this.canvas._logicalHeight = logicalHeight
        this.canvas._dpiSetup = true

        console.log(`游戏Canvas设置: ${logicalWidth}x${logicalHeight}`)
      } else {
        console.log(`游戏Canvas尺寸无需调整: ${this.canvas.width}x${this.canvas.height}`)
      }

      // 设置抗锯齿
      this.ctx.imageSmoothingEnabled = true
      if (this.ctx.imageSmoothingQuality) {
        this.ctx.imageSmoothingQuality = 'high'
      }
    } else {
      console.log(`非微信环境，使用默认Canvas设置`)
    }
  }

  // 初始化游戏状态
  initGameState() {
    // 使用配置参数创建玩家
    const players = []
    const humanCount = this.gameConfig.playerCount - this.gameConfig.aiCount

    // 创建人类玩家
    for (let i = 0; i < humanCount; i++) {
      players.push(this.createPlayer(`玩家${i + 1}`))
    }

    // 创建AI玩家
    for (let i = 0; i < this.gameConfig.aiCount; i++) {
      players.push(this.createPlayer(`AI${i + 1}`))
    }

    this.gameState = {
      phase: 'collect', // collect, scoring, end
      round: 1,
      currentPlayer: 0,
      playerCount: this.gameConfig.playerCount,
      aiCount: this.gameConfig.aiCount,
      aiDifficulty: this.gameConfig.aiDifficulty,
      players: players,
      factories: this.createFactories(),
      centerArea: [],
      firstPlayerTile: true,
      tileBag: this.createTileBag(),
      discardPile: [],
      // 新增：选择状态
      showingFactorySelection: false,
      showingCenterSelection: false,
      selectedFactory: null,
      selectedTiles: null,
      // 新增：计时器相关
      turnTimer: null,
      turnStartTime: null,
      turnTimeLimit: 20000, // 20秒，单位毫秒
      timeRemaining: 20000,
      showTimer: true
    }

    this.fillFactories()
    console.log('游戏状态初始化完成')

    // 调试：检查初始墙壁状态
    this.debugWallState('游戏初始化后')

    // 启动第一个玩家的计时器
    this.startTurnTimer()

    // 检查游戏开始时是否需要执行AI回合
    this.checkAndExecuteAITurn()
  }

  // 创建玩家
  createPlayer(name) {
    return {
      name: name,
      score: 0,
      patternLines: [[], [], [], [], []], // 5行准备区
      wall: this.createWall(),
      floorLine: [],
      hasFirstPlayerTile: false
    }
  }

  // 创建墙壁
  createWall() {
    const colorPattern = [
      ['blue', 'yellow', 'red', 'black', 'teal'],
      ['teal', 'blue', 'yellow', 'red', 'black'],
      ['black', 'teal', 'blue', 'yellow', 'red'],
      ['red', 'black', 'teal', 'blue', 'yellow'],
      ['yellow', 'red', 'black', 'teal', 'blue']
    ]

    return colorPattern.map(row =>
      row.map(color => ({ color, filled: false }))
    )
  }

  // 创建磁砖袋
  createTileBag() {
    const bag = []
    GAME_CONFIG.COLORS.forEach(color => {
      for (let i = 0; i < GAME_CONFIG.TILES_PER_COLOR; i++) {
        bag.push(color)
      }
    })
    return this.shuffleArray(bag)
  }

  // 创建工厂（根据玩家数量动态计算）
  createFactories() {
    // 根据lua.txt规则：工厂数量 = 1 + 玩家数量 * 2
    const factoryCount = 1 + this.gameConfig.playerCount * 2
    console.log(`创建${factoryCount}个工厂（${this.gameConfig.playerCount}人游戏）`)
    return Array(factoryCount).fill(null).map(() => [])
  }

  // 填充工厂
  fillFactories() {
    console.log(`填充工厂开始: 磁砖袋有${this.gameState.tileBag.length}个磁砖，废弃堆有${this.gameState.discardPile.length}个磁砖`)

    this.gameState.factories.forEach((factory, factoryIndex) => {
      factory.length = 0
      for (let i = 0; i < GAME_CONFIG.TILES_PER_FACTORY; i++) {
        if (this.gameState.tileBag.length > 0) {
          factory.push(this.gameState.tileBag.pop())
        } else {
          console.log(`工厂${factoryIndex + 1}填充时磁砖袋空了，尝试重新填充`)
          this.refillTileBag()
          if (this.gameState.tileBag.length > 0) {
            factory.push(this.gameState.tileBag.pop())
          } else {
            console.log(`警告: 磁砖袋和废弃堆都空了！`)
          }
        }
      }
      console.log(`工厂${factoryIndex + 1}填充完成: [${factory.join(', ')}]`)
    })

    console.log(`填充工厂完成: 磁砖袋剩余${this.gameState.tileBag.length}个磁砖`)
  }

  // 重新填充磁砖袋
  refillTileBag() {
    console.log(`重新填充磁砖袋: 废弃堆有${this.gameState.discardPile.length}个磁砖`)
    this.gameState.tileBag = [...this.gameState.discardPile]
    this.gameState.discardPile = []
    this.gameState.tileBag = this.shuffleArray(this.gameState.tileBag)
    console.log(`磁砖袋重新填充完成: ${this.gameState.tileBag.length}个磁砖`)
  }

  // 洗牌算法
  shuffleArray(array) {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  // 绑定事件
  bindEvents() {
    // 微信小游戏环境使用全局事件监听器
    if (typeof wx !== 'undefined') {
      console.log('AzulGame：使用微信小游戏事件绑定')

      this.touchHandler = (e) => {
        if (e.touches && e.touches.length > 0) {
          const touch = e.touches[0]
          this.handleClick(touch.clientX, touch.clientY)
        }
      }

      // 使用微信小游戏的全局事件监听器
      wx.onTouchStart(this.touchHandler)
      // 微信小游戏通常不需要click事件，触摸事件已经足够

    } else {
      console.log('AzulGame：使用浏览器事件绑定')

      this.touchHandler = (e) => {
        if (e.preventDefault && typeof e.preventDefault === 'function') {
          e.preventDefault()
        }
        const touch = e.touches[0]
        const rect = this.canvas.getBoundingClientRect()
        const x = touch.clientX - rect.left
        const y = touch.clientY - rect.top
        this.handleClick(x, y)
      }

      this.clickHandler = (e) => {
        const rect = this.canvas.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top
        this.handleClick(x, y)
      }

      // 使用浏览器的addEventListener
      this.canvas.addEventListener('touchstart', this.touchHandler)
      this.canvas.addEventListener('click', this.clickHandler)
    }
  }

  // 处理点击
  handleClick(x, y) {
    // 检测BGM开关按钮点击（优先处理，不受游戏状态限制）
    if (this.bgmToggleButton && this.isPointInRect(x, y, this.bgmToggleButton)) {
      if (window.gameApp) {
        window.gameApp.toggleBgm()
      }
      return
    }

    // 检测玩家头像点击（不受游戏状态限制，随时可以切换查看）
    if (this.playerAvatarAreas) {
      for (const avatarArea of this.playerAvatarAreas) {
        if (this.isPointInRect(x, y, avatarArea)) {
          console.log(`点击了玩家${avatarArea.playerIndex + 1}的头像`)
          this.currentViewingPlayer = avatarArea.playerIndex
          this.render() // 重新渲染以显示新选中的玩家
          return
        }
      }
    }

    if (this.gameState.phase !== 'collect') return

    // 检查当前玩家是否是人类玩家
    const humanCount = this.gameState.playerCount - this.gameState.aiCount
    const isCurrentPlayerHuman = this.gameState.currentPlayer < humanCount
    if (!isCurrentPlayerHuman) return // 只允许人类玩家操作

    // 如果正在显示放置选择，检测点击区域
    if (this.gameState.selectedTiles && !this.gameState.showingFactorySelection) {
      const placementHit = this.getPlacementHit(x, y)

      if (placementHit !== null) {
        // 点击了可放置的区域
        this.placeTiles(this.gameState.selectedTiles, placementHit)
        return
      } else {
        // 检查是否点击在当前玩家面板内
        const inPlayerPanel = this.isPointInCurrentPlayerPanel(x, y)
        if (inPlayerPanel) {
          // 点击了玩家面板内的非高亮区域，不做任何操作
          console.log('点击玩家面板内的非高亮区域，不取消选择')
          return
        } else {
          // 点击了玩家面板外，取消选择
          console.log('点击玩家面板外，取消磁砖选择')
          this.gameState.selectedTiles = null
          this.gameState.selectedColor = null
          return
        }
      }
    }

    const hitResult = this.hitTest(x, y)
    if (hitResult) {
      // 如果点击的是高亮框内的空白处，不做任何处理
      if (hitResult.type === 'factoryBoxInside') {
        console.log('点击工厂选择框内的空白处，不取消选择')
        return
      }
      if (hitResult.type === 'centerBoxInside') {
        console.log('点击中央区域选择框内的空白处，不取消选择')
        return
      }
      this.processHit(hitResult)
    } else {
      // 如果没有命中任何有效区域，检查是否需要取消当前选择状态
      if (this.gameState.showingFactorySelection || this.gameState.showingCenterSelection) {
        console.log('点击高亮区域外，取消选择状态')
        this.cancelSelection()
      }
    }
  }

  // 获取放置点击（新版本）
  getPlacementHit(x, y) {
    // 检查是否点击了可放置的准备区行
    if (this.placementClickAreas) {
      for (let area of this.placementClickAreas) {
        if (this.isPointInRect(x, y, area)) {
          return area.lineIndex
        }
      }
    }

    // 没有点击可放置区域
    return null
  }

  // 获取准备区行点击（旧版本，保留以防需要）
  getPatternLineHit(x, y) {
    // 检查取消按钮
    if (this.placementCancelButton &&
        this.isPointInRect(x, y, this.placementCancelButton)) {
      return 'cancel'
    }

    // 检查准备区按钮
    if (this.placementButtons) {
      for (let button of this.placementButtons) {
        if (this.isPointInRect(x, y, button)) {
          if (button.canPlace) {
            return button.lineIndex
          } else {
            // 显示不能放置的提示
            wx.showToast({
              title: '不能放置到此行',
              icon: 'none',
              duration: 1500
            })
            return null
          }
        }
      }
    }

    return null
  }

  // 碰撞检测
  hitTest(x, y) {
    // 如果正在显示工厂选择，检测颜色选择
    if (this.gameState.showingFactorySelection) {
      const colorHit = this.getFactoryColorHit(x, y)
      if (colorHit) {
        return {
          type: 'factoryColor',
          factoryIndex: this.gameState.selectedFactory,
          color: colorHit.color
        }
      }

      // 检测取消按钮
      const cancelHit = this.getCancelButtonHit(x, y)
      if (cancelHit) {
        return { type: 'cancel' }
      }

      // 检测是否点击在高亮框内
      const inHighlightBox = this.isPointInFactorySelectionBox(x, y)
      if (inHighlightBox) {
        return { type: 'factoryBoxInside' } // 点击高亮框内的空白处，不取消
      }

      return null
    }

    // 如果正在显示中央区域选择，检测颜色选择
    if (this.gameState.showingCenterSelection) {
      const colorHit = this.getCenterColorHit(x, y)
      if (colorHit) {
        return {
          type: 'centerColor',
          color: colorHit.color
        }
      }

      // 检测取消按钮
      const cancelHit = this.getCancelButtonHit(x, y)
      if (cancelHit) {
        return { type: 'cancel' }
      }

      // 检测是否点击在高亮框内
      const inHighlightBox = this.isPointInCenterSelectionBox(x, y)
      if (inHighlightBox) {
        return { type: 'centerBoxInside' } // 点击高亮框内的空白处，不取消
      }

      return null
    }

    // 如果正在显示放置选择，检测准备区行点击
    if (this.gameState.selectedTiles && this.gameState.selectedTiles.length > 0) {
      console.log('检测放置点击，selectedTiles:', this.gameState.selectedTiles.length, 'showingPlacement:', this.gameState.showingPlacement)
      const placementHit = this.getPlacementHit(x, y)
      console.log('placementHit结果:', placementHit)
      if (placementHit !== null) {
        console.log('返回placement类型，lineIndex:', placementHit)
        return {
          type: 'placement',
          lineIndex: placementHit
        }
      }
    }

    // 检测工厂点击（整个工厂）
    for (let i = 0; i < this.gameState.factories.length; i++) {
      const factory = this.gameState.factories[i]
      if (factory.length === 0) continue

      const factoryRect = this.getFactoryRect(i)
      if (this.isPointInRect(x, y, factoryRect)) {
        return {
          type: 'factory',
          factoryIndex: i
        }
      }
    }

    // 检测中央区域点击（整个区域）
    const centerRect = this.getCenterRect()
    const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea
    if (this.isPointInRect(x, y, centerRect) && Array.isArray(centerTiles) && centerTiles.length > 0) {
      return {
        type: 'center'
      }
    }

    return null
  }

  // 游戏主循环
  gameLoop() {
    if (this.destroyed) {
      console.log('游戏已销毁，停止渲染循环')
      return
    }

    try {
      this.render()
    } catch (error) {
      console.error('渲染错误：', error)
      // 显示错误信息
      if (this.ctx) {
        this.ctx.fillStyle = '#ff0000'
        this.ctx.font = '16px Arial'
        this.ctx.textAlign = 'center'
        const { width, height } = this.getLogicalSize()
        this.ctx.fillText('渲染错误: ' + error.message, width / 2, height / 2)
      }
    }

    this.animationId = requestAnimationFrame(() => this.gameLoop())
  }

  // 销毁游戏
  destroy() {
    this.destroyed = true
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    // 清理计时器
    this.clearTurnTimer()

    // 清理事件监听器
    if (typeof wx !== 'undefined') {
      // 微信小游戏环境：使用offTouchStart清理
      if (this.touchHandler) {
        wx.offTouchStart(this.touchHandler)
        console.log('清理AzulGame微信小游戏触摸事件')
      }
    } else {
      // 浏览器环境：使用removeEventListener清理
      if (this.canvas && this.touchHandler) {
        this.canvas.removeEventListener('touchstart', this.touchHandler)
      }
      if (this.canvas && this.clickHandler) {
        this.canvas.removeEventListener('click', this.clickHandler)
      }
      console.log('清理AzulGame浏览器事件监听器')
    }

    console.log('游戏已销毁')
  }

  // 渲染游戏
  render() {
    if (!this.ctx || !this.canvas) {
      console.error('Canvas或上下文不可用')
      return
    }

    // 安全检查：确保游戏状态存在
    if (!this.gameState) {
      // 如果游戏已被销毁，不打印警告日志
      if (!this.destroyed) {
        console.warn('游戏状态不存在，跳过渲染')
      }
      return
    }

    try {
      this.clearCanvas()
      this.renderBackground()
      this.renderBgmToggleButton()
      this.renderHeader()
      this.renderFactories()
      this.renderCenterArea()
      this.renderPlayers()
      this.renderUI()

      // 渲染计分动画
      if (this.gameState.scoringAnimations && this.gameState.scoringAnimations.length > 0) {
        this.renderScoringAnimations()
      }

      // 渲染自定义游戏结束对话框
      if (this.customDialog && this.customDialog.show) {
        this.renderCustomDialog()
      }

      // 渲染游戏结束返回按钮
      if (this.showReturnButton) {
        this.renderReturnButton()
      }
    } catch (error) {
      console.error('渲染过程中出错：', error)
      console.error('游戏状态:', this.gameState)
      // 显示基本错误信息 - 使用逻辑尺寸
      const width = this.canvas._logicalWidth || this.canvas.width
      const height = this.canvas._logicalHeight || this.canvas.height
      this.ctx.fillStyle = '#ff0000'
      this.ctx.font = '16px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('渲染错误', width / 2, height / 2)
      this.ctx.fillText(error.message, width / 2, height / 2 + 30)
    }
  }

  // 渲染自定义游戏结束对话框
  renderCustomDialog() {
    if (!this.customDialog || !this.customDialog.show) {
      return
    }

    const { width, height } = this.getLogicalSize()

    // 计算文字内容所需高度
    const lines = this.customDialog.message.split('\n')
    const lineHeight = 24
    const titleHeight = 60
    const buttonAreaHeight = 80
    const padding = 40
    const contentHeight = lines.length * lineHeight

    // 对话框尺寸 - 根据内容自适应高度
    const dialogWidth = Math.min(width * 0.85, 420)
    const minDialogHeight = 200
    const maxDialogHeight = height * 0.8
    const calculatedHeight = titleHeight + contentHeight + buttonAreaHeight + padding
    const dialogHeight = Math.max(minDialogHeight, Math.min(maxDialogHeight, calculatedHeight))

    const dialogX = (width - dialogWidth) / 2
    const dialogY = (height - dialogHeight) / 2

    // 绘制半透明背景遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    this.ctx.fillRect(0, 0, width, height)

    // 绘制对话框背景
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(dialogX, dialogY, dialogWidth, dialogHeight)

    // 绘制对话框边框
    this.ctx.strokeStyle = '#cccccc'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(dialogX, dialogY, dialogWidth, dialogHeight)

    // 绘制标题
    this.ctx.fillStyle = '#333333'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(this.customDialog.title, dialogX + dialogWidth / 2, dialogY + 40)

    // 绘制消息内容
    this.ctx.fillStyle = '#666666'
    this.ctx.font = '15px Arial'
    this.ctx.textAlign = 'center'

    // 处理多行文本 - 使用计算好的位置
    const startY = dialogY + titleHeight + 10
    const maxContentHeight = dialogHeight - titleHeight - buttonAreaHeight - 20

    // 如果内容太多，使用滚动显示（简化版本：只显示前面的行）
    const maxLines = Math.floor(maxContentHeight / lineHeight)
    const displayLines = lines.slice(0, maxLines)

    displayLines.forEach((line, index) => {
      // 如果行太长，进行截断
      const maxWidth = dialogWidth - 40
      let displayLine = line
      this.ctx.font = '15px Arial'

      if (this.ctx.measureText(line).width > maxWidth) {
        // 简单截断处理
        while (this.ctx.measureText(displayLine + '...').width > maxWidth && displayLine.length > 0) {
          displayLine = displayLine.slice(0, -1)
        }
        displayLine += '...'
      }

      this.ctx.fillText(displayLine, dialogX + dialogWidth / 2, startY + index * lineHeight)
    })

    // 如果有内容被截断，显示提示
    if (lines.length > maxLines) {
      this.ctx.fillStyle = '#999999'
      this.ctx.font = '12px Arial'
      this.ctx.fillText('...', dialogX + dialogWidth / 2, startY + maxLines * lineHeight)
    }

    // 绘制按钮
    const buttonWidth = 100
    const buttonHeight = 40
    const buttonSpacing = 20
    const totalButtonWidth = this.customDialog.buttons.length * buttonWidth + (this.customDialog.buttons.length - 1) * buttonSpacing
    const buttonStartX = dialogX + (dialogWidth - totalButtonWidth) / 2
    const buttonY = dialogY + dialogHeight - 60

    // 保存按钮区域用于点击检测
    this.customDialog.buttonRects = []

    this.customDialog.buttons.forEach((button, index) => {
      const buttonX = buttonStartX + index * (buttonWidth + buttonSpacing)

      // 保存按钮区域
      this.customDialog.buttonRects.push({
        x: buttonX,
        y: buttonY,
        width: buttonWidth,
        height: buttonHeight
      })

      // 绘制按钮背景
      this.ctx.fillStyle = button.color
      this.ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight)

      // 绘制按钮边框
      this.ctx.strokeStyle = '#ffffff'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight)

      // 绘制按钮文字
      this.ctx.fillStyle = '#ffffff'
      this.ctx.font = 'bold 14px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(button.text, buttonX + buttonWidth / 2, buttonY + buttonHeight / 2)
    })
  }

  // 渲染游戏结束返回按钮
  renderReturnButton() {
    const { width, height } = this.getLogicalSize()

    // 按钮尺寸和位置
    const buttonWidth = 120
    const buttonHeight = 40
    const buttonX = width - buttonWidth - 20
    const buttonY = height - buttonHeight - 20

    // 保存按钮区域用于点击检测
    this.returnButtonRect = {
      x: buttonX,
      y: buttonY,
      width: buttonWidth,
      height: buttonHeight
    }

    // 绘制按钮背景
    this.ctx.fillStyle = this.isRankedGame ? '#FF6B6B' : '#4CAF50'
    this.ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight)

    // 绘制按钮边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight)

    // 绘制按钮文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'

    const buttonText = this.isRankedGame ? '返回排位赛' : '返回大厅'
    this.ctx.fillText(buttonText, buttonX + buttonWidth / 2, buttonY + buttonHeight / 2)
  }

  // 清空画布
  clearCanvas() {
    // 使用逻辑尺寸清空画布
    const width = this.canvas._logicalWidth || this.canvas.width
    const height = this.canvas._logicalHeight || this.canvas.height
    this.ctx.clearRect(0, 0, width, height)
  }

  // 渲染背景
  renderBackground() {
    // 使用逻辑尺寸渲染背景
    const width = this.canvas._logicalWidth || this.canvas.width
    const height = this.canvas._logicalHeight || this.canvas.height

    // 尝试使用背景图片
    if (window.gameApp && window.gameApp.images && window.gameApp.images.background) {
      try {
        // 绘制背景图片，拉伸填满整个画布
        this.ctx.drawImage(window.gameApp.images.background, 0, 0, width, height)
        return
      } catch (error) {
        console.warn('绘制背景图片失败:', error)
      }
    }

    // 回退到渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#f8f9fa')
    gradient.addColorStop(1, '#e9ecef')
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, width, height)
  }

  // 渲染BGM开关按钮（游戏页面专用）
  renderBgmToggleButton() {
    const buttonSize = 50
    const margin = 20
    const x = margin
    const y = margin

    // 按钮背景
    const bgColor = window.gameApp && window.gameApp.bgmEnabled ? '#4CAF50' : '#757575'
    this.ctx.fillStyle = bgColor
    this.ctx.fillRect(x, y, buttonSize, buttonSize)

    // 按钮边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(x, y, buttonSize, buttonSize)

    // 按钮图标
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    const icon = window.gameApp && window.gameApp.bgmEnabled ? '🎵' : '🔇'
    this.ctx.fillText(icon, x + buttonSize / 2, y + buttonSize / 2 + 8)

    // 存储按钮区域（用于点击检测）
    this.bgmToggleButton = { x, y, width: buttonSize, height: buttonSize }
  }

  // 渲染头部
  renderHeader() {
    const { width } = this.getLogicalSize()
    const safeAreaTop = 40  // 安全区域偏移，避免刘海遮挡

    // 标题
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('🎮 花砖物语', width / 2, 30 + safeAreaTop)

    // 回合信息
    this.ctx.font = '16px Arial'
    this.ctx.fillText(`第${this.gameState.round}轮 - ${this.getPhaseText()}`, width / 2, 55 + safeAreaTop)

    // 当前玩家
    const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
    if (currentPlayer) {
      this.ctx.fillStyle = '#e74c3c'
      const playerName = currentPlayer.name || currentPlayer.nickName || `玩家${this.gameState.currentPlayer + 1}`
      this.ctx.fillText(`当前: ${playerName}`, width / 2, 75 + safeAreaTop)
    }
  }

  // 获取阶段文本
  getPhaseText() {
    switch (this.gameState.phase) {
      case 'collect': return '收集磁砖'
      case 'scoring': return '计分阶段'
      case 'end': return '游戏结束'
      default: return '未知阶段'
    }
  }

  // 渲染工厂
  renderFactories() {
    // 安全检查：确保游戏状态和工厂数组存在
    if (!this.gameState || !this.gameState.factories) {
      console.warn('游戏状态或工厂数组不存在，跳过工厂渲染')
      return
    }

    const factorySize = 60
    const margin = 5
    const safeAreaTop = 40  // 与头部保持一致的安全区域偏移
    const startY = 100 + safeAreaTop

    // 计算每行可以放置的工厂数量
    const { width } = this.getLogicalSize()
    const maxFactoriesPerRow = Math.floor((width - 40) / (factorySize + margin))
    const rows = Math.ceil(this.gameState.factories.length / maxFactoriesPerRow)

    // 只在工厂数量变化时输出日志，避免刷屏
    if (!this._lastFactoryLayoutInfo ||
        this._lastFactoryLayoutInfo.count !== this.gameState.factories.length ||
        this._lastFactoryLayoutInfo.maxPerRow !== maxFactoriesPerRow) {
      console.log(`工厂布局: ${this.gameState.factories.length}个工厂, 每行${maxFactoriesPerRow}个, 共${rows}行`)
      this._lastFactoryLayoutInfo = {
        count: this.gameState.factories.length,
        maxPerRow: maxFactoriesPerRow,
        rows: rows
      }
    }

    this.gameState.factories.forEach((factory, index) => {
      const row = Math.floor(index / maxFactoriesPerRow)
      const col = index % maxFactoriesPerRow
      const factoriesInThisRow = Math.min(maxFactoriesPerRow, this.gameState.factories.length - row * maxFactoriesPerRow)

      // 计算当前行的起始X位置（居中对齐）
      const rowWidth = factoriesInThisRow * (factorySize + margin) - margin
      const rowStartX = (width - rowWidth) / 2

      const x = rowStartX + col * (factorySize + margin)
      const y = startY + row * (factorySize + margin + 10)

      // 工厂背景
      this.ctx.fillStyle = '#ffffff'
      this.ctx.fillRect(x, y, factorySize, factorySize)
      this.ctx.strokeStyle = '#dee2e6'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(x, y, factorySize, factorySize)

      // 工厂编号
      this.ctx.fillStyle = '#6c757d'
      this.ctx.font = '12px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(`${index + 1}`, x + factorySize / 2, y - 5)

      // 渲染磁砖
      const factoryTiles = factory.tiles || factory; // 兼容新旧格式
      if (Array.isArray(factoryTiles)) {
        factoryTiles.forEach((tile, tileIndex) => {
          const tileSize = 20
          const tileX = x + (tileIndex % 2) * (tileSize + 2) + 10
          const tileY = y + Math.floor(tileIndex / 2) * (tileSize + 2) + 10

          this.renderTile(tile, tileX, tileY, tileSize)
        })
      }
    })
  }

  // 渲染中央区域
  renderCenterArea() {
    // 安全检查：确保游戏状态和必要属性存在
    if (!this.gameState || !this.gameState.factories || !this.gameState.centerArea) {
      console.warn('游戏状态或中央区域不存在，跳过中央区域渲染')
      return
    }

    const safeAreaTop = 40  // 与头部保持一致的安全区域偏移

    // 动态计算中央区域位置，避免与工厂重合
    const factorySize = 60
    const margin = 5
    const factoryStartY = 100 + safeAreaTop
    const { width } = this.getLogicalSize()
    const maxFactoriesPerRow = Math.floor((width - 40) / (factorySize + margin))
    const factoryRows = Math.ceil(this.gameState.factories.length / maxFactoriesPerRow)
    const factoryEndY = factoryStartY + factoryRows * (factorySize + margin + 10) - 10

    // 中央区域位置在工厂下方，留出适当间距
    const centerY = factoryEndY + 30


    // 标题
    this.ctx.fillStyle = '#495057'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('中央区域', width / 2, centerY)

    // 起始玩家磁砖 - 优化高DPI显示
    if (this.gameState.firstPlayerTile) {
      const tileSize = 32
      const tileX = width / 2 - tileSize / 2
      const tileY = centerY + 10

      // 背景
      this.ctx.fillStyle = '#ffd700'
      this.ctx.fillRect(tileX, tileY, tileSize, tileSize)

      // 边框
      this.ctx.strokeStyle = '#f39c12'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(tileX, tileY, tileSize, tileSize)

      // 文字
      this.ctx.fillStyle = '#2c3e50'
      this.ctx.font = 'bold 14px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('起始', width / 2, tileY + tileSize / 2)
    }

    // 中央磁砖
    const centerTiles = this.groupCenterTiles()
    centerTiles.forEach((group, index) => {
      const x = width / 2 - (centerTiles.length * 35) / 2 + index * 35
      const y = centerY + 50

      this.renderTile(group.color, x, y, 25)

      if (group.count > 1) {
        this.ctx.fillStyle = '#e74c3c'
        this.ctx.fillRect(x + 20, y - 5, 15, 15)
        this.ctx.fillStyle = '#ffffff'
        this.ctx.font = '10px Arial'
        this.ctx.textAlign = 'center'
        this.ctx.fillText(group.count.toString(), x + 27, y + 5)
      }
    })
  }

  // 分组中央磁砖
  groupCenterTiles() {
    // 安全检查：确保中央区域存在
    if (!this.gameState || !this.gameState.centerArea) {
      console.warn('中央区域不存在，返回空数组')
      return []
    }

    const groups = {}
    const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea

    if (Array.isArray(centerTiles)) {
      centerTiles.forEach(tile => {
        groups[tile] = (groups[tile] || 0) + 1
      })
    }

    return Object.keys(groups).map(color => ({
      color: color,
      count: groups[color]
    }))
  }

  // 渲染墙壁空槽位 - 使用棋盘图片的对应部分
  renderWallSlot(x, y, size, row, col, expectedColor) {
    let slotDrawn = false

    // 尝试使用棋盘图片
    if (window.gameApp && window.gameApp.images && window.gameApp.images.board) {
      try {
        const boardImg = window.gameApp.images.board

        // 棋盘图片是5x5的色块，计算对应的源区域
        const sourceSize = boardImg.width / 5  // 假设棋盘图片是正方形，每个色块的大小
        const sourceX = col * sourceSize
        const sourceY = row * sourceSize

        // 绘制棋盘图片的对应部分
        this.ctx.drawImage(
          boardImg,
          sourceX, sourceY, sourceSize, sourceSize,  // 源区域
          x, y, size, size  // 目标区域
        )

        slotDrawn = true
      } catch (error) {
        console.warn('绘制墙壁棋盘槽位失败:', error)
      }
    }

    // 回退到浅色默认颜色绘制
    if (!slotDrawn) {
      this.ctx.fillStyle = this.getDefaultColor(expectedColor)
      this.ctx.fillRect(x, y, size, size)
      this.ctx.strokeStyle = '#ddd'
      this.ctx.lineWidth = 1
      this.ctx.strokeRect(x, y, size, size)
    } else {
      // 如果使用了棋盘图片，添加边框以区分槽位
      this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
      this.ctx.lineWidth = 1
      this.ctx.strokeRect(x, y, size, size)
    }
  }

  // 渲染磁砖 - 支持图片和颜色
  renderTile(color, x, y, size) {
    // 尝试使用图片资源
    if (window.gameApp && window.gameApp.images && window.gameApp.images[color]) {
      try {
        // 使用图片绘制瓷砖
        this.ctx.drawImage(window.gameApp.images[color], x, y, size, size)

        // 添加边框增强视觉效果
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
        this.ctx.lineWidth = 1
        this.ctx.strokeRect(x, y, size, size)

        return
      } catch (error) {
        console.warn(`绘制瓷砖图片失败 ${color}:`, error)
        // 继续使用颜色绘制
      }
    }

    // 回退到颜色绘制
    this.ctx.fillStyle = GAME_CONFIG.COLOR_MAP[color] || '#6c757d'
    this.ctx.fillRect(x, y, size, size)

    // 添加边框增强视觉效果
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.lineWidth = 1
    this.ctx.strokeRect(x, y, size, size)

    // 添加内部高光效果增强视觉表现
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.ctx.fillRect(x + 2, y + 2, size / 3, size / 3)
  }

  // 渲染玩家
  renderPlayers() {
    // 安全检查：确保玩家数组存在
    if (!this.gameState || !this.gameState.players || !Array.isArray(this.gameState.players)) {
      console.warn('玩家数组不存在，跳过玩家渲染')
      return
    }

    // 初始化当前查看的玩家索引（如果未设置）
    if (this.currentViewingPlayer === undefined) {
      this.currentViewingPlayer = 0 // 默认显示第一个玩家
    }

    // 使用新的单面板布局
    this.renderSinglePanelLayout()
  }

  // 新的单面板布局
  renderSinglePanelLayout() {
    const { width, height } = this.getLogicalSize()
    const playerCount = this.gameState.players.length

    // 计算面板位置
    const centerRect = this.getCenterRect()
    const centerEndY = centerRect.y + centerRect.height
    const minStartY = centerEndY + 20

    // 根据玩家数量动态计算面板高度
    const avatarSize = 50
    const avatarSpacing = 10
    const topBottomPadding = 40 // 顶部和底部各20px
    const minPanelHeight = 200 // 最小面板高度
    const requiredHeightForAvatars = topBottomPadding + playerCount * (avatarSize + avatarSpacing) - avatarSpacing
    const panelHeight = Math.max(minPanelHeight, requiredHeightForAvatars)

    const defaultStartY = height - panelHeight - 50 // 底部留50px空间
    const panelY = Math.max(minStartY, defaultStartY)

    // 头像区域宽度
    const avatarAreaWidth = 80
    const panelX = 10
    const panelWidth = width - 20
    const contentX = panelX + avatarAreaWidth + 10 // 头像区域后留10px间距
    const contentWidth = panelWidth - avatarAreaWidth - 10

    // 渲染面板背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)'
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight)
    this.ctx.strokeStyle = '#ddd'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight)

    // 渲染左侧玩家头像列表
    this.renderPlayerAvatars(panelX, panelY, avatarAreaWidth, panelHeight, playerCount)

    // 渲染当前选中玩家的面板内容
    const currentPlayer = this.gameState.players[this.currentViewingPlayer]
    if (currentPlayer) {
      this.renderPlayerContent(currentPlayer, this.currentViewingPlayer, contentX, panelY, contentWidth, panelHeight)
    }
  }

  // 渲染玩家头像列表
  renderPlayerAvatars(x, y, width, height, playerCount) {
    const avatarSize = 50
    const spacing = 10
    const startY = y + 20 // 顶部留20px间距

    // 初始化头像点击区域数组
    if (!this.playerAvatarAreas) {
      this.playerAvatarAreas = []
    }
    this.playerAvatarAreas = []

    this.gameState.players.forEach((player, index) => {
      const avatarX = x + (width - avatarSize) / 2 // 居中
      const avatarY = startY + index * (avatarSize + spacing)

      // 检查是否超出面板高度
      if (avatarY + avatarSize > y + height - 20) {
        return // 跳过超出范围的头像
      }

      // 头像背景
      const isSelected = index === this.currentViewingPlayer
      const isCurrentPlayer = index === this.gameState.currentPlayer

      // 选中状态的背景
      if (isSelected) {
        this.ctx.fillStyle = '#2196F3'
        this.ctx.fillRect(avatarX - 3, avatarY - 3, avatarSize + 6, avatarSize + 6)
      }

      // 当前回合玩家的边框
      if (isCurrentPlayer) {
        this.ctx.strokeStyle = '#FF9800'
        this.ctx.lineWidth = 3
        this.ctx.strokeRect(avatarX - 2, avatarY - 2, avatarSize + 4, avatarSize + 4)
      }

      // 渲染头像（真实头像或默认圆形背景）
      this.renderPlayerAvatar(player, avatarX, avatarY, avatarSize, index)

      // 玩家分数（小字）
      this.ctx.fillStyle = '#333'
      this.ctx.font = '10px Arial'
      this.ctx.fillText(`${player.score || 0}分`, avatarX + avatarSize/2, avatarY + avatarSize + 12)

      // 存储点击区域
      this.playerAvatarAreas.push({
        playerIndex: index,
        x: avatarX,
        y: avatarY,
        width: avatarSize,
        height: avatarSize
      })
    })
  }

  // 渲染玩家头像
  renderPlayerAvatar(player, x, y, size, playerIndex) {
    const centerX = x + size/2
    const centerY = y + size/2
    const radius = size/2 - 2

    // 先渲染默认背景
    this.renderDefaultAvatar(centerX, centerY, radius, playerIndex)

    // 尝试加载和渲染真实头像
    if (player.avatarUrl) {
      // 触发头像加载（如果还没加载）
      this.loadPlayerAvatar(player.avatarUrl, playerIndex)

      // 检查头像是否已加载成功
      const avatarImg = this.playerAvatars && this.playerAvatars[playerIndex]

      if (avatarImg && avatarImg !== null && avatarImg.complete && avatarImg.naturalWidth > 0) {
        // 创建圆形裁剪区域
        this.ctx.save()
        this.ctx.beginPath()
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
        this.ctx.clip()

        // 绘制头像图片，保持比例并居中
        const imgSize = Math.min(avatarImg.naturalWidth, avatarImg.naturalHeight)
        const sx = (avatarImg.naturalWidth - imgSize) / 2
        const sy = (avatarImg.naturalHeight - imgSize) / 2

        try {
          this.ctx.drawImage(avatarImg, sx, sy, imgSize, imgSize, x, y, size, size)
        } catch (error) {
          console.error(`❌ 玩家${playerIndex + 1}头像绘制失败:`, error)
          this.ctx.restore()
          // 绘制失败，继续显示默认头像
          this.renderFallbackAvatar(centerX, centerY, radius, playerIndex)
          return
        }

        this.ctx.restore()

        // 头像边框
        this.ctx.strokeStyle = '#333'
        this.ctx.lineWidth = 2
        this.ctx.beginPath()
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
        this.ctx.stroke()

        return // 头像渲染成功，直接返回
      }
    }

    // 没有头像或头像未加载成功，显示默认头像
    this.renderFallbackAvatar(centerX, centerY, radius, playerIndex)
  }

  // 渲染备用头像（编号）
  renderFallbackAvatar(centerX, centerY, radius, playerIndex) {
    // 玩家编号
    this.ctx.fillStyle = '#fff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText((playerIndex + 1).toString(), centerX, centerY + 6)

    // 头像边框
    this.ctx.strokeStyle = '#333'
    this.ctx.lineWidth = 2
    this.ctx.beginPath()
    this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
    this.ctx.stroke()
  }

  // 渲染默认头像背景
  renderDefaultAvatar(centerX, centerY, radius, playerIndex) {
    this.ctx.fillStyle = this.getPlayerColor(playerIndex)
    this.ctx.beginPath()
    this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
    this.ctx.fill()
  }

  // 加载玩家头像
  loadPlayerAvatar(avatarUrl, playerIndex) {
    if (!avatarUrl) {
      return false
    }

    // 初始化头像缓存和失败记录
    if (!this.playerAvatars) {
      this.playerAvatars = {}
    }
    if (!this.avatarLoadAttempts) {
      this.avatarLoadAttempts = {}
    }

    // 如果已经加载过，检查加载状态
    if (this.playerAvatars[playerIndex]) {
      const img = this.playerAvatars[playerIndex]
      if (img === null) {
        return false // 之前加载失败，跳过重试
      }
      return img.complete && img.naturalWidth > 0
    }

    // 检查是否已经尝试过加载这个URL
    const attemptKey = `${playerIndex}_${avatarUrl}`
    if (this.avatarLoadAttempts[attemptKey]) {
      return false // 已经尝试过，避免重复加载
    }

    // 创建新的图片对象
    const img = this.createImage()
    if (!img) {
      console.warn(`无法创建图片对象，玩家${playerIndex + 1}`)
      this.avatarLoadAttempts[attemptKey] = true
      return false
    }

    // 标记正在尝试加载
    this.avatarLoadAttempts[attemptKey] = true

    // 只在首次加载时输出日志
    console.log(`🔄 开始加载玩家${playerIndex + 1}头像`)

    img.onload = () => {
      console.log(`✅ 玩家${playerIndex + 1}头像加载成功`)
      // 延迟重新渲染，确保图片完全加载
      setTimeout(() => {
        if (this.render && !this.destroyed) {
          this.render()
        }
      }, 50)
    }

    img.onerror = (error) => {
      console.warn(`❌ 玩家${playerIndex + 1}头像加载失败，跳过后续尝试`)
      // 标记为加载失败，避免重复尝试
      this.playerAvatars[playerIndex] = null
    }

    // 微信小游戏环境不需要设置crossOrigin
    if (typeof wx === 'undefined' && img.crossOrigin !== undefined) {
      img.crossOrigin = 'anonymous'
    }

    // 设置图片源，开始加载
    img.src = avatarUrl
    this.playerAvatars[playerIndex] = img

    return false // 首次加载返回false，等待异步加载完成
  }

  // 获取玩家颜色
  getPlayerColor(playerIndex) {
    const colors = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0']
    return colors[playerIndex % colors.length]
  }

  // 渲染玩家内容（在单面板中显示）
  renderPlayerContent(player, playerIndex, x, y, width, height) {
    // 安全检查：确保玩家对象和必要属性存在
    if (!player) {
      console.warn(`玩家${playerIndex}对象不存在，跳过渲染`)
      return
    }

    // 确保玩家对象有必要的属性
    if (!player.patternLines) {
      console.warn(`玩家${playerIndex}缺少patternLines，初始化为空数组`)
      player.patternLines = [[], [], [], [], []]
    }
    if (!player.wall) {
      console.warn(`玩家${playerIndex}缺少wall，初始化为空墙壁`)
      player.wall = this.createPlayerWall()
    }
    if (!player.floorLine) {
      console.warn(`玩家${playerIndex}缺少floorLine，初始化为空数组`)
      player.floorLine = []
    }

    // 玩家信息标题
    this.ctx.fillStyle = '#333'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'left'
    const playerName = player.nickName || player.name || `玩家 ${playerIndex + 1}`
    this.ctx.fillText(playerName, x + 10, y + 25)

    // 玩家分数
    this.ctx.textAlign = 'right'
    this.ctx.fillText(`${player.score || 0} 分`, x + width - 10, y + 25)

    // 当前回合指示器
    if (playerIndex === this.gameState.currentPlayer) {
      this.ctx.fillStyle = '#FF9800'
      this.ctx.font = 'bold 12px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('当前回合', x + width/2, y + 25)
    }

    // 渲染玩家的游戏区域（准备区、墙壁、地板线）
    this.renderPlayerGameArea(player, playerIndex, x, y + 35, width, height - 35)
  }

  // 渲染玩家游戏区域
  renderPlayerGameArea(player, playerIndex, x, y, width, height) {
    // 增大磁砖尺寸以提高可见性
    const tileSize = 20
    const spacing = 3

    // 根据可用宽度动态计算区域尺寸，确保不超出屏幕
    const availableWidth = width - 20 // 左右各留10px边距
    const patternAreaWidth = Math.min(140, availableWidth * 0.45) // 准备区占45%
    const wallAreaWidth = Math.min(140, availableWidth * 0.45)   // 墙壁区占45%
    const gapBetweenAreas = Math.min(20, availableWidth * 0.1)   // 中间间隔占10%
    const floorAreaHeight = 35

    // 准备区位置
    const patternX = x + 10
    const patternY = y + 10

    // 墙壁位置 - 确保不超出右边界
    const wallX = Math.min(
      patternX + patternAreaWidth + gapBetweenAreas,
      x + width - wallAreaWidth - 10
    )
    const wallY = y + 10

    // 地板线位置
    const floorX = x + 10
    const floorY = y + height - floorAreaHeight - 10

    // 渲染准备区
    this.renderPatternLinesInArea(player.patternLines, patternX, patternY, tileSize, spacing)

    // 渲染墙壁
    this.renderWallInArea(player.wall, wallX, wallY, tileSize, spacing)

    // 渲染地板线
    this.renderFloorLineInArea(player.floorLine, floorX, floorY, tileSize, spacing, width - 20)
  }

  // 在指定区域渲染准备区
  renderPatternLinesInArea(patternLines, startX, startY, tileSize, spacing) {
    patternLines.forEach((line, lineIndex) => {
      const lineY = startY + lineIndex * (tileSize + spacing + 3)

      // 行号
      this.ctx.fillStyle = '#6c757d'
      this.ctx.font = 'bold 12px Arial'
      this.ctx.textAlign = 'left'
      this.ctx.fillText(`${lineIndex + 1}:`, startX - 20, lineY + tileSize / 2 + 4)

      // 渲染准备区磁砖
      for (let i = 0; i <= lineIndex; i++) {
        const tileX = startX + i * (tileSize + spacing)
        if (i < line.length) {
          this.renderTile(line[i], tileX, lineY, tileSize)
        } else {
          // 空槽位
          this.ctx.strokeStyle = '#ddd'
          this.ctx.lineWidth = 1
          this.ctx.strokeRect(tileX, lineY, tileSize, tileSize)
        }
      }
    })
  }

  // 在指定区域渲染墙壁
  renderWallInArea(wall, startX, startY, tileSize, spacing) {
    // 墙壁标题
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.fillText('墙壁:', startX, startY - 5)

    // 渲染5x5墙壁网格
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (tileSize + spacing)
        const y = startY + row * (tileSize + spacing + 3)
        const cell = wall[row][col]

        if (cell.filled) {
          // 已填充：显示完整颜色
          this.renderTile(cell.color, x, y, tileSize)
        } else {
          // 未填充：尝试使用棋盘图片
          this.renderWallSlot(x, y, tileSize, row, col, cell.color)
        }
      }
    }
  }

  // 在指定区域渲染地板线
  renderFloorLineInArea(floorLine, startX, startY, tileSize, spacing, maxWidth) {
    const floorPenalties = [-1, -1, -2, -2, -2, -3, -3]
    const slotSize = tileSize

    // 地板线标题
    this.ctx.fillStyle = '#e74c3c'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.fillText('地板线:', startX, startY)

    // 渲染7个地板线槽位
    for (let i = 0; i < 7; i++) {
      const x = startX + 60 + i * (slotSize + spacing)
      const y = startY + 5

      // 槽位背景
      this.ctx.fillStyle = '#f8f9fa'
      this.ctx.fillRect(x, y, slotSize, slotSize)
      this.ctx.strokeStyle = '#dee2e6'
      this.ctx.lineWidth = 1
      this.ctx.strokeRect(x, y, slotSize, slotSize)

      // 如果有磁砖，显示磁砖
      if (i < floorLine.length) {
        const tile = floorLine[i]
        if (tile === 'first') {
          // 起始玩家磁砖
          this.ctx.fillStyle = '#ffd700'
          this.ctx.fillRect(x + 1, y + 1, slotSize - 2, slotSize - 2)
          this.ctx.strokeStyle = '#f39c12'
          this.ctx.lineWidth = 1
          this.ctx.strokeRect(x + 1, y + 1, slotSize - 2, slotSize - 2)
          this.ctx.fillStyle = '#2c3e50'
          this.ctx.font = 'bold 10px Arial'
          this.ctx.textAlign = 'center'
          this.ctx.fillText('起', x + slotSize / 2, y + slotSize / 2 + 3)
        } else {
          // 普通磁砖
          this.renderTile(tile, x + 1, y + 1, slotSize - 2)
        }
      }

      // 显示扣分
      this.ctx.fillStyle = '#e74c3c'
      this.ctx.font = 'bold 8px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(floorPenalties[i].toString(), x + slotSize / 2, y + slotSize + 12)
    }
  }

  // 渲染玩家墙壁
  renderPlayerWall(player, startX, startY) {
    const tileSize = 14  // 增加墙壁磁砖大小
    const gap = 2

    // 墙壁标题
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.font = 'bold 13px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.fillText('墙壁:', startX, startY - 5)

    // 渲染5x5墙壁网格
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (tileSize + gap)
        const y = startY + row * (tileSize + gap)
        const cell = player.wall[row][col]

        if (cell.filled) {
          // 已填充：显示完整颜色
          this.renderTile(cell.color, x, y, tileSize)
        } else {
          // 未填充：尝试使用棋盘图片
          this.renderWallSlot(x, y, tileSize, row, col, cell.color)
        }
      }
    }
  }

  // 获取默认浅色
  getDefaultColor(color) {
    const colorMap = {
      blue: 'rgba(52, 152, 219, 0.3)',
      yellow: 'rgba(241, 196, 15, 0.3)',
      red: 'rgba(231, 76, 60, 0.3)',
      black: 'rgba(52, 73, 94, 0.3)',
      teal: 'rgba(26, 188, 156, 0.3)'
    }
    return colorMap[color] || 'rgba(200, 200, 200, 0.3)'
  }

  // 渲染玩家墙壁（紧凑版本）
  renderPlayerWallCompact(player, startX, startY, tileSize, gap, isCompact, lineSpacing) {
    // 墙壁标题
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.font = isCompact ? 'bold 11px Arial' : 'bold 13px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.fillText('墙壁:', startX, startY - 10)  // 调整标题位置，保持与内容的相对距离

    // 渲染5x5墙壁网格 - 使用与准备区相同的行间距
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (tileSize + gap)
        // 使用与准备区相同的行间距计算Y坐标
        const y = startY + row * lineSpacing
        const cell = player.wall[row][col]

        if (cell.filled) {
          // 已填充：显示完整颜色
          this.renderTile(cell.color, x, y, tileSize)
        } else {
          // 未填充：尝试使用棋盘图片
          this.renderWallSlot(x, y, tileSize, row, col, cell.color)
        }
      }
    }
  }

  // 渲染地板线（紧凑版本）
  renderFloorLineCompact(player, startX, startY, isCompact) {
    const floorPenalties = [-1, -1, -2, -2, -2, -3, -3]
    const slotSize = isCompact ? 12 : 15
    const gap = isCompact ? 2 : 3

    // 地板线标题
    this.ctx.fillStyle = '#e74c3c'
    this.ctx.font = isCompact ? 'bold 10px Arial' : 'bold 12px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.fillText('地板线:', startX, startY)

    // 渲染7个地板线槽位
    for (let i = 0; i < 7; i++) {
      const x = startX + (isCompact ? 45 : 60) + i * (slotSize + gap)
      const y = startY - 8

      // 槽位背景
      this.ctx.fillStyle = '#f8f9fa'
      this.ctx.fillRect(x, y, slotSize, slotSize)
      this.ctx.strokeStyle = '#dee2e6'
      this.ctx.lineWidth = 1
      this.ctx.strokeRect(x, y, slotSize, slotSize)

      // 如果有磁砖，显示磁砖
      if (i < player.floorLine.length) {
        const tile = player.floorLine[i]
        if (tile === 'first') {
          // 起始玩家磁砖 - 优化高DPI显示
          this.ctx.fillStyle = '#ffd700'
          this.ctx.fillRect(x + 1, y + 1, slotSize - 2, slotSize - 2)

          // 添加边框增强视觉效果
          this.ctx.strokeStyle = '#f39c12'
          this.ctx.lineWidth = 1
          this.ctx.strokeRect(x + 1, y + 1, slotSize - 2, slotSize - 2)

          // 优化文字渲染
          this.ctx.fillStyle = '#2c3e50'
          this.ctx.font = `bold ${isCompact ? '8px' : '10px'} Arial`
          this.ctx.textAlign = 'center'
          this.ctx.textBaseline = 'middle'
          this.ctx.fillText('起', x + slotSize / 2, y + slotSize / 2)
        } else {
          // 普通磁砖
          this.renderTile(tile, x + 1, y + 1, slotSize - 2)
        }
      }

      // 显示扣分
      this.ctx.fillStyle = '#e74c3c'
      this.ctx.font = isCompact ? 'bold 6px Arial' : 'bold 8px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(floorPenalties[i].toString(), x + slotSize / 2, y + slotSize + 8)
    }


  }

  // 渲染地板线
  renderFloorLine(player, startX, startY) {
    const floorPenalties = [-1, -1, -2, -2, -2, -3, -3]
    const slotSize = 15
    const gap = 3

    // 地板线标题
    this.ctx.fillStyle = '#e74c3c'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.fillText('地板线:', startX, startY)

    // 渲染7个地板线槽位
    for (let i = 0; i < 7; i++) {
      const x = startX + 60 + i * (slotSize + gap)
      const y = startY - 10

      // 槽位背景
      this.ctx.fillStyle = '#f8f9fa'
      this.ctx.fillRect(x, y, slotSize, slotSize)
      this.ctx.strokeStyle = '#dee2e6'
      this.ctx.lineWidth = 1
      this.ctx.strokeRect(x, y, slotSize, slotSize)

      // 如果有磁砖，显示磁砖
      if (i < player.floorLine.length) {
        const tile = player.floorLine[i]
        if (tile === 'first') {
          // 起始玩家磁砖 - 优化高DPI显示
          this.ctx.fillStyle = '#ffd700'
          this.ctx.fillRect(x + 1, y + 1, slotSize - 2, slotSize - 2)

          // 添加边框增强视觉效果
          this.ctx.strokeStyle = '#f39c12'
          this.ctx.lineWidth = 1
          this.ctx.strokeRect(x + 1, y + 1, slotSize - 2, slotSize - 2)

          // 优化文字渲染
          this.ctx.fillStyle = '#2c3e50'
          this.ctx.font = 'bold 10px Arial'
          this.ctx.textAlign = 'center'
          this.ctx.textBaseline = 'middle'
          this.ctx.fillText('起', x + slotSize / 2, y + slotSize / 2)
        } else {
          // 普通磁砖
          this.renderTile(tile, x + 1, y + 1, slotSize - 2)
        }
      }

      // 显示扣分
      this.ctx.fillStyle = '#e74c3c'
      this.ctx.font = 'bold 8px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(floorPenalties[i].toString(), x + slotSize / 2, y + slotSize + 10)
    }


  }

  // 渲染UI
  renderUI() {
    // 减少日志输出，避免刷屏
    const hasSelection = this.gameState.showingFactorySelection || this.gameState.showingCenterSelection || this.gameState.selectedTiles;

    if (this.gameState.showingFactorySelection) {
      this.renderFactorySelection()
    } else if (this.gameState.showingCenterSelection) {
      this.renderCenterSelection()
    } else if (this.gameState.selectedTiles) {
      this.renderPlacementSelection()
    }

    // 渲染计时器
    this.renderTimer()

    // 渲染重连状态
    if (this.isReconnecting) {
      this.renderReconnectingStatus()
    }

    // 操作提示
    this.ctx.fillStyle = '#6c757d'
    this.ctx.font = '14px Arial'
    this.ctx.textAlign = 'center'

    const { width, height } = this.getLogicalSize()
    if (this.gameState.showingFactorySelection) {
      this.ctx.fillText('选择要拿取的磁砖颜色', width / 2, height - 20)
    } else if (this.gameState.showingCenterSelection) {
      this.ctx.fillText('选择要拿取的磁砖颜色', width / 2, height - 20)
    } else if (this.gameState.selectedTiles) {
      this.ctx.fillText('点击高亮区域放置磁砖，点击其他地方取消', width / 2, height - 20)
    } else {
      this.ctx.fillText('点击工厂或中央区域选择磁砖', width / 2, height - 20)
    }
  }

  // 渲染计时器
  renderTimer() {
    // 只在收集阶段且当前玩家是人类时显示计时器
    const humanCount = this.gameState.playerCount - this.gameState.aiCount
    const isCurrentPlayerHuman = this.gameState.currentPlayer < humanCount

    if (this.gameState.phase !== 'collect' || !isCurrentPlayerHuman || !this.gameState.showTimer) {
      return
    }

    const timeSeconds = Math.ceil(this.gameState.timeRemaining / 1000)
    const safeAreaTop = 40  // 头部安全区域

    // 计时器背景 - 放在头部区域右侧，避免与所有元素重合
    const timerWidth = 100
    const timerHeight = 35
    const { width } = this.getLogicalSize()
    const timerX = width - timerWidth - 15  // 头部右侧位置
    const timerY = 45 + safeAreaTop  // 在回合信息的高度

    // 根据剩余时间改变颜色
    let bgColor, textColor
    if (timeSeconds <= 5) {
      bgColor = 'rgba(244, 67, 54, 0.9)'  // 红色警告
      textColor = '#ffffff'
    } else if (timeSeconds <= 10) {
      bgColor = 'rgba(255, 152, 0, 0.9)'  // 橙色提醒
      textColor = '#ffffff'
    } else {
      bgColor = 'rgba(76, 175, 80, 0.9)'  // 绿色正常
      textColor = '#ffffff'
    }

    // 绘制计时器背景
    this.ctx.fillStyle = bgColor
    this.ctx.fillRect(timerX, timerY, timerWidth, timerHeight)
    this.ctx.strokeStyle = textColor
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(timerX, timerY, timerWidth, timerHeight)

    // 绘制计时器文字
    this.ctx.fillStyle = textColor
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText(`⏰ ${timeSeconds}s`, timerX + timerWidth / 2, timerY + timerHeight / 2 + 5)
  }

  // 渲染工厂选择界面
  renderFactorySelection() {
    const { width, height } = this.getLogicalSize()
    const factoryIndex = this.gameState.selectedFactory
    const factory = this.gameState.factories[factoryIndex]

    if (!factory) {
      console.error('工厂不存在:', factoryIndex)
      return
    }

    if (factory.length === 0) {
      console.error('工厂为空:', factoryIndex)
      return
    }

    // 半透明覆盖
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
    this.ctx.fillRect(0, 0, width, height)

    // 选择框
    const boxWidth = Math.min(300, width - 40)
    const boxHeight = 200
    const x = (width - boxWidth) / 2
    const y = (height - boxHeight) / 2

    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(x, y, boxWidth, boxHeight)
    this.ctx.strokeStyle = '#2c3e50'
    this.ctx.lineWidth = 3
    this.ctx.strokeRect(x, y, boxWidth, boxHeight)

    // 标题
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.font = 'bold 18px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText(`工厂 ${factoryIndex + 1}`, x + boxWidth / 2, y + 30)

    // 显示工厂中的所有磁砖
    this.ctx.font = '14px Arial'
    this.ctx.fillText('选择要拿取的颜色:', x + boxWidth / 2, y + 55)

    // 按颜色分组显示
    const colorGroups = this.groupFactoryTiles(factory)
    const startY = y + 80
    const tileSize = 30

    // 重置点击区域数组
    this.factoryColorAreas = []

    colorGroups.forEach((group, index) => {
      const tileX = x + 30 + index * 60
      const tileY = startY

      // 渲染磁砖
      this.renderTile(group.color, tileX, tileY, tileSize)

      // 显示数量
      this.ctx.fillStyle = '#2c3e50'
      this.ctx.font = 'bold 14px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(`×${group.count}`, tileX + tileSize / 2, tileY + tileSize + 20)

      // 存储点击区域（用于碰撞检测）
      this.factoryColorAreas.push({
        color: group.color,
        x: tileX,
        y: tileY,
        width: tileSize,
        height: tileSize
      })
    })

    // 设置工厂颜色点击区域完成

    // 取消按钮
    const cancelBtnWidth = 80
    const cancelBtnHeight = 30
    const cancelX = x + boxWidth - cancelBtnWidth - 15
    const cancelY = y + boxHeight - cancelBtnHeight - 15

    this.ctx.fillStyle = '#e74c3c'
    this.ctx.fillRect(cancelX, cancelY, cancelBtnWidth, cancelBtnHeight)
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('取消', cancelX + cancelBtnWidth / 2, cancelY + cancelBtnHeight / 2 + 5)

    // 存储取消按钮区域
    this.cancelButtonArea = {
      x: cancelX,
      y: cancelY,
      width: cancelBtnWidth,
      height: cancelBtnHeight
    }
  }

  // 分组工厂磁砖
  groupFactoryTiles(factory) {
    const groups = {}
    const factoryTiles = factory.tiles || factory; // 兼容新旧格式

    if (Array.isArray(factoryTiles)) {
      factoryTiles.forEach(tile => {
        groups[tile] = (groups[tile] || 0) + 1
      })
    }

    return Object.keys(groups).map(color => ({
      color: color,
      count: groups[color]
    }))
  }

  // 渲染中央区域选择界面
  renderCenterSelection() {
    const { width, height } = this.getLogicalSize()
    const centerArea = this.gameState.centerArea

    // 半透明覆盖
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
    this.ctx.fillRect(0, 0, width, height)

    // 选择框
    const boxWidth = Math.min(300, width - 40)
    const boxHeight = 200
    const x = (width - boxWidth) / 2
    const y = (height - boxHeight) / 2

    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(x, y, boxWidth, boxHeight)
    this.ctx.strokeStyle = '#2c3e50'
    this.ctx.lineWidth = 3
    this.ctx.strokeRect(x, y, boxWidth, boxHeight)

    // 标题
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.font = 'bold 18px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('中央区域', x + boxWidth / 2, y + 30)

    // 显示起始玩家磁砖提示
    if (this.gameState.firstPlayerTile) {
      this.ctx.font = '14px Arial'
      this.ctx.fillStyle = '#e74c3c'
      this.ctx.fillText('选择任意颜色将获得起始玩家磁砖', x + boxWidth / 2, y + 55)
    } else {
      this.ctx.font = '14px Arial'
      this.ctx.fillText('选择要拿取的颜色:', x + boxWidth / 2, y + 55)
    }

    // 按颜色分组显示
    const colorGroups = this.groupCenterTiles()
    const startY = y + 80
    const tileSize = 30

    // 重置颜色区域数组
    this.centerColorAreas = []

    colorGroups.forEach((group, index) => {
      const tileX = x + 30 + index * 60
      const tileY = startY

      // 渲染磁砖
      this.renderTile(group.color, tileX, tileY, tileSize)

      // 显示数量
      this.ctx.fillStyle = '#2c3e50'
      this.ctx.font = 'bold 14px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(`×${group.count}`, tileX + tileSize / 2, tileY + tileSize + 20)

      // 存储点击区域（用于碰撞检测）
      this.centerColorAreas[index] = {
        color: group.color,
        x: tileX,
        y: tileY,
        width: tileSize,
        height: tileSize
      }
    })

    // 取消按钮
    const cancelBtnWidth = 80
    const cancelBtnHeight = 30
    const cancelX = x + boxWidth - cancelBtnWidth - 15
    const cancelY = y + boxHeight - cancelBtnHeight - 15

    this.ctx.fillStyle = '#e74c3c'
    this.ctx.fillRect(cancelX, cancelY, cancelBtnWidth, cancelBtnHeight)
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('取消', cancelX + cancelBtnWidth / 2, cancelY + cancelBtnHeight / 2 + 5)

    // 存储取消按钮区域
    this.cancelButtonArea = {
      x: cancelX,
      y: cancelY,
      width: cancelBtnWidth,
      height: cancelBtnHeight
    }
  }

  // 渲染放置选择界面（新版本：高亮可放置区域）
  renderPlacementSelection() {
    const { width, height } = this.getLogicalSize()

    // 添加选择性遮罩，让非高亮区域变灰
    this.renderSelectiveMask()

    // 高亮可放置区域
    this.renderPlacementHighlight()

    // 显示选中磁砖信息（右上角）
    this.renderSelectedTilesInfo()
  }

  // 渲染选择性遮罩（让非高亮区域变灰）
  renderSelectiveMask() {
    const { width, height } = this.getLogicalSize()
    const currentPlayerIndex = this.gameState.currentPlayer
    const playerCount = this.gameState.players.length

    // 计算当前玩家面板的位置
    let currentPanelX, currentPanelY, currentPanelWidth, currentPanelHeight

    if (playerCount <= 2) {
      // 垂直布局
      const playerHeight = 180
      const startY = height - 430
      currentPanelX = 10
      currentPanelY = startY + currentPlayerIndex * (playerHeight + 10)
      currentPanelWidth = width - 20
      currentPanelHeight = playerHeight
    } else {
      // 网格布局
      currentPanelWidth = (width - 30) / 2
      currentPanelHeight = playerCount === 3 ? 160 : 140
      const startY = height - (playerCount === 3 ? 390 : 350)
      const col = currentPlayerIndex % 2
      const row = Math.floor(currentPlayerIndex / 2)
      currentPanelX = 10 + col * (currentPanelWidth + 10)
      currentPanelY = startY + row * (currentPanelHeight + 10)
    }

    // 不使用globalCompositeOperation，而是分区域绘制遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'

    // 上方区域遮罩（从顶部到当前玩家面板上方）
    if (currentPanelY > 0) {
      this.ctx.fillRect(0, 0, width, currentPanelY)
    }

    // 左侧区域遮罩（当前玩家面板左侧）
    if (currentPanelX > 0) {
      this.ctx.fillRect(0, currentPanelY, currentPanelX, currentPanelHeight)
    }

    // 右侧区域遮罩（当前玩家面板右侧）
    const rightX = currentPanelX + currentPanelWidth
    if (rightX < width) {
      this.ctx.fillRect(rightX, currentPanelY, width - rightX, currentPanelHeight)
    }

    // 下方区域遮罩（当前玩家面板下方到底部）
    const bottomY = currentPanelY + currentPanelHeight
    if (bottomY < height) {
      this.ctx.fillRect(0, bottomY, width, height - bottomY)
    }
  }

  // 渲染放置高亮效果
  renderPlacementHighlight() {
    const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
    const currentPlayerIndex = this.gameState.currentPlayer

    // 如果当前玩家不是正在查看的玩家，切换到当前玩家
    if (this.currentViewingPlayer !== currentPlayerIndex) {
      this.currentViewingPlayer = currentPlayerIndex
      this.render() // 重新渲染以显示当前玩家
      return
    }

    // 计算单面板布局中的内容区域位置
    const { width, height } = this.getLogicalSize()
    const centerRect = this.getCenterRect()
    const centerEndY = centerRect.y + centerRect.height
    const minStartY = centerEndY + 20

    // 根据玩家数量动态计算面板高度（与renderSinglePanelLayout保持一致）
    const playerCount = this.gameState.players.length
    const avatarSize = 50
    const avatarSpacing = 10
    const topBottomPadding = 40
    const minPanelHeight = 200
    const requiredHeightForAvatars = topBottomPadding + playerCount * (avatarSize + avatarSpacing) - avatarSpacing
    const panelHeight = Math.max(minPanelHeight, requiredHeightForAvatars)

    const defaultStartY = height - panelHeight - 50
    const panelY = Math.max(minStartY, defaultStartY)

    const avatarAreaWidth = 80
    const panelX = 10
    const contentX = panelX + avatarAreaWidth + 10
    const contentWidth = width - 20 - avatarAreaWidth - 10

    // 高亮可放置的准备区行（使用新的布局参数）
    this.highlightAvailablePatternLines(currentPlayer, contentX, panelY + 35, true)

    // 存储可点击区域用于点击检测
    this.updatePlacementClickAreas(currentPlayer, contentX, panelY + 35, true)
  }

  // 渲染单个玩家（用于高亮显示）
  renderSinglePlayer(player, playerIndex, y) {
    // 使用统一的面板渲染系统
    const playerCount = this.gameState.players.length

    if (playerCount <= 2) {
      // 垂直布局
      const { width } = this.getLogicalSize()
      this.renderSinglePlayerPanel(player, playerIndex, 10, y, width - 20, 180)
    } else {
      // 网格布局 - 需要找到当前玩家的位置
      const { width, height } = this.getLogicalSize()
      const panelWidth = (width - 30) / 2
      const panelHeight = playerCount === 3 ? 160 : 140
      const col = playerIndex % 2
      const row = Math.floor(playerIndex / 2)
      const x = 10 + col * (panelWidth + 10)

      // 使用固定位置，与renderPlacementHighlight保持一致
      const startY = height - (playerCount === 3 ? 390 : 350)

      const panelY = startY + row * (panelHeight + 10)

      this.renderSinglePlayerPanel(player, playerIndex, x, panelY, panelWidth, panelHeight, true)
    }
  }

  // 高亮可放置的准备区行
  highlightAvailablePatternLines(player, panelX, panelY, isCompact) {
    // 使用与实际渲染相同的参数
    const tileSize = 20
    const spacing = 3
    const lineSpacing = tileSize + spacing + 3  // 与renderPatternLinesInArea保持一致

    for (let i = 0; i < 5; i++) {
      const patternLine = player.patternLines[i]
      const maxCapacity = i + 1

      // 检查是否可以放置
      const canPlace = this.canPlaceOnLine(patternLine, this.gameState.selectedTiles[0], maxCapacity, player, i)

      if (canPlace) {
        // 计算准备区行的精确坐标 - 与renderPatternLinesInArea保持一致
        const patternStartX = panelX + 10  // 与renderPlayerGameArea中的patternX一致
        const patternStartY = panelY + 10  // 与renderPlayerGameArea中的patternY一致
        const lineY = patternStartY + i * lineSpacing

        // 高亮框应该覆盖从行号到最后一个磁砖槽位
        const highlightX = patternStartX - 20  // 从行号开始
        // 第i行有i+1个磁砖，最后一个磁砖的索引是i
        const lastTileX = patternStartX + i * (tileSize + spacing)  // 最后一个磁砖的X坐标
        const highlightWidth = lastTileX + tileSize - highlightX  // 到最后一个磁砖结束
        const highlightHeight = tileSize + 2  // 磁砖高度加上少量边距

        // 高亮背景 - 与磁砖精确对齐（移除-7的偏移）
        this.ctx.fillStyle = 'rgba(40, 167, 69, 0.3)'
        this.ctx.fillRect(highlightX, lineY - 1, highlightWidth, highlightHeight)

        // 高亮边框
        this.ctx.strokeStyle = '#28a745'
        this.ctx.lineWidth = 2
        this.ctx.strokeRect(highlightX, lineY - 1, highlightWidth, highlightHeight)
      }
    }

    // 高亮地板线（总是可以放置）
    this.highlightFloorLine(player, panelX, panelY, isCompact)
  }

  // 高亮地板线
  highlightFloorLine(player, panelX, panelY, isCompact) {
    // 使用与实际渲染完全相同的位置计算
    // 渲染时使用：renderFloorLineCompact(player, x + 8, floorY, isCompact)
    // 其中 floorY = isCompact ? y + 125 : y + 145
    // 而 panelY 对应渲染时的 y，所以：
    const floorLineY = isCompact ? panelY + 125 : panelY + 145
    const slotSize = isCompact ? 12 : 15

    // 计算实际地板线槽位的位置（与renderFloorLineCompact保持一致）
    // 渲染时：startX = x + 8, startY = floorY
    // 槽位位置：x = startX + (isCompact ? 45 : 60) + i * (slotSize + gap)
    //          y = startY - 8
    const renderStartX = panelX + 8  // 对应渲染时的 startX
    const actualSlotY = floorLineY - 8  // 对应渲染时的 y = startY - 8
    const slotStartX = renderStartX + (isCompact ? 45 : 60)  // 第一个槽位的X坐标
    const gap = isCompact ? 2 : 3
    const totalSlotsWidth = 7 * slotSize + 6 * gap  // 7个槽位的总宽度

    // 地板线高亮背景（覆盖整个地板线区域）
    this.ctx.fillStyle = 'rgba(231, 76, 60, 0.2)'
    this.ctx.fillRect(renderStartX, actualSlotY - 2, slotStartX - renderStartX + totalSlotsWidth, slotSize + 4)

    // 地板线高亮边框
    this.ctx.strokeStyle = '#e74c3c'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(renderStartX, actualSlotY - 2, slotStartX - renderStartX + totalSlotsWidth, slotSize + 4)
  }

  // 检查是否可以在指定行放置磁砖
  canPlaceOnLine(patternLine, tileColor, maxCapacity, player, lineIndex) {
    // 检查准备区：行为空，或者颜色匹配且未满
    const patternLineOk = patternLine.length === 0 ||
                         (patternLine[0] === tileColor && patternLine.length < maxCapacity)

    if (!patternLineOk) return false

    // 检查墙壁：该行是否已有该颜色
    if (player && lineIndex !== undefined) {
      const wallRow = player.wall[lineIndex]
      const wallHasColor = wallRow.some(cell => cell.color === tileColor && cell.filled)
      if (wallHasColor) return false
    }

    return true
  }

  // 更新放置点击区域
  updatePlacementClickAreas(player, panelX, panelY, isCompact) {
    if (!this.placementClickAreas) this.placementClickAreas = []
    this.placementClickAreas = []

    // 使用与实际渲染和高亮相同的参数
    const tileSize = 20
    const spacing = 3
    const lineSpacing = tileSize + spacing + 3

    for (let i = 0; i < 5; i++) {
      const patternLine = player.patternLines[i]
      const maxCapacity = i + 1
      const canPlace = this.canPlaceOnLine(patternLine, this.gameState.selectedTiles[0], maxCapacity, player, i)

      if (canPlace) {
        // 计算与高亮框完全相同的坐标
        const patternStartX = panelX + 10
        const patternStartY = panelY + 10
        const lineY = patternStartY + i * lineSpacing
        const clickX = patternStartX - 20  // 从行号开始
        const lastTileX = patternStartX + i * (tileSize + spacing)  // 最后一个磁砖的X坐标
        const clickWidth = lastTileX + tileSize - clickX  // 到最后一个磁砖结束

        this.placementClickAreas.push({
          x: clickX,
          y: lineY,
          width: clickWidth,
          height: tileSize,
          lineIndex: i,
          type: 'patternLine'
        })
      }
    }

    // 添加地板线点击区域（总是可以点击）
    const floorLineY = isCompact ? panelY + 125 : panelY + 145  // 与实际渲染位置保持一致
    const slotSize = isCompact ? 12 : 15

    // 计算实际地板线槽位的位置（与高亮和渲染保持一致）
    const renderStartX = panelX + 8  // 对应渲染时的 startX
    const actualSlotY = floorLineY - 8  // 对应渲染时的 y = startY - 8
    const slotStartX = renderStartX + (isCompact ? 45 : 60)  // 第一个槽位的X坐标
    const gap = isCompact ? 2 : 3
    const totalSlotsWidth = 7 * slotSize + 6 * gap

    this.placementClickAreas.push({
      x: renderStartX,
      y: actualSlotY - 2,
      width: slotStartX - renderStartX + totalSlotsWidth,
      height: slotSize + 4,
      lineIndex: 5, // 使用5表示地板线
      type: 'floorLine'
    })
  }

  // 显示选中磁砖信息
  renderSelectedTilesInfo() {
    const infoWidth = 200
    const infoHeight = 60
    const safeAreaTop = 40  // 避免刘海遮挡
    // 移到左上角，避免与右上角的计时器重合
    // 位置在当前玩家提示下方，避免重合
    const x = 10
    const y = 10 + safeAreaTop + 30  // 在当前玩家提示下方

    // 信息框背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)'
    this.ctx.fillRect(x, y, infoWidth, infoHeight)
    this.ctx.strokeStyle = '#2c3e50'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(x, y, infoWidth, infoHeight)

    // 标题
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('选中的磁砖', x + infoWidth / 2, y + 20)

    // 显示磁砖
    this.gameState.selectedTiles.forEach((tile, index) => {
      this.renderTile(tile, x + 20 + index * 25, y + 25, 20)
    })

    // 提示文字
    this.ctx.fillStyle = '#666'
    this.ctx.font = '10px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('点击高亮区域放置，点击灰色取消', x + infoWidth / 2, y + infoHeight - 5)
  }

  // 处理点击结果
  processHit(hitResult) {
    console.log('点击结果:', hitResult)

    // 移除了在点击时重置计时器的逻辑，防止用户通过不断点击来重置计时器

    if (hitResult.type === 'factory') {
      // 点击工厂，显示颜色选择
      this.showFactorySelection(hitResult.factoryIndex)
    } else if (hitResult.type === 'factoryColor') {
      // 选择工厂中的颜色
      this.selectFromFactory(hitResult.factoryIndex, hitResult.color)
    } else if (hitResult.type === 'center') {
      // 点击中央区域，显示颜色选择
      this.showCenterSelection()
    } else if (hitResult.type === 'centerColor') {
      // 选择中央区域中的颜色
      this.selectFromCenter(hitResult.color)
    } else if (hitResult.type === 'cancel') {
      // 取消选择
      this.cancelSelection()
    }
  }

  // 显示工厂选择界面
  showFactorySelection(factoryIndex) {
    console.log(`显示工厂${factoryIndex + 1}的颜色选择`)

    // 播放瓷砖选择音效
    if (window.gameApp) {
      window.gameApp.playSound('tileSelect')
    }

    this.gameState.showingFactorySelection = true
    this.gameState.selectedFactory = factoryIndex

    console.log('设置工厂选择状态后，游戏状态:', {
      showingFactorySelection: this.gameState.showingFactorySelection,
      selectedFactory: this.gameState.selectedFactory
    })

    // 立即重新渲染以设置点击区域
    setTimeout(() => {
      console.log('延迟渲染工厂选择界面')
      this.render()
    }, 50)
  }

  // 显示中央区域选择界面
  showCenterSelection() {
    console.log('显示中央区域的颜色选择')
    this.gameState.showingCenterSelection = true

    // 立即重新渲染以设置点击区域
    this.render()
  }

  // 取消选择
  cancelSelection() {
    console.log('取消选择')
    this.gameState.showingFactorySelection = false
    this.gameState.showingCenterSelection = false
    this.gameState.selectedFactory = null
    this.gameState.selectedTiles = null
    this.gameState.selectedColor = null // 清除选择的颜色

    // 清除工厂选择相关状态
    this.gameState.selectedFactoryIndex = undefined
    this.gameState.selectedFactoryTiles = null
    this.gameState.remainingFactoryTiles = null
  }

  // 从工厂选择磁砖
  selectFromFactory(factoryIndex, color) {
    const factory = this.gameState.factories[factoryIndex]
    const selectedTiles = factory.filter(tile => tile === color)
    const remainingTiles = factory.filter(tile => tile !== color)

    console.log(`从工厂${factoryIndex + 1}选择${selectedTiles.length}个${color}磁砖`)

    // 先不清空工厂，等到确认放置后再清空
    // 存储工厂信息，用于后续处理
    this.gameState.selectedFactoryIndex = factoryIndex
    this.gameState.selectedFactoryTiles = selectedTiles
    this.gameState.remainingFactoryTiles = remainingTiles

    // 清除任何之前的中央区域选择状态（防止混淆）
    this.gameState.selectedCenterTiles = null

    // 关闭选择界面
    this.gameState.showingFactorySelection = false
    this.gameState.selectedFactory = null

    // 存储选中的磁砖，等待玩家选择放置位置
    this.gameState.selectedTiles = selectedTiles
    this.gameState.selectedColor = color

    // 显示放置选择界面
    this.showPlacementSelection(selectedTiles)
  }

  // 从中央区域选择磁砖
  selectFromCenter(color) {
    const selectedTiles = this.gameState.centerArea.filter(tile => tile === color)

    console.log(`从中央区域选择${selectedTiles.length}个${color}磁砖`)

    // 先不移除磁砖，等到确认放置后再移除
    // 关闭选择界面
    this.gameState.showingCenterSelection = false

    // 清除任何之前的工厂选择状态（防止混淆）
    this.gameState.selectedFactoryIndex = undefined
    this.gameState.selectedFactoryTiles = null
    this.gameState.remainingFactoryTiles = null

    // 存储选中的磁砖和颜色，等待玩家选择放置位置
    this.gameState.selectedTiles = selectedTiles
    this.gameState.selectedColor = color // 记住选择的颜色
    this.gameState.selectedCenterTiles = selectedTiles // 用于后续清理

    // 显示放置选择界面
    this.showPlacementSelection(selectedTiles)
  }

  // 显示放置选择界面
  showPlacementSelection(tiles) {
    console.log(`显示${tiles.length}个磁砖的放置选择`)
    // 放置选择界面通过renderUI中的条件渲染显示
  }

  // 获取工厂颜色点击
  getFactoryColorHit(x, y) {
    if (!this.factoryColorAreas || this.factoryColorAreas.length === 0) {
      return null
    }

    for (let i = 0; i < this.factoryColorAreas.length; i++) {
      const area = this.factoryColorAreas[i]
      if (area && this.isPointInRect(x, y, area)) {
        console.log('命中工厂颜色:', area.color)
        return { color: area.color }
      }
    }
    return null
  }

  // 获取中央区域颜色点击
  getCenterColorHit(x, y) {
    if (!this.centerColorAreas) return null

    for (let area of this.centerColorAreas) {
      if (this.isPointInRect(x, y, area)) {
        return { color: area.color }
      }
    }
    return null
  }

  // 获取取消按钮点击
  getCancelButtonHit(x, y) {
    if (!this.cancelButtonArea) return null

    if (this.isPointInRect(x, y, this.cancelButtonArea)) {
      return true
    }
    return false
  }

  // 检测点击是否在工厂选择框内
  isPointInFactorySelectionBox(x, y) {
    const { width, height } = this.getLogicalSize()
    const boxWidth = Math.min(300, width - 40)
    const boxHeight = 200
    const boxX = (width - boxWidth) / 2
    const boxY = (height - boxHeight) / 2

    return this.isPointInRect(x, y, {
      x: boxX,
      y: boxY,
      width: boxWidth,
      height: boxHeight
    })
  }

  // 检测点击是否在中央区域选择框内
  isPointInCenterSelectionBox(x, y) {
    const { width, height } = this.getLogicalSize()
    const boxWidth = Math.min(300, width - 40)
    const boxHeight = 200
    const boxX = (width - boxWidth) / 2
    const boxY = (height - boxHeight) / 2

    return this.isPointInRect(x, y, {
      x: boxX,
      y: boxY,
      width: boxWidth,
      height: boxHeight
    })
  }

  // 检测点击是否在当前玩家面板内
  isPointInCurrentPlayerPanel(x, y) {
    // 使用新的单面板布局
    const { width, height } = this.getLogicalSize()
    const centerRect = this.getCenterRect()
    const centerEndY = centerRect.y + centerRect.height
    const minStartY = centerEndY + 20

    // 根据玩家数量动态计算面板高度（与renderSinglePanelLayout保持一致）
    const playerCount = this.gameState.players.length
    const avatarSize = 50
    const avatarSpacing = 10
    const topBottomPadding = 40
    const minPanelHeight = 200
    const requiredHeightForAvatars = topBottomPadding + playerCount * (avatarSize + avatarSpacing) - avatarSpacing
    const panelHeight = Math.max(minPanelHeight, requiredHeightForAvatars)

    const defaultStartY = height - panelHeight - 50
    const panelY = Math.max(minStartY, defaultStartY)

    const panelX = 10
    const panelWidth = width - 20

    return this.isPointInRect(x, y, {
      x: panelX,
      y: panelY,
      width: panelWidth,
      height: panelHeight
    })
  }

  // 放置磁砖
  placeTiles(tiles, lineIndex) {
    const currentPlayer = this.gameState.players[this.gameState.currentPlayer]

    // 播放瓷砖放置音效
    if (window.gameApp) {
      window.gameApp.playSound('tilePlace')
    }

    // 检查是否放置到地板线
    if (lineIndex === 5) {
      console.log(`主动放置${tiles.length}个磁砖到地板线`)

      // 处理工厂/中央区域的清理逻辑
      const isFromFactory = this.handleTileSourceCleanup()

      // 检查起始玩家磁砖（只有从中央区域选择时才获得）
      if (this.gameState.firstPlayerTile && !isFromFactory) {
        this.gameState.firstPlayerTile = false
        currentPlayer.floorLine.push('first')
        currentPlayer.hasFirstPlayerTile = true
        console.log('获得起始玩家磁砖')
      }

      // 直接放入地板线
      currentPlayer.floorLine.push(...tiles)

      // 清除选择状态并进入下一个玩家
      this.clearSelectionState()

      // 用户完成了操作，清除当前计时器
      this.clearTurnTimer()

      this.nextPlayer()
      return
    }

    const patternLine = currentPlayer.patternLines[lineIndex]
    const maxCapacity = lineIndex + 1

    console.log(`放置${tiles.length}个磁砖到第${lineIndex + 1}行`)

    // 处理工厂/中央区域的清理逻辑
    const isFromFactory = this.handleTileSourceCleanup()

    // 检查起始玩家磁砖（只有从中央区域选择时才获得）
    if (this.gameState.firstPlayerTile && !isFromFactory) {
      this.gameState.firstPlayerTile = false
      currentPlayer.floorLine.push('first')
      currentPlayer.hasFirstPlayerTile = true
      console.log('获得起始玩家磁砖')
    }

    // 检查是否可以放置
    if (patternLine.length > 0 && patternLine[0] !== tiles[0]) {
      // 不能放置，放入地板线
      console.log('颜色不匹配，放入地板线')
      currentPlayer.floorLine.push(...tiles)

      wx.showToast({
        title: '颜色不匹配！',
        icon: 'none',
        duration: 1500
      })
    } else {
      // 检查墙壁是否已有该颜色
      const wallRow = currentPlayer.wall[lineIndex]
      const wallHasColor = wallRow.some(cell => cell.color === tiles[0] && cell.filled)

      if (wallHasColor) {
        console.log('墙壁已有该颜色，放入地板线')
        currentPlayer.floorLine.push(...tiles)

        wx.showToast({
          title: '墙壁已有该颜色！',
          icon: 'none',
          duration: 1500
        })
      } else {
      // 放置磁砖
      let placedCount = 0
      for (let tile of tiles) {
        if (patternLine.length < maxCapacity) {
          patternLine.push(tile)
          placedCount++
        } else {
          currentPlayer.floorLine.push(tile)
        }
      }

      console.log(`成功放置${placedCount}个磁砖，${tiles.length - placedCount}个溢出到地板线`)

      if (placedCount > 0) {
        wx.showToast({
          title: `放置${placedCount}个磁砖`,
          icon: 'success',
          duration: 1000
        })
      }
    }
    }

    // 清除选择状态
    this.gameState.selectedTiles = null
    this.gameState.selectedColor = null
    this.gameState.showingFactorySelection = false
    this.gameState.showingCenterSelection = false
    this.gameState.selectedFactory = null

    // 用户完成了一个完整操作，重置计时器为下一个玩家准备
    // 注意：这里不立即启动计时器，而是在nextPlayer()中启动
    this.clearTurnTimer()

    // 检查回合结束
    this.checkTurnEnd()
  }

  // 检查回合结束
  checkTurnEnd() {
    const allFactoriesEmpty = this.gameState.factories.every(factory => factory.length === 0)
    const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea
    const centerEmpty = Array.isArray(centerTiles) && centerTiles.length === 0

    console.log('🔍 检查回合结束状态:')
    console.log('  - 所有工厂是否为空:', allFactoriesEmpty)
    console.log('  - 中央区域是否为空:', centerEmpty)
    console.log('  - 工厂状态:', this.gameState.factories.map((f, i) => `工厂${i}: [${f.join(', ')}]`))
    console.log('  - 中央区域状态:', centerTiles)
    console.log('  - 当前阶段:', this.gameState.phase)
    console.log('  - 当前玩家:', this.gameState.currentPlayer)

    if (allFactoriesEmpty && centerEmpty) {
      console.log('✅ 回合结束条件满足，进入计分阶段')
      // 进入计分阶段
      this.gameState.phase = 'scoring'
      setTimeout(() => {
        this.scoringPhase()
      }, 1000)
    } else {
      console.log('⏭️ 回合未结束，切换到下一个玩家')
      // 切换玩家
      this.nextPlayer()
    }
  }

  // 下一个玩家
  nextPlayer() {
    // 清除当前玩家的计时器
    this.clearTurnTimer()

    // 检查轮次是否结束（所有工厂和中央区域都空了）
    if (this.isRoundComplete()) {
      console.log('轮次结束，开始计分')
      this.scoringPhase()
      return
    }

    // 紧急检查：如果所有工厂都空了但中央区域还有瓷砖，检查是否有可操作的瓷砖
    const allFactoriesEmpty = this.gameState.factories.every(factory => factory.length === 0)
    const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea
    const hasOperableTiles = Array.isArray(centerTiles) && centerTiles.length > 0

    if (allFactoriesEmpty && !hasOperableTiles) {
      console.log('🚨 紧急检测：所有工厂为空且中央区域无可操作瓷砖，强制结束回合')
      this.scoringPhase()
      return
    }

    // 检查是否陷入无限循环（同一轮中玩家切换次数过多）
    if (!this.playerSwitchCount) {
      this.playerSwitchCount = 0
      this.currentRoundNumber = this.gameState.round
    }

    if (this.currentRoundNumber === this.gameState.round) {
      this.playerSwitchCount++
      if (this.playerSwitchCount > this.gameState.playerCount * 3) {
        console.log('🚨 检测到可能的无限循环，强制结束回合')
        console.log(`当前轮次: ${this.gameState.round}, 玩家切换次数: ${this.playerSwitchCount}`)
        this.scoringPhase()
        return
      }
    } else {
      // 新的一轮，重置计数器
      this.playerSwitchCount = 1
      this.currentRoundNumber = this.gameState.round
    }

    this.gameState.currentPlayer = (this.gameState.currentPlayer + 1) % this.gameState.playerCount

    console.log(`🔄 切换到玩家 ${this.gameState.currentPlayer + 1} (轮次${this.gameState.round}, 切换次数${this.playerSwitchCount})`)

    // 为新的当前玩家启动计时器
    this.startTurnTimer()

    // 检查当前玩家是否是AI
    this.checkAndExecuteAITurn()
  }

  // 检查并执行AI回合
  checkAndExecuteAITurn() {
    // 根据配置判断当前玩家是否是AI
    const humanCount = this.gameState.playerCount - this.gameState.aiCount
    const isAI = this.gameState.currentPlayer >= humanCount

    if (isAI) {
      console.log(`轮到AI玩家${this.gameState.currentPlayer + 1}`)
      // AI玩家不需要计时器
      this.clearTurnTimer()
      setTimeout(() => {
        this.executeAITurn()
      }, 1500)
    } else {
      console.log(`当前玩家${this.gameState.currentPlayer + 1}是人类，等待操作`)
      // 人类玩家需要计时器（如果还没有启动的话）
      if (!this.gameState.turnTimer) {
        this.startTurnTimer()
      }
    }
  }

  // 启动回合计时器
  startTurnTimer() {
    // 清除之前的计时器
    this.clearTurnTimer()

    // 只为人类玩家启动计时器
    const humanCount = this.gameState.playerCount - this.gameState.aiCount
    const isCurrentPlayerHuman = this.gameState.currentPlayer < humanCount

    if (!isCurrentPlayerHuman || this.gameState.phase !== 'collect') {
      console.log(`跳过计时器启动: 当前玩家${this.gameState.currentPlayer + 1}, 是人类: ${isCurrentPlayerHuman}, 阶段: ${this.gameState.phase}`)
      return
    }

    console.log(`为玩家${this.gameState.currentPlayer + 1}启动${this.gameState.turnTimeLimit/1000}秒计时器`)

    this.gameState.turnStartTime = Date.now()
    this.gameState.timeRemaining = this.gameState.turnTimeLimit

    // 设置超时处理
    this.gameState.turnTimer = setTimeout(() => {
      this.handleTurnTimeout()
    }, this.gameState.turnTimeLimit)

    // 启动计时器更新
    this.updateTimer()
  }

  // 清除回合计时器
  clearTurnTimer() {
    if (this.gameState.turnTimer) {
      clearTimeout(this.gameState.turnTimer)
      this.gameState.turnTimer = null
    }
    this.gameState.turnStartTime = null
    this.gameState.timeRemaining = this.gameState.turnTimeLimit
  }

  // 更新计时器显示
  updateTimer() {
    if (!this.gameState.turnStartTime || !this.gameState.turnTimer) {
      return
    }

    const elapsed = Date.now() - this.gameState.turnStartTime
    this.gameState.timeRemaining = Math.max(0, this.gameState.turnTimeLimit - elapsed)

    // 如果还有时间，继续更新
    if (this.gameState.timeRemaining > 0) {
      setTimeout(() => this.updateTimer(), 100) // 每100ms更新一次
    }
  }

  // 处理回合超时
  handleTurnTimeout() {
    console.log(`玩家${this.gameState.currentPlayer + 1}操作超时，AI自动帮助操作`)

    wx.showToast({
      title: '操作超时，AI自动帮助',
      icon: 'none',
      duration: 2000
    })

    // 清除计时器
    this.clearTurnTimer()

    // 清除任何选择状态
    this.clearSelectionState()

    // 使用AI逻辑自动执行一个操作
    this.executeTimeoutAIMove()
  }

  // 执行超时AI移动
  executeTimeoutAIMove() {
    console.log('执行超时AI自动操作')

    // 使用现有的AI逻辑选择最佳移动
    const bestMove = this.getBestAIMove()

    if (bestMove) {
      console.log(`超时AI选择: ${bestMove.type === 'factory' ? `工厂${bestMove.factoryIndex + 1}` : '中央区域'}的${bestMove.color}磁砖，放置到第${bestMove.lineIndex + 1}行`)

      // 延迟执行，让用户看到提示
      setTimeout(() => {
        if (bestMove.type === 'factory') {
          this.aiSelectFromFactory(bestMove.factoryIndex, bestMove.color, bestMove.lineIndex)
        } else {
          this.aiSelectFromCenter(bestMove.color, bestMove.lineIndex)
        }
      }, 1000)
    } else {
      console.log('超时AI无法找到合适的移动，跳过回合')
      // 如果找不到移动，直接进入下一个玩家
      setTimeout(() => {
        this.nextPlayer()
      }, 1000)
    }
  }

  // 检查轮次是否完成
  isRoundComplete() {
    // 检查所有工厂是否为空
    const allFactoriesEmpty = this.gameState.factories.every(factory => factory.length === 0)

    // 检查中央区域是否为空
    const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea
    const centerAreaEmpty = Array.isArray(centerTiles) && centerTiles.length === 0

    const isComplete = allFactoriesEmpty && centerAreaEmpty

    console.log('🔍 检查轮次是否完成:')
    console.log('  - 所有工厂为空:', allFactoriesEmpty)
    console.log('  - 中央区域为空:', centerAreaEmpty)
    console.log('  - 轮次完成:', isComplete)

    return isComplete
  }

  // 执行AI回合
  executeAITurn() {
    // 使用高级AI策略
    const bestMove = this.getBestAIMove()
    if (bestMove) {
      console.log(`AI选择: ${bestMove.type === 'factory' ? `工厂${bestMove.factoryIndex + 1}` : '中央区域'}的${bestMove.color}磁砖，放置到第${bestMove.lineIndex + 1}行，评分: ${bestMove.scorePotential.toFixed(2)}`)

      if (bestMove.type === 'factory') {
        this.aiSelectFromFactory(bestMove.factoryIndex, bestMove.color, bestMove.lineIndex)
      } else {
        this.aiSelectFromCenter(bestMove.color, bestMove.lineIndex)
      }
    } else {
      console.log('AI无法找到合适的移动')
    }
  }

  // AI从工厂选择磁砖（不显示界面）
  aiSelectFromFactory(factoryIndex, color, lineIndex) {
    const factory = this.gameState.factories[factoryIndex]
    const selectedTiles = factory.filter(tile => tile === color)
    const remainingTiles = factory.filter(tile => tile !== color)

    console.log(`AI从工厂${factoryIndex + 1}选择${selectedTiles.length}个${color}磁砖`)

    // 清空工厂
    this.gameState.factories[factoryIndex] = []

    // 剩余磁砖放入中央区域
    this.gameState.centerArea.push(...remainingTiles)

    // AI直接放置到指定位置
    this.aiPlaceTilesDirectly(selectedTiles, lineIndex)
  }

  // AI从中央区域选择磁砖（不显示界面）
  aiSelectFromCenter(color, lineIndex) {
    const selectedTiles = this.gameState.centerArea.filter(tile => tile === color)
    this.gameState.centerArea = this.gameState.centerArea.filter(tile => tile !== color)

    console.log(`AI从中央区域选择${selectedTiles.length}个${color}磁砖`)

    // 检查起始玩家磁砖
    if (this.gameState.firstPlayerTile) {
      this.gameState.firstPlayerTile = false
      const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
      currentPlayer.floorLine.push('first')
      currentPlayer.hasFirstPlayerTile = true
    }

    // AI直接放置到指定位置
    this.aiPlaceTilesDirectly(selectedTiles, lineIndex)
  }

  // AI直接放置磁砖到指定位置
  aiPlaceTilesDirectly(tiles, lineIndex) {
    console.log(`AI放置${tiles.length}个磁砖到第${lineIndex + 1}行`)

    // 延迟放置，让玩家看到AI的选择
    setTimeout(() => {
      if (lineIndex < 5) {
        // AI专用的放置逻辑，不触发起始玩家标记检查
        this.aiPlaceTilesOnPatternLine(tiles, lineIndex)
      } else {
        // 放置到地板线
        const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
        currentPlayer.floorLine.push(...tiles)
        console.log(`AI将${tiles.length}个磁砖放入地板线`)
        this.nextPlayer()
      }
    }, 500)
  }

  // AI专用的放置磁砖到图案线的方法
  aiPlaceTilesOnPatternLine(tiles, lineIndex) {
    const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
    const patternLine = currentPlayer.patternLines[lineIndex]
    const maxCapacity = lineIndex + 1

    console.log(`AI放置${tiles.length}个磁砖到第${lineIndex + 1}行`)

    // 检查是否可以放置
    if (patternLine.length > 0 && patternLine[0] !== tiles[0]) {
      // 不能放置，放入地板线
      console.log('AI: 颜色不匹配，放入地板线')
      currentPlayer.floorLine.push(...tiles)
      this.nextPlayer()
      return
    }

    // 计算可以放置的磁砖数量
    const availableSpace = maxCapacity - patternLine.length
    const tilesToPlace = Math.min(tiles.length, availableSpace)
    const excessTiles = tiles.length - tilesToPlace

    // 放置磁砖到图案线
    for (let i = 0; i < tilesToPlace; i++) {
      patternLine.push(tiles[0]) // 所有磁砖都是同一颜色
    }

    // 多余的磁砖放入地板线
    if (excessTiles > 0) {
      console.log(`AI: ${excessTiles}个多余磁砖放入地板线`)
      for (let i = 0; i < excessTiles; i++) {
        currentPlayer.floorLine.push(tiles[0])
      }
    }

    console.log(`AI成功放置${tilesToPlace}个磁砖到第${lineIndex + 1}行`)

    // 进入下一个玩家
    this.nextPlayer()
  }

  // AI自动放置磁砖（保留旧方法作为备用）
  aiPlaceTiles(tiles) {
    const aiPlayer = this.gameState.players[this.gameState.currentPlayer]

    // 简单AI策略：找到第一个可以放置的行
    let bestLine = 0
    for (let i = 0; i < 5; i++) {
      const patternLine = aiPlayer.patternLines[i]
      const maxCapacity = i + 1

      // 如果行是空的或者颜色匹配，且有空间
      if ((patternLine.length === 0 || patternLine[0] === tiles[0]) &&
          patternLine.length < maxCapacity) {
        bestLine = i
        break
      }
    }

    console.log(`AI选择放置到第${bestLine + 1}行`)

    // 延迟放置，让玩家看到AI的选择
    setTimeout(() => {
      // 使用AI专用的放置方法，避免起始玩家标记问题
      this.aiPlaceTilesOnPatternLine(tiles, bestLine)
    }, 500)
  }

  // 计分阶段
  scoringPhase() {
    console.log('开始计分阶段')

    // 开始逐个玩家的计分动画
    this.gameState.scoringPlayerIndex = 0
    this.scoreNextPlayer()
  }

  // 计分下一个玩家
  scoreNextPlayer() {
    if (this.gameState.scoringPlayerIndex >= this.gameState.players.length) {
      // 所有玩家计分完毕
      this.finishAllScoring()
      return
    }

    const player = this.gameState.players[this.gameState.scoringPlayerIndex]
    this.gameState.currentPlayer = this.gameState.scoringPlayerIndex

    console.log(`开始为玩家${this.gameState.scoringPlayerIndex + 1}计分`)

    // 为当前玩家计分
    this.scorePlayer(player)
  }

  // 完成所有玩家的计分
  finishAllScoring() {
    console.log('所有玩家计分完毕')

    // 调试：检查计分后的墙壁状态
    this.debugWallState(`第${this.gameState.round}轮计分后`)

    // 重置计分相关状态
    this.gameState.scoringPlayerIndex = 0
    this.gameState.scoringPlayer = undefined
    this.gameState.phase = 'collect'

    // 检查游戏结束
    if (this.checkGameEnd()) {
      // 开始最终计分动画
      this.calculateFinalScores()
    } else {
      this.prepareNextRound()
    }
  }

  // 玩家计分（基于lua.txt的标准算分逻辑，带动画）
  scorePlayer(player) {
    this.gameState.phase = 'scoring'
    this.gameState.scoringPlayer = this.gameState.currentPlayer
    this.gameState.scoringAnimations = []

    // 算分时自动切换到对应玩家的面板
    this.currentViewingPlayer = this.gameState.scoringPlayer
    console.log(`算分阶段：自动切换到玩家${this.gameState.scoringPlayer + 1}的面板`)

    // 开始计分动画序列
    this.startScoringAnimation(player, 0)
  }

  // 开始计分动画序列
  startScoringAnimation(player, lineIndex) {
    if (lineIndex >= 5) {
      // 所有行处理完毕，处理地板线扣分
      this.processFloorLinePenalty(player)
      return
    }

    const patternLine = player.patternLines[lineIndex]
    const requiredCount = lineIndex + 1

    if (patternLine.length === requiredCount) {
      const tileColor = patternLine[0]
      const wallRow = player.wall[lineIndex]
      const targetCell = wallRow.find(cell => cell.color === tileColor)

      if (targetCell && !targetCell.filled) {
        // 开始该行的计分动画
        this.animateRowScoring(player, lineIndex, () => {
          // 动画完成后处理下一行
          setTimeout(() => {
            this.startScoringAnimation(player, lineIndex + 1)
          }, 200)  // 从300ms减少到200ms
        })
        return
      }
    }

    // 该行无需计分，直接处理下一行
    this.startScoringAnimation(player, lineIndex + 1)
  }

  // 动画化行计分
  animateRowScoring(player, lineIndex, callback) {
    const patternLine = player.patternLines[lineIndex]
    const tileColor = patternLine[0]
    const wallRow = player.wall[lineIndex]
    const targetCell = wallRow.find(cell => cell.color === tileColor)
    const col = wallRow.indexOf(targetCell)

    // 放置磁砖到墙壁
    targetCell.filled = true
    console.log(`磁砖放置: ${player.name} 第${lineIndex + 1}行第${col + 1}列 (${tileColor})`)

    // 检查是否完成了整行
    const isRowComplete = player.wall[lineIndex].every(cell => cell.filled)
    if (isRowComplete) {
      console.log(`🎉 ${player.name} 完成了第${lineIndex + 1}行！`)
    }

    // 添加放置动画效果
    this.gameState.scoringAnimations.push({
      type: 'tilePlacement',
      row: lineIndex,
      col: col,
      color: tileColor,
      startTime: Date.now(),
      duration: 500
    })

    // 计算得分
    const score = this.calculateTileScore(player, lineIndex, col)

    setTimeout(() => {
      // 显示连接动画
      this.animateConnections(player, lineIndex, col, () => {
        // 显示得分动画并实时更新分数
        this.showScoreAnimation(score, lineIndex, col, player)

        console.log(`玩家${this.gameState.currentPlayer + 1}在第${lineIndex + 1}行得${score}分`)

        setTimeout(callback, 600)  // 从800ms减少到600ms
      })
    }, 400)  // 从500ms减少到400ms
  }

  // 动画化连接效果
  animateConnections(player, row, col, callback) {
    const connections = this.getConnectionTiles(player, row, col)

    // 为每个连接的磁砖添加高亮动画
    connections.forEach((tile, index) => {
      setTimeout(() => {
        this.gameState.scoringAnimations.push({
          type: 'tileHighlight',
          row: tile.row,
          col: tile.col,
          startTime: Date.now(),
          duration: 400  // 从600ms减少到400ms
        })
      }, index * 80)  // 从100ms减少到80ms
    })

    setTimeout(callback, connections.length * 80 + 400)  // 相应调整总时间
  }

  // 获取连接的磁砖位置
  getConnectionTiles(player, row, col) {
    const connections = []

    // 检查四个方向
    const directions = [
      [-1, 0], // 上
      [1, 0],  // 下
      [0, -1], // 左
      [0, 1]   // 右
    ]

    directions.forEach(([rowInc, colInc]) => {
      let r = row + rowInc
      let c = col + colInc

      while (r >= 0 && r < 5 && c >= 0 && c < 5) {
        if (player.wall[r][c].filled) {
          connections.push({ row: r, col: c })
          r += rowInc
          c += colInc
        } else {
          break
        }
      }
    })

    return connections
  }

  // 显示得分动画
  showScoreAnimation(score, row, col, player) {
    // 播放得分音效
    if (window.gameApp) {
      window.gameApp.playSound('scoreUpdate')
    }

    // 添加得分弹出动画（加快速度）
    this.gameState.scoringAnimations.push({
      type: 'scorePopup',
      score: score,
      row: row,
      col: col,
      startTime: Date.now(),
      duration: 600  // 从1000ms减少到600ms
    })

    // 添加分数实时更新动画（加快速度）
    this.gameState.scoringAnimations.push({
      type: 'scoreUpdate',
      player: player,
      oldScore: player.score,
      newScore: player.score + score,
      startTime: Date.now(),
      duration: 500,  // 从800ms减少到500ms
      scoreToAdd: score  // 记录要添加的分数，用于动画结束时更新
    })

    // 不立即更新玩家分数，等动画结束时更新
    // player.score += score  // 注释掉立即更新
  }

  // 处理地板线扣分
  processFloorLinePenalty(player) {
    const floorPenalties = [-1, -1, -2, -2, -2, -3, -3]
    let totalPenalty = 0

    // 为每个有磁砖的地板线槽位创建单独的扣分动画
    const floorLineAnimations = []
    for (let i = 0; i < Math.min(player.floorLine.length, 7); i++) {
      const penalty = floorPenalties[i]
      totalPenalty += penalty

      // 为每个槽位创建扣分动画
      floorLineAnimations.push({
        type: 'floorSlotPenalty',
        penalty: penalty,
        slotIndex: i,
        player: player,
        startTime: Date.now() + i * 100,  // 错开动画时间
        duration: 800
      })
    }

    if (totalPenalty < 0) {
      // 添加所有地板线槽位的扣分动画
      this.gameState.scoringAnimations.push(...floorLineAnimations)

      // 添加分数更新动画（扣分）
      this.gameState.scoringAnimations.push({
        type: 'scoreUpdate',
        player: player,
        oldScore: player.score,
        newScore: Math.max(0, player.score + totalPenalty),
        startTime: Date.now() + 400,  // 在槽位动画开始后延迟开始
        duration: 600,
        scoreToAdd: totalPenalty  // 记录要添加的分数（负数）
      })

      setTimeout(() => {
        console.log(`玩家${this.gameState.currentPlayer + 1}地板线扣分动画开始`)

        // 将地板线磁砖放入废弃堆（除了起始玩家磁砖）
        const tilesToDiscard = player.floorLine.filter(tile => tile !== 'first')
        this.gameState.discardPile.push(...tilesToDiscard)
        console.log(`将${tilesToDiscard.length}个地板线磁砖放入废弃堆`)

        // 清空地板线
        player.floorLine = []

        // 计分动画结束
        this.finishScoringAnimation()
      }, 1200)  // 增加延迟时间，让所有槽位动画都能播放
    } else {
      // 无扣分，直接结束
      // 将地板线磁砖放入废弃堆（除了起始玩家磁砖）
      const tilesToDiscard = player.floorLine.filter(tile => tile !== 'first')
      this.gameState.discardPile.push(...tilesToDiscard)
      console.log(`将${tilesToDiscard.length}个地板线磁砖放入废弃堆`)

      player.floorLine = []
      this.finishScoringAnimation()
    }
  }

  // 完成计分动画
  finishScoringAnimation() {
    setTimeout(() => {
      this.gameState.scoringAnimations = []

      // 清空当前玩家的所有已完成准备区
      const currentPlayer = this.gameState.players[this.gameState.scoringPlayerIndex]
      for (let lineIndex = 0; lineIndex < 5; lineIndex++) {
        const patternLine = currentPlayer.patternLines[lineIndex]
        const requiredCount = lineIndex + 1

        if (patternLine.length === requiredCount) {
          // 将多余的磁砖放入废弃堆（除了放到墙壁上的那一个）
          const excessTiles = patternLine.slice(1) // 第一个磁砖放到墙壁，其余的丢弃
          this.gameState.discardPile.push(...excessTiles)
          console.log(`将${excessTiles.length}个多余磁砖放入废弃堆`)

          // 清空准备区
          currentPlayer.patternLines[lineIndex] = []
        }
      }

      // 继续下一个玩家的计分
      this.gameState.scoringPlayerIndex++
      this.scoreNextPlayer()
    }, 500)
  }

  // 计算磁砖得分（基于lua.txt的getScore算法）
  calculateTileScore(player, row, col) {
    const counts = [
      this.countDirection(player, row, -1, col, 0), // 向上
      this.countDirection(player, row, 1, col, 0),  // 向下
      this.countDirection(player, row, 0, col, -1), // 向左
      this.countDirection(player, row, 0, col, 1)   // 向右
    ]

    let score = 0
    for (let count of counts) {
      score += count
    }

    // lua.txt的核心算分逻辑：如果同时有水平和垂直连接，额外得2分；否则得1分
    if ((counts[0] > 0 || counts[1] > 0) && (counts[2] > 0 || counts[3] > 0)) {
      score += 2 // 有水平和垂直连接
    } else {
      score += 1 // 基础1分
    }

    return score
  }

  // 计算指定方向的连接数量（基于lua.txt的countDirection算法）
  countDirection(player, row, rowInc, col, colInc) {
    let count = 0
    row += rowInc
    col += colInc

    while (row >= 0 && row < 5 && col >= 0 && col < 5) {
      if (player.wall[row][col].filled) {
        count++
        row += rowInc
        col += colInc
      } else {
        break
      }
    }

    return count
  }

  // 检查游戏结束
  checkGameEnd() {
    const gameEnded = this.gameState.players.some((player, playerIndex) => {
      const hasFullRow = player.wall.some((row, rowIndex) => {
        const isRowFull = row.every(cell => cell.filled)
        if (isRowFull) {
          console.log(`游戏结束：玩家${playerIndex + 1}完成了第${rowIndex + 1}行`)
        }
        return isRowFull
      })
      return hasFullRow
    })

    if (!gameEnded) {
      console.log(`第${this.gameState.round}轮结束，没有玩家完成整行，继续游戏`)
    }

    return gameEnded
  }

  // 调试：检查墙壁状态
  debugWallState(context) {
    console.log(`=== 墙壁状态检查 (${context}) ===`)
    this.gameState.players.forEach((player, playerIndex) => {
      console.log(`${player.name}:`)
      player.wall.forEach((row, rowIndex) => {
        const filledCount = row.filter(cell => cell.filled).length
        const isFullRow = row.every(cell => cell.filled)
        console.log(`  第${rowIndex + 1}行: ${filledCount}/5 ${isFullRow ? '✅完整' : '⭕未完整'}`)
      })
    })
    console.log('=== 墙壁状态检查结束 ===')
  }

  // 最终计分（基于lua.txt的finalScoring逻辑）
  calculateFinalScores() {
    console.log('开始最终计分')

    // 设置最终计分阶段
    this.gameState.phase = 'finalScoring'
    this.gameState.finalScoringPlayer = 0
    this.gameState.finalScoringAnimations = []

    // 开始第一个玩家的最终计分动画
    this.startPlayerFinalScoring(0)
  }

  // 开始指定玩家的最终计分动画
  startPlayerFinalScoring(playerIndex) {
    if (playerIndex >= this.gameState.players.length) {
      // 所有玩家计分完成，显示游戏结束
      this.gameState.phase = 'end'
      this.showGameEnd()
      return
    }

    const player = this.gameState.players[playerIndex]
    this.gameState.finalScoringPlayer = playerIndex
    console.log(`开始玩家${playerIndex + 1}的最终计分`)

    // 最终计分时自动切换到对应玩家的面板
    this.currentViewingPlayer = playerIndex
    console.log(`最终计分阶段：自动切换到玩家${playerIndex + 1}的面板`)

    let animationDelay = 0
    const animationInterval = 1000 // 每个动画间隔1秒

    // 完整行奖励（每行2分）
    for (let row = 0; row < 5; row++) {
      if (player.wall[row].every(cell => cell.filled)) {
        setTimeout(() => {
          this.showFinalScoreAnimation(2, `完成第${row + 1}行`, player, 'row', row)
        }, animationDelay)
        animationDelay += animationInterval
        console.log(`玩家${playerIndex + 1}完成第${row + 1}行，获得2分奖励`)
      }
    }

    // 完整列奖励（每列7分）
    for (let col = 0; col < 5; col++) {
      if (player.wall.every(row => row[col].filled)) {
        setTimeout(() => {
          this.showFinalScoreAnimation(7, `完成第${col + 1}列`, player, 'column', col)
        }, animationDelay)
        animationDelay += animationInterval
        console.log(`玩家${playerIndex + 1}完成第${col + 1}列，获得7分奖励`)
      }
    }

    // 同色收集奖励（每种颜色5个得10分）
    const colors = GAME_CONFIG.COLORS // 使用配置中的颜色：['blue', 'yellow', 'red', 'black', 'teal']
    colors.forEach(color => {
      let colorCount = 0
      player.wall.forEach(row => {
        row.forEach(cell => {
          if (cell.color === color && cell.filled) {
            colorCount++
          }
        })
      })
      if (colorCount === 5) {
        setTimeout(() => {
          this.showFinalScoreAnimation(10, `收集5个${color}磁砖`, player, 'color', color)
        }, animationDelay)
        animationDelay += animationInterval
        console.log(`玩家${playerIndex + 1}收集5个${color}磁砖，获得10分奖励`)
      }
    })

    // 如果没有任何奖励，直接进入下一个玩家
    if (animationDelay === 0) {
      setTimeout(() => {
        this.startPlayerFinalScoring(playerIndex + 1)
      }, 500)
    } else {
      // 等待所有动画完成后进入下一个玩家
      setTimeout(() => {
        this.startPlayerFinalScoring(playerIndex + 1)
      }, animationDelay + 500)
    }
  }

  // 显示最终计分动画
  showFinalScoreAnimation(score, description, player, type, index) {
    console.log(`${player.name}${description}，获得${score}分`)

    // 设置当前计分玩家，让高亮动画能正确显示
    const playerIndex = this.gameState.players.indexOf(player)
    this.gameState.scoringPlayer = playerIndex

    // 检查是否为多人游戏，如果是则不更新分数（服务端已经计算过了）
    const isMultiplayerGame = this.isMultiplayer || (this.multiplayerManager && this.multiplayerManager.connected)

    // 根据奖励类型添加相应的高亮动画和分数弹出动画（最终结算使用更慢的节奏）
    if (type === 'row') {
      // 行奖励：高亮整行，在中间位置显示总分数
      const filledTiles = []
      for (let col = 0; col < 5; col++) {
        if (player.wall[index][col].filled) {
          filledTiles.push({ row: index, col: col })
        }
      }

      filledTiles.forEach((tile, tileIndex) => {
        const startTime = Date.now() + tileIndex * 150

        // 高亮动画
        this.gameState.scoringAnimations.push({
          type: 'tileHighlight',
          row: tile.row,
          col: tile.col,
          startTime: startTime,
          duration: 800
        })
      })

      // 只在中间位置显示一次总分数
      if (filledTiles.length > 0) {
        const middleIndex = Math.floor(filledTiles.length / 2)
        const middleTile = filledTiles[middleIndex]

        this.gameState.scoringAnimations.push({
          type: 'scorePopup',
          score: score, // 显示总分数
          row: middleTile.row,
          col: middleTile.col,
          startTime: Date.now() + middleIndex * 150 + 200, // 在中间磁砖高亮后显示
          duration: 800
        })
      }
    } else if (type === 'column') {
      // 列奖励：高亮整列，在中间位置显示总分数
      const filledTiles = []
      for (let row = 0; row < 5; row++) {
        if (player.wall[row][index].filled) {
          filledTiles.push({ row: row, col: index })
        }
      }

      filledTiles.forEach((tile, tileIndex) => {
        const startTime = Date.now() + tileIndex * 150

        // 高亮动画
        this.gameState.scoringAnimations.push({
          type: 'tileHighlight',
          row: tile.row,
          col: tile.col,
          startTime: startTime,
          duration: 800
        })
      })

      // 只在中间位置显示一次总分数
      if (filledTiles.length > 0) {
        const middleIndex = Math.floor(filledTiles.length / 2)
        const middleTile = filledTiles[middleIndex]

        this.gameState.scoringAnimations.push({
          type: 'scorePopup',
          score: score, // 显示总分数
          row: middleTile.row,
          col: middleTile.col,
          startTime: Date.now() + middleIndex * 150 + 200, // 在中间磁砖高亮后显示
          duration: 800
        })
      }
    } else if (type === 'color') {
      // 颜色奖励：高亮所有该颜色的磁砖，在第一个位置显示总分数
      const targetColor = index
      const filledTiles = []

      console.log(`最终计分：高亮所有${targetColor}颜色的磁砖`)

      for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
          const cell = player.wall[row][col]
          if (cell.filled && cell.color === targetColor) {
            filledTiles.push({ row: row, col: col })
          }
        }
      }

      filledTiles.forEach((tile, tileIndex) => {
        const startTime = Date.now() + tileIndex * 120

        console.log(`找到${targetColor}磁砖在位置(${tile.row}, ${tile.col})`)

        // 高亮动画
        this.gameState.scoringAnimations.push({
          type: 'tileHighlight',
          row: tile.row,
          col: tile.col,
          startTime: startTime,
          duration: 800
        })
      })

      // 只在第一个磁砖位置显示一次总分数
      if (filledTiles.length > 0) {
        const firstTile = filledTiles[0]

        this.gameState.scoringAnimations.push({
          type: 'scorePopup',
          score: score, // 显示总分数
          row: firstTile.row,
          col: firstTile.col,
          startTime: Date.now() + 200, // 在第一个磁砖高亮后显示
          duration: 800
        })
      }

      console.log(`为${targetColor}颜色创建了${filledTiles.length}个高亮动画和1个分数弹出动画`)
    }

    // 现在多人游戏也真实更新分数，让动画有意义
    player.score += score;
    console.log(`${player.name}分数已更新: +${score}分，总分: ${player.score}分`);

    this.gameState.scoringAnimations.push({
      type: 'scoreUpdate',
      player: player,
      oldScore: player.score - score, // 使用更新前的分数作为起始值
      newScore: player.score, // 使用更新后的分数作为结束值
      startTime: Date.now() + 300,
      duration: 800,
      scoreToAdd: score
    });
  }

  // 准备下一轮
  prepareNextRound() {
    this.gameState.round++
    this.gameState.phase = 'collect'
    this.gameState.firstPlayerTile = true
    this.gameState.centerArea = []

    // 重置所有选择状态
    this.gameState.showingFactorySelection = false
    this.gameState.showingCenterSelection = false
    this.gameState.selectedFactory = null
    this.gameState.selectedTiles = null
    this.gameState.selectedColor = null
    this.gameState.selectedFactoryIndex = undefined
    this.gameState.selectedFactoryTiles = null
    this.gameState.remainingFactoryTiles = null
    this.gameState.selectedCenterTiles = null

    // 重置计分相关状态
    this.gameState.scoringPlayer = undefined
    this.gameState.scoringPlayerIndex = 0
    this.gameState.scoringAnimations = []

    // 重新填充工厂
    this.fillFactories()

    // 找到拥有起始玩家磁砖的玩家作为下一轮的起始玩家
    let nextFirstPlayer = 0
    for (let i = 0; i < this.gameState.players.length; i++) {
      if (this.gameState.players[i].hasFirstPlayerTile) {
        nextFirstPlayer = i
        this.gameState.players[i].hasFirstPlayerTile = false // 重置标记
        break
      }
    }

    this.gameState.currentPlayer = nextFirstPlayer

    console.log(`开始第${this.gameState.round}轮，玩家${nextFirstPlayer + 1}先手`)

    // 调试：检查新轮开始时的墙壁状态
    this.debugWallState(`第${this.gameState.round}轮开始`)

    // 检查新轮的先手玩家是否是AI
    this.checkAndExecuteAITurn()
  }

  // 显示游戏结束
  showGameEnd() {
    // 最终计分已经通过动画系统完成，这里只显示结果
    const winner = this.gameState.players.reduce((prev, current) =>
      prev.score > current.score ? prev : current
    )

    console.log(`游戏结束！获胜者：${winner.name}，得分：${winner.score}`)

    // 显示所有玩家的最终得分
    let scoreText = '最终得分：\n'
    this.gameState.players.forEach((player, index) => {
      scoreText += `${player.name}: ${player.score}分\n`
    })

    wx.showModal({
      title: '游戏结束',
      content: `获胜者：${winner.name}\n\n${scoreText}`,
      showCancel: false,
      confirmText: '重新开始',
      success: () => {
        this.initGameState()
      }
    })
  }

  // 紧急重置游戏（用于修复卡住的情况）
  emergencyReset() {
    console.log('🚨 执行紧急重置')

    // 清理所有计时器
    this.clearTurnTimer()
    if (this.gameState.turnTimer) {
      clearTimeout(this.gameState.turnTimer)
      this.gameState.turnTimer = null
    }

    // 重置循环检测计数器
    this.playerSwitchCount = 0
    this.currentRoundNumber = 0

    // 显示重置对话框
    wx.showModal({
      title: '游戏异常',
      content: '检测到游戏可能卡住了，是否重新开始游戏？',
      confirmText: '重新开始',
      cancelText: '强制计分',
      success: (res) => {
        if (res.confirm) {
          console.log('用户选择重新开始游戏')
          this.initGameState()
        } else {
          console.log('用户选择强制计分，尝试强制进入计分阶段')
          // 强制清空所有工厂和中央区域
          this.gameState.factories.forEach(factory => factory.length = 0)
          const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea
          if (Array.isArray(centerTiles)) {
            centerTiles.length = 0
          }
          this.gameState.centerArea = []

          // 强制进入计分阶段
          this.gameState.phase = 'scoring'
          this.scoringPhase()
        }
      }
    })
  }

  // 辅助方法：获取工厂矩形（支持多行布局）
  getFactoryRect(index) {
    const factorySize = 60
    const margin = 5
    const safeAreaTop = 40  // 与渲染保持一致的安全区域偏移
    const startY = 100 + safeAreaTop

    // 计算每行可以放置的工厂数量（与renderFactories保持一致）
    const { width } = this.getLogicalSize()
    const maxFactoriesPerRow = Math.floor((width - 40) / (factorySize + margin))

    const row = Math.floor(index / maxFactoriesPerRow)
    const col = index % maxFactoriesPerRow
    const factoriesInThisRow = Math.min(maxFactoriesPerRow, this.gameState.factories.length - row * maxFactoriesPerRow)

    // 计算当前行的起始X位置（居中对齐）
    const rowWidth = factoriesInThisRow * (factorySize + margin) - margin
    const rowStartX = (width - rowWidth) / 2

    const x = rowStartX + col * (factorySize + margin)
    const y = startY + row * (factorySize + margin + 10)

    return {
      x: x,
      y: y,
      width: factorySize,
      height: factorySize
    }
  }

  // 辅助方法：获取中央区域矩形
  getCenterRect() {
    const safeAreaTop = 40  // 与渲染保持一致的安全区域偏移

    // 动态计算中央区域位置，与renderCenterArea保持一致
    const factorySize = 60
    const margin = 5
    const factoryStartY = 100 + safeAreaTop
    const { width } = this.getLogicalSize()
    const maxFactoriesPerRow = Math.floor((width - 40) / (factorySize + margin))
    const factoryRows = Math.ceil(this.gameState.factories.length / maxFactoriesPerRow)
    const factoryEndY = factoryStartY + factoryRows * (factorySize + margin + 10) - 10

    // 中央区域位置在工厂下方
    const centerY = factoryEndY + 30

    return {
      x: 0,
      y: centerY - 10,  // 稍微扩大点击区域
      width: this.getLogicalSize().width,
      height: 100
    }
  }

  // 辅助方法：获取中央颜色矩形
  getCenterColorRect(color) {
    const centerTiles = this.groupCenterTiles()
    const colorIndex = centerTiles.findIndex(group => group.color === color)
    const x = this.getLogicalSize().width / 2 - (centerTiles.length * 35) / 2 + colorIndex * 35

    return {
      x: x,
      y: 230,
      width: 25,
      height: 25
    }
  }

  // 渲染计分动画
  renderScoringAnimations() {
    const currentTime = Date.now()

    // 检查并处理已完成的动画
    const completedAnimations = []
    this.gameState.scoringAnimations = this.gameState.scoringAnimations.filter(animation => {
      const elapsed = currentTime - animation.startTime
      const isCompleted = elapsed >= animation.duration

      if (isCompleted) {
        completedAnimations.push(animation)
      }

      return !isCompleted
    })

    // 处理已完成的动画
    completedAnimations.forEach(animation => {
      if (animation.type === 'scoreUpdate') {
        // 分数更新动画完成（分数已经在动画开始时更新了，这里只是记录日志）
        console.log(`分数动画完成，玩家 ${animation.player.name} 当前分数: ${animation.player.score}`)
      } else if (animation.type === 'tilePlacement' && animation.onComplete) {
        // 瓷砖放置动画完成时的回调
        animation.onComplete()
      }
    })

    // 渲染每个动画
    this.gameState.scoringAnimations.forEach(animation => {
      const elapsed = currentTime - animation.startTime
      const progress = Math.max(0, Math.min(elapsed / animation.duration, 1)) // 确保progress在0-1范围内

      switch (animation.type) {
        case 'tilePlacement':
          this.renderTilePlacementAnimation(animation, progress)
          break
        case 'tileHighlight':
          this.renderTileHighlightAnimation(animation, progress)
          break
        case 'scorePopup':
          this.renderScorePopupAnimation(animation, progress)
          break
        case 'floorPenalty':
          this.renderFloorPenaltyAnimation(animation, progress)
          break
        case 'floorSlotPenalty':
          this.renderFloorSlotPenaltyAnimation(animation, progress)
          break
        case 'scoreUpdate':
          this.renderScoreUpdateAnimation(animation, progress)
          break
        case 'ratingChange':
          this.renderRatingChangeAnimation(animation, progress)
          break
      }
    })
  }

  // 渲染磁砖放置动画
  renderTilePlacementAnimation(animation, progress) {
    if (this.gameState.scoringPlayer === undefined) return

    const player = this.gameState.players[this.gameState.scoringPlayer]
    const { row, col, color } = animation

    // 计算起始位置（准备区）和目标位置（墙壁）
    const startPos = this.getPatternLinePosition(player, row)
    const endPos = this.getWallTilePosition(player, row, col)
    if (!startPos || !endPos) return

    // 使用缓动函数计算当前位置
    const easeProgress = this.easeInOutCubic(progress)
    const currentX = startPos.x + (endPos.x - startPos.x) * easeProgress
    const currentY = startPos.y + (endPos.y - startPos.y) * easeProgress

    // 磁砖大小保持不变
    const size = 18

    this.ctx.save()

    // 绘制移动的磁砖
    this.ctx.fillStyle = GAME_CONFIG.COLOR_MAP[color] || '#6c757d'
    this.ctx.fillRect(
      currentX - size/2,
      currentY - size/2,
      size,
      size
    )

    // 添加边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.lineWidth = 1
    this.ctx.strokeRect(
      currentX - size/2,
      currentY - size/2,
      size,
      size
    )

    // 添加移动轨迹效果
    if (progress > 0.1) {
      this.ctx.globalAlpha = 0.3
      this.ctx.strokeStyle = GAME_CONFIG.COLOR_MAP[color] || '#6c757d'
      this.ctx.lineWidth = 2
      this.ctx.beginPath()
      this.ctx.moveTo(startPos.x, startPos.y)
      this.ctx.lineTo(currentX, currentY)
      this.ctx.stroke()
    }

    this.ctx.restore()
  }

  // 缓动函数
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }

  // 获取准备区磁砖位置
  getPatternLinePosition(player, row) {
    const playerIndex = this.gameState.players.indexOf(player)
    const panelPos = this.getPlayerPanelPosition(playerIndex)
    if (!panelPos) return null

    // 使用与实际渲染相同的参数（与renderPlayerGameArea一致）
    const tileSize = 20
    const spacing = 3
    const lineSpacing = tileSize + spacing + 3  // 与renderPatternLinesInArea一致

    // 计算准备区位置（与renderPlayerGameArea一致）
    // 使用内容区域的位置
    const patternStartX = panelPos.contentX + 10  // 内容区域 + 10px边距
    const patternStartY = panelPos.y + 35 + 10   // 面板顶部 + 标题区域 + 边距
    const lineY = patternStartY + row * lineSpacing

    // 返回该行最后一个磁砖的位置
    const patternLine = player.patternLines[row]
    const tileCount = patternLine.length
    const lastTileX = patternStartX + (tileCount - 1) * (tileSize + spacing) + tileSize/2
    const tileY = lineY + tileSize/2

    return {
      x: lastTileX,
      y: tileY
    }
  }

  // 渲染磁砖高亮动画
  renderTileHighlightAnimation(animation, progress) {
    if (this.gameState.scoringPlayer === undefined) return

    const player = this.gameState.players[this.gameState.scoringPlayer]
    const { row, col } = animation

    const wallPos = this.getWallTilePosition(player, row, col)
    if (!wallPos) return

    // 脉冲效果
    const pulse = Math.sin(progress * Math.PI * 4) * 0.5 + 0.5
    const alpha = 0.3 + (pulse * 0.4)

    this.ctx.save()
    this.ctx.globalAlpha = alpha
    this.ctx.fillStyle = '#ffd700' // 金色高亮
    this.ctx.fillRect(wallPos.x - 12, wallPos.y - 12, 24, 24)
    this.ctx.restore()
  }



  // 渲染得分弹出动画
  renderScorePopupAnimation(animation, progress) {
    if (this.gameState.scoringPlayer === undefined) return

    const player = this.gameState.players[this.gameState.scoringPlayer]
    const { score, row, col } = animation

    const wallPos = this.getWallTilePosition(player, row, col)
    if (!wallPos) return

    // 向上飞出效果
    const offsetY = -30 * progress
    const alpha = 1 - progress
    const scale = 1 + (progress * 0.5)

    this.ctx.save()
    this.ctx.globalAlpha = alpha
    this.ctx.fillStyle = '#27ae60'
    this.ctx.font = `bold ${16 * scale}px Arial`
    this.ctx.textAlign = 'center'
    this.ctx.fillText(`+${score}`, wallPos.x, wallPos.y + offsetY)
    this.ctx.restore()
  }

  // 渲染地板线扣分动画（旧版本，保留兼容性）
  renderFloorPenaltyAnimation(animation, progress) {
    if (this.gameState.scoringPlayer === undefined) return

    const player = this.gameState.players[this.gameState.scoringPlayer]
    const { penalty } = animation

    // 在玩家面板上方显示扣分
    const playerIndex = this.gameState.scoringPlayer
    const panelPos = this.getPlayerPanelPosition(playerIndex)
    if (!panelPos) return

    const offsetY = -40 * progress
    const alpha = 1 - progress
    const scale = 1 + (progress * 0.3)

    this.ctx.save()
    this.ctx.globalAlpha = alpha
    this.ctx.fillStyle = '#e74c3c'
    this.ctx.font = `bold ${18 * scale}px Arial`
    this.ctx.textAlign = 'center'
    this.ctx.fillText(`${penalty}`, panelPos.x + panelPos.width/2, panelPos.y + offsetY)
    this.ctx.restore()
  }

  // 渲染地板线槽位扣分动画
  renderFloorSlotPenaltyAnimation(animation, progress) {
    if (this.gameState.scoringPlayer === undefined) return

    const player = this.gameState.players[this.gameState.scoringPlayer]
    const { penalty, slotIndex } = animation

    // 获取地板线槽位的位置
    const slotPos = this.getFloorLineSlotPosition(player, slotIndex)
    if (!slotPos) return

    // 动画效果：从槽位位置向上移动并淡出
    const offsetY = -30 * progress  // 向上移动30px
    const alpha = 1 - progress
    const scale = 1 + (progress * 0.2)  // 轻微放大效果

    this.ctx.save()
    this.ctx.globalAlpha = alpha
    this.ctx.fillStyle = '#e74c3c'
    this.ctx.font = `bold ${14 * scale}px Arial`
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'

    // 在槽位上方显示扣分
    this.ctx.fillText(`${penalty}`, slotPos.x, slotPos.y + offsetY - 15)

    this.ctx.restore()
  }

  // 渲染分数更新动画
  renderScoreUpdateAnimation(animation, progress) {
    if (this.gameState.scoringPlayer === undefined) return

    const { player, oldScore, newScore, isMultiplayerAnimation, scoreToAdd } = animation
    const playerIndex = this.gameState.players.indexOf(player)
    const panelPos = this.getPlayerPanelPosition(playerIndex)
    if (!panelPos) return

    // 对于多人游戏动画，只显示动画效果，不改变实际分数
    let currentScore
    if (isMultiplayerAnimation) {
      // 多人游戏：显示当前分数，动画只是视觉效果
      currentScore = player.score
    } else {
      // 单人游戏：计算当前显示的分数（从旧分数渐变到新分数）
      currentScore = Math.round(oldScore + (newScore - oldScore) * progress)
    }

    // 在玩家面板右上角显示分数（与实际分数显示位置一致）
    const scoreX = panelPos.x + panelPos.width - 10
    const scoreY = panelPos.y + 18

    this.ctx.save()

    // 背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(scoreX - 40, scoreY - 12, 35, 20)

    // 分数文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'right'
    this.ctx.fillText(`${currentScore}`, scoreX - 5, scoreY + 3)

    // 如果正在增加分数，显示+号效果
    if (progress < 1) {
      let scoreIncrease
      if (isMultiplayerAnimation) {
        // 多人游戏：显示动画中的分数增加值
        scoreIncrease = scoreToAdd || 0
      } else {
        // 单人游戏：计算分数差值
        scoreIncrease = newScore - oldScore
      }

      if (scoreIncrease > 0) {
        this.ctx.fillStyle = '#27ae60'
        this.ctx.font = 'bold 12px Arial'
        this.ctx.fillText(`+${scoreIncrease}`, scoreX - 5, scoreY - 15)
      }
    }

    this.ctx.restore()
  }

  // 渲染积分变化动画
  renderRatingChangeAnimation(animation, progress) {
    const { width, height } = this.getLogicalSize()

    // 动画分为三个阶段：淡入(0-0.2)、显示(0.2-0.8)、淡出(0.8-1.0)
    let alpha = 1
    if (progress < 0.2) {
      alpha = progress / 0.2
    } else if (progress > 0.8) {
      alpha = 1 - (progress - 0.8) / 0.2
    }

    this.ctx.save()
    this.ctx.globalAlpha = alpha

    // 背景遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(0, 0, width, height)

    // 主要内容区域
    const contentWidth = Math.min(width * 0.8, 400)
    const contentHeight = Math.min(height * 0.6, 300)
    const contentX = (width - contentWidth) / 2
    const contentY = (height - contentHeight) / 2

    // 内容背景
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(contentX, contentY, contentWidth, contentHeight)

    // 边框
    this.ctx.strokeStyle = '#cccccc'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(contentX, contentY, contentWidth, contentHeight)

    // 标题
    this.ctx.fillStyle = '#333333'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('排位赛结束', contentX + contentWidth / 2, contentY + 50)

    // 排名
    this.ctx.fillStyle = '#666666'
    this.ctx.font = '18px Arial'
    this.ctx.fillText(`排名: 第${animation.placement}名`, contentX + contentWidth / 2, contentY + 90)

    // 积分变化
    const isPositive = animation.ratingChange > 0
    const changeText = isPositive ? `+${animation.ratingChange}` : `${animation.ratingChange}`

    this.ctx.fillStyle = isPositive ? '#4CAF50' : '#f44336'
    this.ctx.font = 'bold 32px Arial'
    this.ctx.fillText(`积分变化: ${changeText}`, contentX + contentWidth / 2, contentY + 140)

    // 新积分
    this.ctx.fillStyle = '#333333'
    this.ctx.font = '20px Arial'
    this.ctx.fillText(`当前积分: ${animation.newRating}`, contentX + contentWidth / 2, contentY + 180)

    // 段位信息
    if (animation.newTier) {
      this.ctx.fillStyle = '#666666'
      this.ctx.font = '16px Arial'
      const tierText = `${animation.newTier.tierName} ${animation.newTier.division || ''}`
      this.ctx.fillText(`当前段位: ${tierText}`, contentX + contentWidth / 2, contentY + 210)
    }

    // 段位提升提示
    if (animation.tierChanged) {
      this.ctx.fillStyle = '#FFD700'
      this.ctx.font = 'bold 18px Arial'
      this.ctx.fillText('🎉 段位提升！', contentX + contentWidth / 2, contentY + 240)
    }

    this.ctx.restore()
  }

  // 获取地板线槽位位置
  getFloorLineSlotPosition(player, slotIndex) {
    const playerIndex = this.gameState.players.indexOf(player)
    const panelPos = this.getPlayerPanelPosition(playerIndex)
    if (!panelPos) return null

    // 使用与实际渲染相同的参数（与renderPlayerGameArea一致）
    const tileSize = 20
    const spacing = 3

    // 计算地板线位置（与renderPlayerGameArea一致）
    const floorY = panelPos.y + 35 + 10 + 5 * (tileSize + spacing + 3) + 10  // 准备区下方
    const floorStartX = panelPos.contentX + 10  // 与准备区对齐

    return {
      x: floorStartX + slotIndex * (tileSize + spacing) + tileSize / 2,
      y: floorY + tileSize / 2
    }
  }

  // 获取墙壁磁砖位置
  getWallTilePosition(player, row, col) {
    const playerIndex = this.gameState.players.indexOf(player)
    const panelPos = this.getPlayerPanelPosition(playerIndex)
    if (!panelPos) return null

    // 使用与实际渲染相同的参数（与renderPlayerGameArea一致）
    const tileSize = 20
    const spacing = 3
    const lineSpacing = tileSize + spacing + 3  // 与renderPatternLinesInArea一致

    // 计算墙壁位置（与renderPlayerGameArea一致）
    // 使用内容区域的宽度进行计算
    const availableWidth = panelPos.contentWidth - 20 // 内容区域减去边距
    const patternAreaWidth = Math.min(140, availableWidth * 0.45)
    const gapBetweenAreas = Math.min(20, availableWidth * 0.1)
    const wallStartX = Math.min(
      panelPos.contentX + 10 + patternAreaWidth + gapBetweenAreas,
      panelPos.contentX + panelPos.contentWidth - Math.min(140, availableWidth * 0.45) - 10
    )
    const wallStartY = panelPos.y + 35 + 10  // 与准备区对齐

    return {
      x: wallStartX + col * (tileSize + spacing) + tileSize/2,
      y: wallStartY + row * lineSpacing + tileSize/2
    }
  }

  // 获取玩家面板位置（单面板布局）
  getPlayerPanelPosition(playerIndex) {
    // 现在使用单面板布局，所有玩家共享同一个面板区域
    // 返回与renderSinglePanelLayout一致的位置
    const { width, height } = this.getLogicalSize()
    const playerCount = this.gameState.players.length

    // 计算面板位置（与renderSinglePanelLayout保持一致）
    const centerRect = this.getCenterRect()
    const centerEndY = centerRect.y + centerRect.height
    const minStartY = centerEndY + 20

    // 根据玩家数量动态计算面板高度
    const avatarSize = 50
    const avatarSpacing = 10
    const topBottomPadding = 40
    const minPanelHeight = 200
    const requiredHeightForAvatars = topBottomPadding + playerCount * (avatarSize + avatarSpacing) - avatarSpacing
    const panelHeight = Math.max(minPanelHeight, requiredHeightForAvatars)

    const defaultStartY = height - panelHeight - 50
    const panelY = Math.max(minStartY, defaultStartY)

    // 头像区域宽度
    const avatarAreaWidth = 80
    const panelX = 10
    const panelWidth = width - 20

    return {
      x: panelX,
      y: panelY,
      width: panelWidth,
      height: panelHeight,
      // 添加内容区域信息
      contentX: panelX + avatarAreaWidth + 10,
      contentWidth: panelWidth - avatarAreaWidth - 10
    }
  }

  // 处理磁砖来源清理
  handleTileSourceCleanup() {
    // 标记是否从工厂选择（在清除状态前记录）
    const isFromFactory = this.gameState.selectedFactoryIndex !== undefined

    // 如果是从工厂选择的，现在才真正清空工厂并移动剩余磁砖
    if (isFromFactory) {
      console.log(`确认从工厂${this.gameState.selectedFactoryIndex + 1}拿取磁砖`)

      // 清空工厂
      this.gameState.factories[this.gameState.selectedFactoryIndex] = []

      // 剩余磁砖放入中央区域
      this.gameState.centerArea.push(...this.gameState.remainingFactoryTiles)

      // 清除工厂选择相关状态
      this.gameState.selectedFactoryIndex = undefined
      this.gameState.selectedFactoryTiles = null
      this.gameState.remainingFactoryTiles = null
    }

    // 如果是从中央区域选择的，现在才真正移除磁砖
    if (this.gameState.selectedCenterTiles) {
      console.log('确认从中央区域拿取磁砖')

      // 从中央区域移除选中的磁砖
      this.gameState.selectedCenterTiles.forEach(tile => {
        const index = this.gameState.centerArea.indexOf(tile)
        if (index > -1) {
          this.gameState.centerArea.splice(index, 1)
        }
      })

      // 清除中央区域选择相关状态
      this.gameState.selectedCenterTiles = null
    }

    // 返回是否从工厂选择
    return isFromFactory
  }

  // 清除选择状态
  clearSelectionState() {
    this.gameState.selectedTiles = null
    this.gameState.selectedColor = null
    this.gameState.showingFactorySelection = false
    this.gameState.showingCenterSelection = false
    this.gameState.selectedFactory = null
  }

  // 辅助方法：点是否在矩形内
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height
  }

  // 辅助方法：获取磁砖点击
  getTileHit(x, y, factoryRect, factory) {
    for (let i = 0; i < factory.length; i++) {
      const tileSize = 20
      const tileX = factoryRect.x + (i % 2) * (tileSize + 2) + 10
      const tileY = factoryRect.y + Math.floor(i / 2) * (tileSize + 2) + 10

      if (x >= tileX && x <= tileX + tileSize &&
          y >= tileY && y <= tileY + tileSize) {
        return { color: factory[i], index: i }
      }
    }
    return null
  }

  // ===== 高级AI决策系统 =====

  // 获取AI最佳移动（基于lua.txt的算法，支持难度调整）
  getBestAIMove() {
    const tileGroups = this.getAvailableTileGroups()
    if (tileGroups.length === 0) return null

    const allMoves = this.getAllAIMoves(tileGroups)
    if (allMoves.length === 0) return null

    // 根据AI难度调整决策策略
    const difficulty = this.gameState.aiDifficulty || 'medium'

    switch (difficulty) {
      case 'easy':
        return this.getEasyAIMove(allMoves)
      case 'hard':
        return this.getHardAIMove(allMoves)
      case 'medium':
      default:
        return this.getMediumAIMove(allMoves)
    }
  }

  // 简单AI：随机选择，偶尔做出好决策
  getEasyAIMove(allMoves) {
    // 30%概率做出最优决策，70%概率随机选择
    if (Math.random() < 0.3) {
      return this.getMediumAIMove(allMoves)
    } else {
      return allMoves[Math.floor(Math.random() * allMoves.length)]
    }
  }

  // 中等AI：原有的策略
  getMediumAIMove(allMoves) {
    // 按评分排序，选择最高分的移动
    const highestScoreMoves = this.getHighestScorePotential(allMoves)
    const mostTilesMoves = this.getMostTiles(highestScoreMoves)

    // 优先选择中央区域的磁砖（如果评分相同）
    const centerMoves = this.getCenterMoves(mostTilesMoves)
    const finalMoves = centerMoves.length > 0 ? centerMoves : mostTilesMoves

    // 从最佳移动中随机选择一个
    return finalMoves[Math.floor(Math.random() * finalMoves.length)]
  }

  // 困难AI：更深入的策略分析
  getHardAIMove(allMoves) {
    // 首先使用中等AI的策略
    const mediumMoves = this.getMediumAIMove(allMoves)

    // 然后进行额外的策略分析
    const strategicMoves = this.analyzeStrategicMoves(allMoves)

    // 如果有明显的策略优势，选择策略移动；否则选择中等AI的移动
    if (strategicMoves.length > 0) {
      return strategicMoves[Math.floor(Math.random() * strategicMoves.length)]
    }

    return mediumMoves
  }

  // 分析策略性移动（困难AI专用）
  analyzeStrategicMoves(allMoves) {
    const strategicMoves = []

    allMoves.forEach(move => {
      let strategicValue = move.scorePotential

      // 分析是否能阻止对手完成行
      const blockingValue = this.calculateBlockingValue(move)
      strategicValue += blockingValue

      // 分析是否能完成多行连击
      const comboValue = this.calculateComboValue(move)
      strategicValue += comboValue

      // 分析是否能获得颜色集合奖励
      const setCompletionValue = this.calculateSetCompletionValue(move)
      strategicValue += setCompletionValue

      move.strategicValue = strategicValue
    })

    // 选择策略价值最高的移动
    const maxStrategicValue = Math.max(...allMoves.map(m => m.strategicValue))
    return allMoves.filter(m => m.strategicValue === maxStrategicValue)
  }

  // 计算阻止对手的价值
  calculateBlockingValue(move) {
    // 简化实现：如果选择的磁砖能阻止对手完成高分行，增加价值
    let blockingValue = 0

    // 检查其他玩家的准备区
    this.gameState.players.forEach((player, playerIndex) => {
      if (playerIndex === this.gameState.currentPlayer) return

      player.patternLines.forEach((line, lineIndex) => {
        if (line.length === lineIndex && line[0] === move.color) {
          // 对手即将完成这一行，阻止他们获得这个颜色有价值
          blockingValue += 2
        }
      })
    })

    return blockingValue
  }

  // 计算连击价值
  calculateComboValue(move) {
    // 简化实现：检查是否能在同一轮完成多行
    const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
    let comboValue = 0

    // 如果这个移动能完成一行，检查是否还有其他即将完成的行
    if (move.lineIndex < 5) {
      const targetLine = currentPlayer.patternLines[move.lineIndex]
      if (targetLine.length === move.lineIndex) {
        // 这一行即将完成，检查其他行
        currentPlayer.patternLines.forEach((line, index) => {
          if (index !== move.lineIndex && line.length === index) {
            comboValue += 1 // 每个额外的完成行增加价值
          }
        })
      }
    }

    return comboValue
  }

  // 计算颜色集合完成价值
  calculateSetCompletionValue(move) {
    const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
    let setValue = 0

    // 检查墙壁上该颜色的数量
    let colorCount = 0
    currentPlayer.wall.forEach(row => {
      row.forEach(cell => {
        if (cell.color === move.color && cell.filled) {
          colorCount++
        }
      })
    })

    // 如果接近完成一个颜色的集合（5个），增加价值
    if (colorCount >= 3) {
      setValue += (colorCount - 2) * 2 // 3个=2分，4个=4分
    }

    return setValue
  }

  // 获取所有可用的磁砖组
  getAvailableTileGroups() {
    const tileGroups = []

    // 从工厂获取磁砖组
    this.gameState.factories.forEach((factory, factoryIndex) => {
      const colorCounts = {}
      factory.forEach(tile => {
        colorCounts[tile] = (colorCounts[tile] || 0) + 1
      })

      Object.entries(colorCounts).forEach(([color, count]) => {
        tileGroups.push({
          type: 'factory',
          factoryIndex: factoryIndex,
          color: color,
          number: count,
          firstTile: false
        })
      })
    })

    // 从中央区域获取磁砖组
    const centerColorCounts = {}
    let hasFirstPlayerTile = this.gameState.firstPlayerTile
    const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea

    if (Array.isArray(centerTiles)) {
      centerTiles.forEach(tile => {
        centerColorCounts[tile] = (centerColorCounts[tile] || 0) + 1
      })
    }

    Object.entries(centerColorCounts).forEach(([color, count]) => {
      tileGroups.push({
        type: 'center',
        color: color,
        number: count,
        firstTile: hasFirstPlayerTile
      })
    })

    return tileGroups
  }

  // 获取所有可能的AI移动
  getAllAIMoves(tileGroups) {
    const allMoves = []
    const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
    const floorLineNum = currentPlayer.floorLine.length
    const penalties = [-1, -1, -2, -2, -2, -3, -3] // 地板线惩罚

    tileGroups.forEach(tiles => {
      const availableLines = this.getAIAvailableLines(currentPlayer, tiles.color)

      availableLines.forEach(lineIndex => {
        let scorePotential = 0
        let tileNum = tiles.number
        let prospect = 1

        if (lineIndex < 5) { // 不是地板线
          // 计算基础得分
          scorePotential = this.calculatePlacementScore(currentPlayer, lineIndex, tiles.color)

          const filledLine = currentPlayer.patternLines[lineIndex].length
          const leftOver = (filledLine + tileNum) - (lineIndex + 1)

          if (leftOver > 0) {
            // 有多余磁砖会进入地板线
            tileNum = leftOver
            scorePotential -= leftOver / this.gameState.round
          } else if (leftOver < 0) {
            // 不能完成这一行，计算未来完成的可能性
            prospect = this.calculateProspect(tiles, leftOver)
            const leftover = (lineIndex + 1) - (filledLine + tiles.number)
            scorePotential = scorePotential / (leftover * leftover + 1)
          } else {
            // 刚好完成这一行
            tileNum = 0
          }

          // 加上轮次奖励
          scorePotential += tiles.number / this.gameState.round

          // 考虑行、列、颜色的连接奖励
          scorePotential += this.getRowLength(currentPlayer, lineIndex) * (2/15)
          scorePotential += this.getColumnLength(currentPlayer, lineIndex, tiles.color) * (7/15)
          scorePotential += this.getColorCount(currentPlayer, tiles.color) * (10/15)

          // 如果行已有磁砖，给予额外奖励
          if (filledLine > 0) {
            scorePotential *= ((lineIndex + 1) - filledLine + 1)
          }
        }

        // 计算地板线惩罚
        // 只有从中央区域选择才会获得起始玩家磁砖
        if (tiles.type === 'center' && tiles.firstTile) {
          tileNum += 1 // 起始玩家磁砖
        }

        let f = floorLineNum
        while (f < floorLineNum + tileNum && f < penalties.length) {
          scorePotential += penalties[f] * 2
          f++
        }

        allMoves.push({
          type: tiles.type,
          factoryIndex: tiles.factoryIndex,
          color: tiles.color,
          number: tiles.number,
          lineIndex: lineIndex,
          scorePotential: scorePotential,
          prospect: prospect
        })
      })
    })

    return allMoves
  }

  // 获取AI可用的准备区行
  getAIAvailableLines(player, color) {
    const availableLines = []

    for (let i = 0; i < 5; i++) {
      const patternLine = player.patternLines[i]
      const maxCapacity = i + 1

      // 检查行是否为空或颜色匹配
      const canPlace = patternLine.length === 0 || patternLine[0] === color

      // 检查行是否还有空间
      const hasSpace = patternLine.length < maxCapacity

      // 检查墙壁是否已有该颜色
      const wallHasColor = this.checkWallHasColor(player.wall[i], color)

      // 只有在可以放置、有空间且墙壁没有该颜色时才可用
      if (canPlace && hasSpace && !wallHasColor) {
        availableLines.push(i)
      }
    }

    // 总是可以放到地板线
    availableLines.push(5) // 地板线索引

    return availableLines
  }

  // 检查墙壁行是否已有指定颜色
  checkWallHasColor(wallRow, color) {
    return wallRow.some(cell => cell.color === color && cell.filled)
  }

  // 计算放置得分
  calculatePlacementScore(player, lineIndex, color) {
    // 基础得分：放置到墙壁后的连接得分
    let score = 1 // 基础1分

    // 模拟放置到墙壁，计算连接得分
    const wallRow = player.wall[lineIndex]
    const targetCell = wallRow.find(cell => cell.color === color)
    if (!targetCell) return score

    const col = wallRow.indexOf(targetCell)

    // 计算水平连接
    let horizontalCount = 1
    // 向左检查
    for (let c = col - 1; c >= 0; c--) {
      if (wallRow[c].filled) {
        horizontalCount++
      } else {
        break
      }
    }
    // 向右检查
    for (let c = col + 1; c < wallRow.length; c++) {
      if (wallRow[c].filled) {
        horizontalCount++
      } else {
        break
      }
    }

    // 计算垂直连接
    let verticalCount = 1
    // 向上检查
    for (let r = lineIndex - 1; r >= 0; r--) {
      if (player.wall[r][col].filled) {
        verticalCount++
      } else {
        break
      }
    }
    // 向下检查
    for (let r = lineIndex + 1; r < 5; r++) {
      if (player.wall[r][col].filled) {
        verticalCount++
      } else {
        break
      }
    }

    score += Math.max(horizontalCount - 1, 0)
    score += Math.max(verticalCount - 1, 0)

    return score
  }

  // 计算未来完成的可能性
  calculateProspect(tiles, leftOver) {
    // 简化版本的prospect计算
    if (leftOver >= 0) return 1

    // 根据缺少的磁砖数量计算可能性
    const needed = Math.abs(leftOver)
    if (needed <= 2) return 0.75
    if (needed <= 4) return 0.5
    return 0.25
  }

  // 获取行长度（已填充的墙壁格子数）
  getRowLength(player, row) {
    return player.wall[row].filter(cell => cell.filled).length
  }

  // 获取列长度（已填充的墙壁格子数）
  getColumnLength(player, row, color) {
    const wallRow = player.wall[row]
    const targetCell = wallRow.find(cell => cell.color === color)
    if (!targetCell) return 0

    const col = wallRow.indexOf(targetCell)
    let count = 0
    for (let r = 0; r < 5; r++) {
      if (player.wall[r][col].filled) {
        count++
      }
    }
    return count
  }

  // 获取颜色数量（玩家已有的该颜色磁砖数）
  getColorCount(player, color) {
    let count = 0

    // 计算准备区中的该颜色磁砖
    player.patternLines.forEach(line => {
      if (line.length > 0 && line[0] === color) {
        count += line.length
      }
    })

    // 计算墙壁中的该颜色磁砖
    player.wall.forEach(row => {
      row.forEach(cell => {
        if (cell.color === color && cell.filled) {
          count++
        }
      })
    })

    return count
  }

  // 获取最高评分的移动
  getHighestScorePotential(allMoves) {
    if (allMoves.length === 0) return []

    const maxScore = Math.max(...allMoves.map(move => move.scorePotential))
    return allMoves.filter(move => move.scorePotential === maxScore)
  }

  // 获取磁砖数量最多的移动
  getMostTiles(moves) {
    if (moves.length === 0) return []

    const maxTiles = Math.max(...moves.map(move => move.number))
    return moves.filter(move => move.number === maxTiles)
  }

  // 获取中央区域的移动
  getCenterMoves(moves) {
    return moves.filter(move => move.type === 'center')
  }


}

// 花砖物语联机游戏类
class AzulMultiplayerGame extends AzulGame {
  constructor(gameConfig, sharedCanvas, multiplayerManager) {
    // 调用父类构造函数
    super(gameConfig, sharedCanvas)

    // 确保游戏状态正确初始化
    this.destroyed = false
    this.gameEndDialogShown = false // 游戏结束对话框显示标志

    // 定义颜色数组，用于最终计分动画
    this.colors = ['blue', 'yellow', 'red', 'black', 'teal']
    this.isRunning = true

    // 联机相关属性
    this.multiplayerManager = multiplayerManager
    this.isMultiplayer = true
    this.localPlayerId = multiplayerManager.playerId
    this.isMyTurn = false
    this.waitingForServer = false
    this.processingSelection = false // 防止选择界面显示时的重复点击

    // 倒计时相关属性
    this.remainingTime = 0
    this.timerStartTime = 0
    this.currentRemainingTime = 0
    this.clientTimerInterval = null

    // 重连状态
    this.isReconnecting = false

    console.log('创建联机游戏实例')
    console.log('本地玩家ID:', this.localPlayerId)
    console.log('游戏配置:', gameConfig)

    // 如果配置中包含roomId，设置到multiplayerManager中
    console.log('检查房间ID设置:', {
      hasRoomId: !!gameConfig.roomId,
      roomId: gameConfig.roomId,
      hasMultiplayerManager: !!this.multiplayerManager
    })

    if (gameConfig.roomId && this.multiplayerManager) {
      this.multiplayerManager.roomId = gameConfig.roomId
      console.log('✅ 设置房间ID成功:', gameConfig.roomId)
    } else {
      console.warn('❌ 无法设置房间ID:', {
        roomId: gameConfig.roomId,
        multiplayerManager: !!this.multiplayerManager
      })
    }

    // 设置联机事件监听
    this.setupMultiplayerEvents()

    // 设置游戏事件监听器
    this.setupGameEventListeners()

    // 初始化联机游戏状态
    this.initMultiplayerGameState(gameConfig)

    // 确保关键方法可用（修复继承问题）
    this.ensureMethodsAvailable()
  }

  // 确保关键方法可用
  ensureMethodsAvailable() {
    // 确保createImage方法可用
    if (!this.createImage) {
      this.createImage = function() {
        try {
          if (typeof wx !== 'undefined' && wx.createImage) {
            // 微信小游戏环境
            return wx.createImage()
          } else if (typeof Image !== 'undefined') {
            // 浏览器环境
            return new Image()
          } else {
            console.warn('当前环境不支持图片加载')
            return null
          }
        } catch (error) {
          console.warn('创建图片对象失败:', error)
          return null
        }
      }
    }
  }

  // 设置联机事件监听
  setupMultiplayerEvents() {
    console.log('设置联机事件监听器')

    // 监听游戏状态更新
    this.multiplayerManager.on('gameStateUpdate', (data) => {
      console.log('收到游戏状态更新:', data)
      this.handleGameStateUpdate(data)
    })

    // 监听回合计分
    this.multiplayerManager.on('roundScoring', (data) => {
      console.log('收到回合计分:', data)
      this.handleRoundScoring(data)
    })

    // 监听玩家动作
    this.multiplayerManager.on('playerAction', (data) => {
      console.log('收到玩家动作:', data)
      this.handlePlayerAction(data)
    })

    // 监听游戏结束
    this.multiplayerManager.on('gameEnded', (data) => {
      console.log('游戏结束:', data)
      this.handleGameEnd(data)
    })

    // 监听游戏开始
    this.multiplayerManager.on('gameStarted', (data) => {
      console.log('收到游戏开始:', data)
      this.handleGameStarted(data)
    })

    // 监听排位赛结束（积分变化）
    this.multiplayerManager.on('rankedGameCompleted', (data) => {
      console.log('收到排位赛结束数据:', data)
      this.handleRankedGameCompleted(data)
    })

    // 监听玩家超时
    this.multiplayerManager.on('playerTimeout', (data) => {
      console.log('收到玩家超时:', data)
      this.handlePlayerTimeout(data)
    })

    // 监听剩余时间更新
    this.multiplayerManager.on('remainingTime', (data) => {
      this.handleRemainingTimeUpdate(data)
    })

    // 监听玩家断线
    this.multiplayerManager.on('playerDisconnected', (data) => {
      console.log('玩家断线:', data)
      this.handlePlayerDisconnected(data)
    })

    // 监听玩家重连
    this.multiplayerManager.on('playerReconnected', (data) => {
      console.log('玩家重连:', data)
      this.handlePlayerReconnected(data)
    })

    // 监听房间关闭
    this.multiplayerManager.on('roomClosed', (data) => {
      console.log('房间被关闭:', data)
      this.handleRoomClosed(data)
    })

    // 监听自己断线
    this.multiplayerManager.on('disconnected', () => {
      console.log('与服务器断开连接，尝试重连...')
      this.handleSelfDisconnected()
    })

    // 监听重连成功
    this.multiplayerManager.on('connected', () => {
      console.log('重新连接到服务器')
      this.handleReconnected()
    })

    // 监听玩家离开
    this.multiplayerManager.on('playerLeft', (data) => {
      console.log('玩家离开游戏:', data)
      this.handlePlayerLeft(data)
    })

    // 监听错误
    this.multiplayerManager.on('error', (data) => {
      console.error('联机错误:', data)
      this.showMessage('联机错误: ' + data.message)

      // 重置等待状态，允许用户重新操作
      this.waitingForServer = false
      this.processingSelection = false

      // 清理选择状态
      this.selectedTiles = null
      this.selectedSource = null
    })
  }

  // 设置游戏事件监听器
  setupGameEventListeners() {
    console.log('设置联机游戏事件监听器')

    // 防重复调用
    if (this.eventListenersSetup) {
      console.log('事件监听器已设置，跳过重复设置')
      return
    }
    this.eventListenersSetup = true

    // 防重复点击的时间戳
    this.lastClickTime = 0
    this.clickDelay = 100 // 100ms内的重复点击将被忽略

    // 清理可能存在的旧定时器
    if (this.eventSetupTimeout) {
      clearTimeout(this.eventSetupTimeout)
    }

    // 延迟设置事件监听器，确保MultiplayerUI完全清理
    this.eventSetupTimeout = setTimeout(() => {
      // 微信小游戏环境的事件处理
      if (typeof wx !== 'undefined') {
        // 直接设置新的触摸处理器
        console.log('AzulMultiplayerGame: 设置触摸处理器')
        this.gameClickHandler = (e) => {
          // 检查游戏状态，避免处理无效事件
          if (this.destroyed || !this.isRunning || !this.gameState) {
            return
          }

          const now = Date.now()
          const timeDiff = now - this.lastClickTime

          if (timeDiff < this.clickDelay) {
            return // 静默忽略，不输出日志
          }
          this.lastClickTime = now

          let x, y
          const touch = e.touches && e.touches[0] ? e.touches[0] : e
          x = touch.clientX || touch.x || 0
          y = touch.clientY || touch.y || 0
          this.handleClick(x, y)
        }

        // 设置新的事件监听器
        wx.onTouchStart(this.gameClickHandler)
        console.log('设置微信小游戏触摸事件监听器')
      } else {
        // 浏览器环境的事件处理
        this.gameClickHandler = (e) => {
          const now = Date.now()
          if (now - this.lastClickTime < this.clickDelay) {
            console.log('忽略重复点击事件')
            return
          }
          this.lastClickTime = now

          const rect = this.canvas.getBoundingClientRect()
          const x = e.clientX - rect.left
          const y = e.clientY - rect.top
          console.log('联机游戏点击事件:', x, y)
          this.handleClick(x, y)
        }

        this.gameTouchHandler = (e) => {
          if (e.preventDefault && typeof e.preventDefault === 'function') {
            e.preventDefault()
          }

          const now = Date.now()
          if (now - this.lastClickTime < this.clickDelay) {
            console.log('忽略重复触摸事件')
            return
          }
          this.lastClickTime = now

          const touch = e.touches[0]
          const rect = this.canvas.getBoundingClientRect()
          const x = touch.clientX - rect.left
          const y = touch.clientY - rect.top
          console.log('联机游戏触摸事件:', x, y)
          this.handleClick(x, y)
        }

        this.canvas.addEventListener('click', this.gameClickHandler)
        this.canvas.addEventListener('touchstart', this.gameTouchHandler)
        console.log('设置浏览器游戏事件监听器（延迟设置）')
      }
    }, 200) // 延迟200ms确保清理完成
  }

  // 初始化联机游戏状态
  initMultiplayerGameState(gameConfig) {
    console.log('初始化联机游戏状态，配置:', gameConfig)

    // 联机游戏的状态完全由服务端控制
    // 客户端只需要等待服务端推送游戏状态
    if (gameConfig.gameState && gameConfig.gameState.players) {
      console.log('使用服务端下发的游戏状态')
      this.gameState = gameConfig.gameState

      // 调试：检查玩家信息
      console.log('服务端玩家数据:');
      this.gameState.players.forEach((player, index) => {
        console.log(`玩家${index}:`, {
          id: player.id,
          nickName: player.nickName,
          name: player.name,
          avatarUrl: player.avatarUrl || '无头像URL'
        });
      });

      // 检查工厂是否有瓷砖
      if (this.gameState.factories) {
        const totalTiles = this.gameState.factories.reduce((sum, factory) => sum + factory.length, 0);
        console.log('工厂瓷砖总数:', totalTiles);
        this.gameState.factories.forEach((factory, index) => {
          console.log(`工厂${index}:`, factory);
        });
      }
    } else {
      console.log('等待服务端推送游戏状态')
      // 创建一个空的游戏状态，等待服务端推送
      this.gameState = {
        players: gameConfig.players || [],
        factories: [],
        centerArea: [],
        currentPlayer: 0,
        round: 1,
        phase: 'collect',
        gameEnded: false
      }
    }

    // 确保游戏状态完整性
    this.validateGameState()

    // 设置当前玩家
    this.updateCurrentPlayer()

    // 启动倒计时（如果游戏已经开始）
    if (this.gameState.phase === 'collect') {
      console.log('游戏已开始，启动倒计时')
      // 延迟启动，确保联机管理器已经准备好
      setTimeout(() => {
        this.startTurnTimer()
      }, 1500) // 增加延迟时间，确保完全清理
    }

    // 延迟渲染，确保状态完全初始化
    setTimeout(() => {
      console.log('开始渲染联机游戏界面')
      this.render()
    }, 100)

    console.log('联机游戏状态初始化完成')
  }

  // 验证游戏状态完整性
  validateGameState() {
    console.log('验证游戏状态完整性')

    // 确保基本属性存在
    if (!this.gameState.players) {
      console.error('游戏状态缺少players')
      this.gameState.players = []
    }

    if (!this.gameState.factories || !Array.isArray(this.gameState.factories)) {
      console.log('初始化工厂')
      const playerCount = this.gameState.players.length
      const factoryCount = this.getFactoryCount(playerCount)
      this.gameState.factories = Array(factoryCount).fill(null).map(() => [])
    }

    if (!this.gameState.centerArea || !Array.isArray(this.gameState.centerArea)) {
      console.log('初始化中央区域')
      this.gameState.centerArea = []
    }

    if (typeof this.gameState.currentPlayer !== 'number') {
      console.log('初始化当前玩家')
      this.gameState.currentPlayer = 0
    }

    if (typeof this.gameState.round !== 'number' || this.gameState.round < 1) {
      console.log('初始化回合数')
      this.gameState.round = 1
    }

    if (!this.gameState.phase) {
      console.log('初始化游戏阶段')
      this.gameState.phase = 'collect'
    }

    // 验证每个玩家对象的完整性
    this.gameState.players.forEach((player, index) => {
      // 验证基本信息
      if (!player.nickName) {
        console.warn(`修复玩家${index}的nickName`)
        player.nickName = `玩家${index + 1}`
      }
      if (!player.id) {
        console.warn(`修复玩家${index}的id`)
        player.id = `player_${index}`
      }

      // 验证游戏状态
      if (!player.patternLines) {
        console.warn(`修复玩家${index}的patternLines`)
        player.patternLines = [[], [], [], [], []]
      }
      if (!player.wall) {
        console.warn(`修复玩家${index}的wall`)
        player.wall = this.createPlayerWall()
      }
      if (!player.floorLine) {
        console.warn(`修复玩家${index}的floorLine`)
        player.floorLine = []
      }
      if (typeof player.score !== 'number') {
        console.warn(`修复玩家${index}的score`)
        player.score = 0
      }
    })

    console.log('游戏状态验证完成:', {
      players: this.gameState.players.length,
      factories: this.gameState.factories.length,
      currentPlayer: this.gameState.currentPlayer,
      round: this.gameState.round,
      phase: this.gameState.phase
    })
  }

  // 创建初始游戏状态
  createInitialGameState(players) {
    console.log('创建初始游戏状态，玩家:', players)

    if (!players || players.length === 0) {
      console.error('玩家列表为空，使用默认玩家')
      players = [
        { id: 'player1', nickName: '玩家1' },
        { id: 'player2', nickName: '玩家2' }
      ]
    }

    // 基于玩家数量确定工厂数量
    const factoryCount = this.getFactoryCount(players.length)
    console.log(`${players.length}名玩家，创建${factoryCount}个工厂`)

    // 创建完整的游戏状态
    const gameState = {
      players: players.map((player, index) => ({
        id: player.id,
        name: player.nickName || player.name || `玩家${index + 1}`,
        index: index,
        score: 0,
        patternLines: [[], [], [], [], []],
        wall: this.createPlayerWall(),
        floorLine: [],
        isReady: false
      })),
      factories: Array(factoryCount).fill(null).map((_, index) => {
        console.log(`创建工厂${index}`)
        return []
      }),
      centerArea: [], // 使用centerArea保持与现有代码一致
      currentPlayer: 0,
      round: 1,
      phase: 'collect', // collect, scoring, end
      gameEnded: false,
      winner: null,
      // 添加一些游戏需要的额外状态
      tileBag: this.createTileBag(),
      firstPlayerMarker: true // 首个玩家标记在中央区域
    }

    console.log('初始游戏状态创建完成:', {
      playerCount: gameState.players.length,
      factoryCount: gameState.factories.length,
      tileBagSize: gameState.tileBag.length
    })

    return gameState
  }

  // 创建瓷砖袋
  createTileBag() {
    const colors = ['blue', 'yellow', 'red', 'black', 'white']
    const tileBag = []

    // 每种颜色20个瓷砖
    colors.forEach(color => {
      for (let i = 0; i < 20; i++) {
        tileBag.push(color)
      }
    })

    // 打乱瓷砖袋
    for (let i = tileBag.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[tileBag[i], tileBag[j]] = [tileBag[j], tileBag[i]]
    }

    console.log('创建瓷砖袋，总数:', tileBag.length)
    return tileBag
  }

  // 创建玩家墙壁
  createPlayerWall() {
    // 花砖物语的墙壁模式：每行每列都有固定的颜色模式
    const colorPattern = [
      ['blue', 'yellow', 'red', 'black', 'teal'],
      ['teal', 'blue', 'yellow', 'red', 'black'],
      ['black', 'teal', 'blue', 'yellow', 'red'],
      ['red', 'black', 'teal', 'blue', 'yellow'],
      ['yellow', 'red', 'black', 'teal', 'blue']
    ]

    return colorPattern.map(row =>
      row.map(color => ({ color, filled: false }))
    )
  }

  // 获取工厂数量（基于玩家数量）
  getFactoryCount(playerCount) {
    const factoryCounts = {
      2: 5,
      3: 7,
      4: 9
    }
    return factoryCounts[playerCount] || 5
  }

  // 更新当前玩家状态
  updateCurrentPlayer() {
    const currentPlayerId = this.gameState.players[this.gameState.currentPlayer]?.id
    this.isMyTurn = (currentPlayerId === this.localPlayerId)

    console.log('更新当前玩家状态:')
    console.log('- 当前玩家索引:', this.gameState.currentPlayer)
    console.log('- 当前玩家ID:', currentPlayerId)
    console.log('- 本地玩家ID:', this.localPlayerId)
    console.log('- 是否轮到我:', this.isMyTurn)
  }

  // 处理点击事件（重写父类方法）
  handleClick(x, y) {
    // 检测BGM开关按钮点击（优先处理，不受游戏状态限制）
    if (this.bgmToggleButton && this.isPointInRect(x, y, this.bgmToggleButton)) {
      if (window.gameApp) {
        window.gameApp.toggleBgm()
      }
      return
    }

    // 检测玩家头像点击（算分阶段不允许切换面板）
    if (this.playerAvatarAreas && this.gameState && this.gameState.phase !== 'scoring') {
      for (const avatarArea of this.playerAvatarAreas) {
        if (this.isPointInRect(x, y, avatarArea)) {
          console.log(`点击了玩家${avatarArea.playerIndex + 1}的头像`)
          this.currentViewingPlayer = avatarArea.playerIndex
          this.render() // 重新渲染以显示新选中的玩家
          return
        }
      }
    } else if (this.playerAvatarAreas && this.gameState && this.gameState.phase === 'scoring') {
      // 算分阶段点击头像时提示
      for (const avatarArea of this.playerAvatarAreas) {
        if (this.isPointInRect(x, y, avatarArea)) {
          this.showMessage('算分阶段无法切换面板')
          return
        }
      }
    }

    // 如果游戏已经停止运行或被清理，静默忽略（不输出日志避免刷屏）
    if (this.destroyed || !this.isRunning || !this.gameState || this.gameClickHandler === null) {
      return
    }

    console.log('联机游戏点击:', x, y, '是否轮到我:', this.isMyTurn, '游戏是否结束:', this.gameState?.gameEnded)

    // 如果游戏已经结束，不处理游戏逻辑点击，但允许UI交互（如返回按钮、对话框等）
    if (this.gameState && this.gameState.gameEnded) {
      console.log('游戏已结束，忽略游戏逻辑点击')
      return
    }

    // 只有轮到自己时才能操作游戏逻辑
    if (!this.isMyTurn) {
      console.log('不是我的回合，忽略游戏逻辑点击')
      this.showMessage('请等待其他玩家操作')
      return
    }

    // 如果正在等待服务器响应，忽略点击
    if (this.waitingForServer) {
      console.log('正在等待服务器响应，忽略点击')
      return
    }

    // 如果正在处理选择界面，忽略点击
    if (this.processingSelection) {
      console.log('正在处理选择界面，忽略点击')
      return
    }

    // 联机游戏特殊处理：跳过父类的AI检查，直接处理游戏逻辑
    this.handleMultiplayerClick(x, y)
  }

  // 联机游戏的点击处理（基于父类逻辑，但跳过AI检查）
  handleMultiplayerClick(x, y) {
    console.log('处理联机游戏点击:', x, y)

    if (this.gameState.phase !== 'collect') {
      console.log('当前阶段不是collect，忽略点击:', this.gameState.phase)
      return
    }

    // 如果正在显示放置选择，检测点击区域
    if (this.gameState.selectedTiles && !this.gameState.showingFactorySelection) {
      const placementHit = this.getPlacementHit(x, y)

      if (placementHit !== null) {
        // 点击了可放置的区域
        console.log('点击了可放置区域:', placementHit)
        this.placeTiles(this.gameState.selectedTiles, placementHit)
        return
      } else {
        // 检查是否点击在当前玩家面板内
        const inPlayerPanel = this.isPointInCurrentPlayerPanel(x, y)
        if (inPlayerPanel) {
          // 点击了玩家面板内的非高亮区域，不做任何操作
          console.log('点击玩家面板内的非高亮区域，不取消选择')
          return
        } else {
          // 点击了玩家面板外，取消选择
          console.log('点击玩家面板外，取消磁砖选择')
          this.gameState.selectedTiles = null
          this.gameState.selectedColor = null
          return
        }
      }
    }

    const hitResult = this.hitTest(x, y)
    console.log('点击测试结果:', hitResult)

    if (hitResult) {
      // 如果点击的是高亮框内的空白处，不做任何处理
      if (hitResult.type === 'factoryBoxInside') {
        console.log('点击工厂选择框内的空白处，不取消选择')
        return
      }
      if (hitResult.type === 'centerBoxInside') {
        console.log('点击中央区域选择框内的空白处，不取消选择')
        return
      }
      this.processHit(hitResult)
    } else {
      // 如果没有命中任何有效区域，检查是否需要取消当前选择状态
      if (this.gameState.showingFactorySelection || this.gameState.showingCenterSelection) {
        console.log('点击高亮区域外，取消选择状态')
        this.cancelSelection()
      }
    }
  }

  // 重写父类的点击处理方法
  processHit(hitResult) {
    console.log('联机游戏点击处理:', hitResult)

    // 检查是否是我的回合
    if (!this.isMyTurn) {
      this.showMessage('不是你的回合！')
      return
    }

    // 检查是否正在等待服务器响应
    if (this.waitingForServer) {
      this.showMessage('请等待服务器响应...')
      return
    }

    // 检查是否正在处理选择（防止重复点击）
    if (this.processingSelection) {
      console.log('正在处理选择，忽略重复点击')
      return
    }

    if (hitResult.type === 'factory') {
      // 点击工厂，显示颜色选择
      console.log('点击工厂，显示颜色选择:', hitResult.factoryIndex)
      this.showFactorySelection(hitResult.factoryIndex)
      return // 立即返回，不继续处理
    } else if (hitResult.type === 'factoryColor') {
      // 选择工厂中的颜色，发送到服务器
      console.log('选择工厂颜色:', hitResult.factoryIndex, hitResult.color)
      this.selectFromFactoryMultiplayer(hitResult.factoryIndex, hitResult.color)
    } else if (hitResult.type === 'center') {
      // 点击中央区域，显示颜色选择
      console.log('点击中央区域，显示颜色选择')
      this.showCenterSelection()
      return // 立即返回，不继续处理
    } else if (hitResult.type === 'centerColor') {
      // 选择中央区域中的颜色，发送到服务器
      console.log('选择中央区域颜色:', hitResult.color)
      this.selectFromCenterMultiplayer(hitResult.color)
    } else if (hitResult.type === 'placement') {
      // 放置瓷砖
      console.log('放置瓷砖到第', hitResult.lineIndex + 1, '行')
      this.placeTiles(this.gameState.selectedTiles, hitResult.lineIndex)
    } else if (hitResult.type === 'cancel') {
      // 取消选择
      console.log('取消选择')
      this.cancelSelection()
    } else {
      console.log('未处理的点击类型:', hitResult.type)
    }
  }

  // 联机版本的工厂选择
  selectFromFactoryMultiplayer(factoryIndex, color) {
    console.log(`联机选择工厂${factoryIndex}的${color}瓷砖`)

    // 验证工厂是否存在该颜色的瓷砖
    const factory = this.gameState.factories[factoryIndex]
    if (!factory) {
      console.error(`工厂${factoryIndex}不存在`)
      this.showMessage('无效的工厂选择')
      return
    }

    // 兼容新旧格式
    const factoryTiles = factory.tiles || factory
    if (!Array.isArray(factoryTiles)) {
      console.error(`工厂${factoryIndex}数据格式无效`)
      this.showMessage('无效的工厂选择')
      return
    }

    const availableTiles = factoryTiles.filter(tile => tile === color)
    if (availableTiles.length === 0) {
      console.error(`工厂${factoryIndex}中没有${color}颜色的瓷砖`)
      console.log('当前工厂内容:', factory)
      this.showMessage(`工厂中没有${color}颜色的瓷砖`)
      return
    }

    // 设置处理标志位，防止重复点击
    this.processingSelection = true

    // 显示放置选择界面
    this.gameState.selectedFactoryIndex = factoryIndex
    this.gameState.selectedColor = color
    this.gameState.selectedTiles = availableTiles
    this.gameState.showingFactorySelection = false
    this.gameState.showingPlacement = true

    console.log(`选择了${availableTiles.length}个${color}瓷砖，请选择放置位置`)
    console.log('当前工厂状态:', this.gameState.factories[factoryIndex])

    // 立即重新渲染以显示放置选择界面
    this.render()

    // 延迟重置标志位
    setTimeout(() => {
      this.processingSelection = false
      console.log('重置瓷砖选择处理标志位')
    }, 200)
  }

  // 联机版本的中央区域选择
  selectFromCenterMultiplayer(color) {
    console.log(`联机选择中央区域的${color}瓷砖`)

    // 设置处理标志位，防止重复点击
    this.processingSelection = true

    // 兼容新旧中央区域格式
    const centerTiles = this.gameState.centerArea.tiles || this.gameState.centerArea

    if (!Array.isArray(centerTiles)) {
      console.error('中央区域数据格式无效:', this.gameState.centerArea)
      this.processingSelection = false
      return
    }

    // 显示放置选择界面
    this.gameState.selectedColor = color
    this.gameState.selectedTiles = centerTiles.filter(tile => tile === color)
    this.gameState.showingCenterSelection = false
    this.gameState.showingPlacement = true

    console.log(`选择了${this.gameState.selectedTiles.length}个${color}瓷砖，请选择放置位置`)

    // 立即重新渲染以显示放置选择界面
    this.render()

    // 延迟重置标志位
    setTimeout(() => {
      this.processingSelection = false
      console.log('重置中央区域选择处理标志位')
    }, 200)
  }

  // 重写父类的动作执行方法
  executeMove(move) {
    console.log('执行联机游戏动作:', move)

    // 检查是否是我的回合
    if (!this.isMyTurn) {
      this.showMessage('不是你的回合！')
      return
    }

    // 发送动作到服务器
    this.sendGameAction(move)

    // 设置等待状态
    this.waitingForServer = true
    this.showMessage('等待服务器确认...')
  }

  // 重写瓷砖放置方法（联机版本）
  placeTiles(tiles, lineIndex) {
    console.log(`联机放置${tiles.length}个瓷砖到第${lineIndex + 1}行`)

    // 检查是否是我的回合
    if (!this.isMyTurn) {
      this.showMessage('不是你的回合！')
      return
    }

    // 检查是否有选中的瓷砖
    if (!this.gameState.selectedTiles || !this.gameState.selectedColor) {
      this.showMessage('请先选择瓷砖')
      return
    }

    // 构造动作数据
    let action
    if (this.gameState.selectedFactoryIndex !== undefined) {
      // 从工厂选择
      action = {
        type: 'selectFromFactory',
        factoryIndex: this.gameState.selectedFactoryIndex,
        color: this.gameState.selectedColor,
        lineIndex: lineIndex
      }
    } else {
      // 从中央区域选择
      action = {
        type: 'selectFromCenter',
        color: this.gameState.selectedColor,
        lineIndex: lineIndex
      }
    }

    console.log('构造的动作数据:', action)

    // 清除本地选择状态
    this.clearSelectionState()

    // 发送动作到服务器
    this.sendGameAction(action)
  }

  // 清除选择状态
  clearSelectionState() {
    this.gameState.selectedTiles = null
    this.gameState.selectedColor = null
    this.gameState.selectedFactoryIndex = undefined
    this.gameState.showingFactorySelection = false
    this.gameState.showingCenterSelection = false
    this.gameState.showingPlacement = false
    this.gameState.selectedFactory = null
  }

  // 发送游戏动作到服务器
  sendGameAction(action) {
    console.log('发送游戏动作到服务器:', action)

    try {
      // 直接传递action对象，multiplayerManager会正确包装
      this.multiplayerManager.sendGameAction(action)

      // 设置等待状态
      this.waitingForServer = true
      this.showMessage('等待服务器确认...')

    } catch (error) {
      console.error('发送游戏动作失败:', error)
      this.waitingForServer = false
      this.showMessage('网络错误，请重试')
    }
  }

  // 更新游戏状态（由服务端推送）
  updateGameState(newGameState) {
    console.log('更新游戏状态:', newGameState)

    // 清除客户端超时保护（收到任何游戏状态更新都说明服务端正常）
    if (this.serverTimeoutProtection) {
      clearTimeout(this.serverTimeoutProtection)
      this.serverTimeoutProtection = null
    }

    // 如果正在播放计分动画，需要保护准备区和地板线状态
    if (this.gameState && this.gameState.phase === 'scoring') {
      console.log('计分动画进行中，保护准备区和地板线状态')

      // 保存当前的准备区状态（包含临时恢复的瓷砖）
      const savedPatternLines = this.gameState.players.map(player =>
        player.patternLines.map(line => [...line])
      )

      // 保存当前的地板线状态
      const savedFloorLines = this.gameState.players.map(player => [...player.floorLine])

      // 更新游戏状态
      this.gameState = newGameState

      // 恢复准备区状态
      this.gameState.players.forEach((player, playerIndex) => {
        player.patternLines = savedPatternLines[playerIndex]
        player.floorLine = savedFloorLines[playerIndex]
      })

      console.log('已保护准备区和地板线状态，继续动画')
    } else {
      // 正常更新游戏状态
      this.gameState = newGameState
    }

    this.waitingForServer = false
    this.updateCurrentPlayer()
    this.render()

    // 启动新回合的计时器
    console.log('调用 startTurnTimer')
    this.startTurnTimer()

    // 显示回合提示
    if (this.isMyTurn) {
      this.showMessage('轮到你了！')
    } else {
      const currentPlayer = this.gameState.players[this.gameState.currentPlayer]
      const playerName = currentPlayer.nickName || currentPlayer.name || `玩家${this.gameState.currentPlayer + 1}`
      this.showMessage(`等待 ${playerName} 操作...`)
    }
  }

  // 处理游戏状态更新（保持兼容性）
  handleGameStateUpdate(data) {
    console.log('处理游戏状态更新:', data)

    if (data.gameState) {
      this.updateGameState(data.gameState)
    }

    // 如果包含剩余时间信息，直接设置倒计时
    if (data.remainingTime !== undefined) {
      this.handleRemainingTimeUpdate({
        remainingTime: data.remainingTime,
        currentPlayer: data.gameState ? data.gameState.currentPlayer : this.gameState.currentPlayer,
        timestamp: Date.now()
      })
    } else {
      // 如果没有剩余时间信息，主动请求
      console.log('游戏状态更新中没有剩余时间信息，主动请求')
      this.requestRemainingTime()
    }
  }

  // 处理回合计分
  handleRoundScoring(data) {
    console.log('处理回合计分:', data)

    // 清除客户端超时保护（进入计分阶段说明游戏正常进行）
    if (this.serverTimeoutProtection) {
      clearTimeout(this.serverTimeoutProtection)
      this.serverTimeoutProtection = null
      console.log('进入计分阶段，清除超时保护')
    }

    // 先播放计分动画，不要立即更新游戏状态（避免覆盖临时恢复的状态）
    if (data.animationData) {
      this.playRoundScoringAnimation(data.animationData)
    }

    // 显示计分详情
    if (data.scoringDetails) {
      this.showScoringDetails(data.scoringDetails)
    }

    // 注意：游戏状态的更新会在动画完成后进行，或者通过其他gameStateUpdate事件进行
    // 这样可以避免在动画期间覆盖临时恢复的准备区和地板线状态
  }

  // 显示计分详情
  showScoringDetails(scoringDetails) {
    console.log('显示计分详情:', scoringDetails)

    let message = `第${scoringDetails.round}轮计分完成\n`

    for (let playerScore of scoringDetails.playerScores) {
      const change = playerScore.totalChange
      if (change !== 0) {
        message += `${playerScore.playerName}: ${change > 0 ? '+' : ''}${change}分\n`
      }
    }

    this.showMessage(message)
  }

  // 播放回合计分动画
  playRoundScoringAnimation(animationData) {
    console.log('播放回合计分动画:', animationData)

    // 设置动画播放标志
    this.isPlayingAnimation = true

    if (!animationData || !animationData.playerAnimations) {
      console.log('没有动画数据，跳过动画')
      return
    }

    // 初始化计分动画状态
    this.gameState.scoringAnimations = []
    this.gameState.phase = 'scoring'
    this.gameState.scoringPlayerIndex = 0 // 从第一个玩家开始计分

    this.showMessage('回合计分中...')

    // 开始逐个玩家的计分动画
    this.startMultiplayerScoringAnimation(animationData.playerAnimations, 0)
  }

  // 开始多人游戏计分动画（逐个玩家）
  startMultiplayerScoringAnimation(playerAnimations, playerIndex) {
    if (playerIndex >= playerAnimations.length) {
      // 所有玩家计分完毕，清理状态
      setTimeout(() => {
        // 清空所有玩家的已完成准备区和地板线（与服务端状态同步）
        playerAnimations.forEach(playerAnim => {
          const player = this.gameState.players.find(p => p.id === playerAnim.playerId)
          if (player) {
            // 清空已完成的准备区
            if (playerAnim.wallPlacements) {
              playerAnim.wallPlacements.forEach(placement => {
                const { row } = placement.position
                player.patternLines[row] = []
              })
            }

            // 清空地板线（如果还没有被清空的话）
            if (player.floorLine.length > 0) {
              const tilesToDiscard = player.floorLine.filter(tile => tile !== 'first')
              if (this.gameState && this.gameState.discardPile) {
                this.gameState.discardPile.push(...tilesToDiscard)
              }
              player.floorLine = []
              console.log(`最终清理玩家 ${player.name} 的地板线`)
            }
          }
        })

        this.gameState.scoringAnimations = []
        this.gameState.scoringPlayer = undefined
        this.gameState.scoringPlayerIndex = 0
        this.gameState.phase = 'collect'

        // 清除动画播放标志
        this.isPlayingAnimation = false

        console.log('所有玩家计分动画完成，状态已同步')
        this.render()
      }, 500)
      return
    }

    const playerAnim = playerAnimations[playerIndex]
    const player = this.gameState.players.find(p => p.id === playerAnim.playerId)

    if (!player) {
      // 玩家不存在，跳到下一个
      this.startMultiplayerScoringAnimation(playerAnimations, playerIndex + 1)
      return
    }

    // 设置当前计分玩家
    this.gameState.scoringPlayer = this.gameState.players.indexOf(player)
    console.log(`开始为玩家${this.gameState.scoringPlayer + 1}(${player.name})计分`)

    // 算分时自动切换到对应玩家的面板
    this.currentViewingPlayer = this.gameState.scoringPlayer
    console.log(`多人算分阶段：自动切换到玩家${this.gameState.scoringPlayer + 1}的面板`)

    // 开始该玩家的计分动画序列
    this.startPlayerScoringSequence(player, playerAnim, () => {
      // 该玩家计分完成，继续下一个玩家（与单机版一致的延迟）
      setTimeout(() => {
        this.startMultiplayerScoringAnimation(playerAnimations, playerIndex + 1)
      }, 500)
    })
  }

  // 开始单个玩家的计分序列
  startPlayerScoringSequence(player, playerAnim, callback) {
    let animationDelay = 0

    // 保存原始状态并临时恢复准备区（因为服务端已经清空了准备区）
    const originalFloorLineState = [...player.floorLine] // 保存原始地板线状态

    // 临时恢复准备区状态，让动画可以从有瓷砖的准备区开始
    playerAnim.wallPlacements.forEach((placement) => {
      const { row } = placement.position
      const requiredCount = row + 1

      // 临时恢复准备区瓷砖（为了动画效果）
      player.patternLines[row] = new Array(requiredCount).fill(placement.color)
    })

    // 处理墙壁放置动画（与单机版保持一致的时序）
    playerAnim.wallPlacements.forEach((placement, placementIndex) => {
      const { row, col } = placement.position

      // 注意：不要在动画开始前就设置墙壁状态，要在动画过程中设置

      // 瓷砖放置动画（从准备区移动到墙壁）
      setTimeout(() => {
        // 在动画开始时设置墙壁状态（与单人模式一致）
        player.wall[row][col].filled = true

        this.gameState.scoringAnimations.push({
          type: 'tilePlacement',
          row: row,
          col: col,
          color: placement.color,
          startTime: Date.now(),
          duration: 500
        })

        // 连接高亮动画（与单机版一致的延迟）
        setTimeout(() => {
          const connections = this.getConnectionTiles(player, row, col)
          connections.forEach((tile, index) => {
            setTimeout(() => {
              this.gameState.scoringAnimations.push({
                type: 'tileHighlight',
                row: tile.row,
                col: tile.col,
                startTime: Date.now(),
                duration: 400
              })
            }, index * 80)
          })

          // 分数弹出动画（在连接动画开始后显示，与单机版一致）
          setTimeout(() => {
            this.gameState.scoringAnimations.push({
              type: 'scorePopup',
              score: placement.score,
              row: row,
              col: col,
              startTime: Date.now(),
              duration: 600
            })
          }, 200) // 连接动画开始后200ms显示分数
        }, 400) // 与单机版一致的400ms延迟
      }, animationDelay)

      // 每个放置的总时间：500ms(放置) + 400ms(连接延迟) + 600ms(分数显示) + 200ms(行间切换)
      animationDelay += 1200
    })

    // 处理地板线惩罚动画（与单机版保持一致）
    if (playerAnim.floorPenalty && playerAnim.floorPenalty < 0) {
      const floorPenalties = [-1, -1, -2, -2, -2, -3, -3]

      // 延迟执行地板线惩罚动画，确保在正确的时机开始
      setTimeout(() => {
        // 为每个有磁砖的地板线槽位创建单独的扣分动画（与单机版一致）
        for (let i = 0; i < Math.min(player.floorLine.length, 7); i++) {
          const penalty = floorPenalties[i]

          this.gameState.scoringAnimations.push({
            type: 'floorSlotPenalty',
            penalty: penalty,
            slotIndex: i,
            player: player,
            startTime: Date.now() + i * 100,  // 错开动画时间，与单机版一致
            duration: 800
          })
        }

        // 在动画结束后清理地板线（与单机版一致，延迟1200ms）
        setTimeout(() => {
          // 将地板线磁砖放入废弃堆（除了起始玩家磁砖）
          const tilesToDiscard = player.floorLine.filter(tile => tile !== 'first')
          if (this.gameState && this.gameState.discardPile) {
            this.gameState.discardPile.push(...tilesToDiscard)
            console.log(`将${tilesToDiscard.length}个地板线磁砖放入废弃堆`)
          }

          // 清空地板线
          player.floorLine = []
          console.log(`玩家 ${player.name} 地板线扣分动画完成，地板线已清空`)
        }, 1200) // 与单机版一致，1200ms后清空
      }, animationDelay)

      animationDelay += 1200 // 与单机版一致的地板线处理时间
    }

    // 总分数更新动画（等所有其他动画完成后再显示）
    if (playerAnim.scoreChange !== 0) {
      const oldScore = player.score
      const newScore = player.score + playerAnim.scoreChange

      // 计算所有动画完成的时间：
      // - 瓷砖放置动画：每个1200ms
      // - 地板线扣分动画：1200ms（如果有的话）
      // 总分数更新动画应该在所有这些动画完成后才开始
      const totalAnimationTime = animationDelay

      setTimeout(() => {
        // 在分数更新动画开始时才更新玩家的实际分数
        player.score = Math.max(0, newScore)

        this.gameState.scoringAnimations.push({
          type: 'scoreUpdate',
          player: player,
          oldScore: oldScore,
          newScore: newScore,
          startTime: Date.now(),
          duration: 600, // 与单机版一致
          scoreToAdd: playerAnim.scoreChange // 记录分数变化，用于动画完成时的验证
        })
      }, totalAnimationTime)

      // 注意：不增加 animationDelay，因为分数更新动画是在其他动画完成后才开始的
    }

    // 等待该玩家所有动画完成（与单机版一致的延迟）
    setTimeout(() => {
      // 注意：不在这里清空准备区，而是在所有玩家动画完成后统一清空
      // 这样可以避免影响其他玩家的动画显示

      // 注意：不在这里清理地板线，地板线的清理由地板线扣分动画自己处理
      // 或者在所有玩家动画完成后统一处理

      console.log(`玩家 ${player.name} 计分动画完成`)
      callback()
    }, animationDelay + 500)
  }

  // 多人游戏专用：播放最终计分动画并更新分数
  showFinalScoreAnimationMultiplayer(score, description, player, type, index) {
    console.log(`${player.name}${description}，获得${score}分奖励（多人游戏动画，更新分数）`)

    // 设置当前计分玩家，让高亮动画能正确显示
    const playerIndex = this.gameState.players.indexOf(player)
    this.gameState.scoringPlayer = playerIndex

    // 根据奖励类型添加相应的高亮动画
    if (type === 'row') {
      // 行奖励：高亮整行
      const filledTiles = []
      for (let col = 0; col < 5; col++) {
        if (player.wall[index][col].filled) {
          filledTiles.push({ row: index, col: col })
        }
      }

      filledTiles.forEach((tile, tileIndex) => {
        const startTime = Date.now() + tileIndex * 150

        // 高亮动画
        this.gameState.scoringAnimations.push({
          type: 'tileHighlight',
          row: tile.row,
          col: tile.col,
          startTime: startTime,
          duration: 800
        })
      })

      // 在中间位置显示分数
      if (filledTiles.length > 0) {
        const middleIndex = Math.floor(filledTiles.length / 2)
        const middleTile = filledTiles[middleIndex]

        this.gameState.scoringAnimations.push({
          type: 'scorePopup',
          score: score,
          row: middleTile.row,
          col: middleTile.col,
          startTime: Date.now() + middleIndex * 150 + 200,
          duration: 800
        })
      }
    } else if (type === 'column') {
      // 列奖励：高亮整列
      const filledTiles = []
      for (let row = 0; row < 5; row++) {
        if (player.wall[row][index].filled) {
          filledTiles.push({ row: row, col: index })
        }
      }

      filledTiles.forEach((tile, tileIndex) => {
        const startTime = Date.now() + tileIndex * 150

        // 高亮动画
        this.gameState.scoringAnimations.push({
          type: 'tileHighlight',
          row: tile.row,
          col: tile.col,
          startTime: startTime,
          duration: 800
        })
      })

      // 在中间位置显示分数
      if (filledTiles.length > 0) {
        const middleIndex = Math.floor(filledTiles.length / 2)
        const middleTile = filledTiles[middleIndex]

        this.gameState.scoringAnimations.push({
          type: 'scorePopup',
          score: score,
          row: middleTile.row,
          col: middleTile.col,
          startTime: Date.now() + middleIndex * 150 + 200,
          duration: 800
        })
      }
    } else if (type === 'color') {
      // 颜色奖励：高亮所有该颜色的磁砖
      const targetColor = this.colors[index] // 假设index对应颜色索引
      const filledTiles = []

      for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
          if (player.wall[row][col].filled && player.wall[row][col].color === targetColor) {
            filledTiles.push({ row: row, col: col })
          }
        }
      }

      filledTiles.forEach((tile, tileIndex) => {
        const startTime = Date.now() + tileIndex * 150

        // 高亮动画
        this.gameState.scoringAnimations.push({
          type: 'tileHighlight',
          row: tile.row,
          col: tile.col,
          startTime: startTime,
          duration: 800
        })
      })

      // 在第一个位置显示分数
      if (filledTiles.length > 0) {
        const firstTile = filledTiles[0]

        this.gameState.scoringAnimations.push({
          type: 'scorePopup',
          score: score,
          row: firstTile.row,
          col: firstTile.col,
          startTime: Date.now() + 200,
          duration: 800
        })
      }
    }

    // 更新玩家分数并添加分数更新动画
    const oldScore = player.score
    player.score += score
    console.log(`${player.name}分数已更新: +${score}分，总分: ${player.score}分`)

    this.gameState.scoringAnimations.push({
      type: 'scoreUpdate',
      player: player,
      oldScore: oldScore,
      newScore: player.score,
      startTime: Date.now() + 300,
      duration: 800,
      scoreToAdd: score
    })
  }

  // 播放最终计分动画
  playFinalScoringAnimation(animationData, finalScoringDetails, callback) {
    console.log('播放最终计分动画:', animationData, finalScoringDetails)

    if (!finalScoringDetails || !finalScoringDetails.playerScores) {
      console.log('没有最终计分数据，跳过动画')
      if (callback) callback()
      return
    }

    // 初始化最终计分动画状态
    this.gameState.scoringAnimations = []
    this.gameState.phase = 'finalScoring'

    this.showMessage('最终计分中...')

    // 开始逐个玩家的最终计分动画
    this.startMultiplayerFinalScoring(finalScoringDetails.playerScores, 0, callback)
  }

  // 开始多人游戏最终计分动画（逐个玩家）
  startMultiplayerFinalScoring(playerScores, playerIndex, finalCallback) {
    if (playerIndex >= playerScores.length) {
      // 所有玩家最终计分完毕
      setTimeout(() => {
        this.gameState.scoringAnimations = []
        this.gameState.scoringPlayer = undefined
        this.gameState.phase = 'end'
        this.render()
        if (finalCallback) finalCallback()
      }, 500)
      return
    }

    const playerScore = playerScores[playerIndex]
    const player = this.gameState.players.find(p => p.id === playerScore.playerId)

    if (!player) {
      // 玩家不存在，跳到下一个
      this.startMultiplayerFinalScoring(playerScores, playerIndex + 1, finalCallback)
      return
    }

    // 设置当前计分玩家
    this.gameState.scoringPlayer = this.gameState.players.indexOf(player)
    console.log(`开始玩家${this.gameState.scoringPlayer + 1}(${player.name})的最终计分`)

    // 最终计分时自动切换到对应玩家的面板
    this.currentViewingPlayer = this.gameState.scoringPlayer
    console.log(`多人最终计分阶段：自动切换到玩家${this.gameState.scoringPlayer + 1}的面板`)

    let animationDelay = 0
    const animationInterval = 1000 // 每个动画间隔1秒

    // 完整行奖励 - 需要找出具体完成了哪些行
    if (playerScore.bonuses && playerScore.bonuses.completeRows && playerScore.bonuses.completeRows.count > 0) {
      for (let row = 0; row < 5; row++) {
        if (player.wall[row].every(cell => cell.filled)) {
          setTimeout(() => {
            this.showFinalScoreAnimationMultiplayer(2, `完成第${row + 1}行`, player, 'row', row)
          }, animationDelay)
          animationDelay += animationInterval
        }
      }
    }

    // 完整列奖励 - 需要找出具体完成了哪些列
    if (playerScore.bonuses && playerScore.bonuses.completeColumns && playerScore.bonuses.completeColumns.count > 0) {
      for (let col = 0; col < 5; col++) {
        if (player.wall.every(row => row[col].filled)) {
          setTimeout(() => {
            this.showFinalScoreAnimationMultiplayer(7, `完成第${col + 1}列`, player, 'column', col)
          }, animationDelay)
          animationDelay += animationInterval
        }
      }
    }

    // 同色收集奖励 - 需要找出具体完成了哪些颜色
    if (playerScore.bonuses && playerScore.bonuses.completeColors && playerScore.bonuses.completeColors.count > 0) {
      const colors = GAME_CONFIG.COLORS // ['blue', 'yellow', 'red', 'black', 'white']
      colors.forEach(color => {
        let colorCount = 0
        player.wall.forEach(row => {
          row.forEach(cell => {
            if (cell.color === color && cell.filled) {
              colorCount++
            }
          })
        })
        if (colorCount === 5) {
          setTimeout(() => {
            this.showFinalScoreAnimationMultiplayer(10, `收集5个${color}磁砖`, player, 'color', color)
          }, animationDelay)
          animationDelay += animationInterval
        }
      })
    }

    // 如果没有任何奖励，直接进入下一个玩家
    if (animationDelay === 0) {
      setTimeout(() => {
        this.startMultiplayerFinalScoring(playerScores, playerIndex + 1, finalCallback)
      }, 500)
    } else {
      // 等待所有动画完成后进入下一个玩家
      setTimeout(() => {
        this.startMultiplayerFinalScoring(playerScores, playerIndex + 1, finalCallback)
      }, animationDelay + 500)
    }
  }

  // 显示游戏结果
  showGameResult(gameEndData) {
    console.log('显示游戏结果:', gameEndData)

    const winner = gameEndData.winner
    let resultMessage = ''

    if (Array.isArray(winner)) {
      resultMessage = `平局！获胜者: ${winner.map(p => p.name).join(', ')}`
    } else {
      resultMessage = `游戏结束！获胜者: ${winner.name}，分数: ${winner.score}`
    }

    // 显示结果
    this.showMessage(resultMessage)

    // 可以在这里添加更详细的结果显示界面
    setTimeout(() => {
      wx.showModal({
        title: '游戏结束',
        content: resultMessage,
        showCancel: false,
        confirmText: '确定'
      })
    }, 1000)
  }

  // 重写计时器相关方法（联机游戏由服务端控制计时）
  startTurnTimer() {
    console.log('联机游戏计时器由服务端控制，启动客户端显示倒计时')
    // 启动客户端倒计时显示
    this.startClientTimer()
  }

  clearTurnTimer() {
    console.log('联机游戏清除客户端倒计时显示')
    // 清理客户端倒计时显示
    this.clearClientTimer()
  }

  handleTurnTimeout() {
    console.log('联机游戏超时由服务端处理，客户端显示超时提示')
    this.showMessage('回合超时，AI代替操作')
  }

  // 启动客户端倒计时显示
  startClientTimer() {
    // 如果已经有倒计时在运行，检查是否是同一个玩家的回合
    if (this.currentRemainingTime > 0 && this.clientTimerInterval) {
      console.log('倒计时已在运行，不重复请求')
      return
    }

    // 清除之前的计时器
    this.clearClientTimer()

    // 只在回合开始时获取一次剩余时间
    this.requestRemainingTime()
  }

  // 清除客户端倒计时显示
  clearClientTimer() {
    if (this.clientTimerInterval) {
      clearInterval(this.clientTimerInterval)
      this.clientTimerInterval = null
    }
    // 完全清理时才重置所有时间变量
    this.remainingTime = 0
    this.timerStartTime = 0
    this.currentRemainingTime = 0

    // 确保渲染循环继续运行
    if (this.isRunning && !this.destroyed) {
      this.render()
    }
  }

  // 请求剩余时间（只在回合开始时调用一次）
  requestRemainingTime() {
    console.log('请求剩余时间，房间ID:', this.multiplayerManager?.roomId)
    if (this.multiplayerManager && this.multiplayerManager.roomId) {
      this.multiplayerManager.sendMessage({
        type: 'getRemainingTime',
        data: {
          roomId: this.multiplayerManager.roomId
        }
      })
      console.log('已发送获取剩余时间请求')
    } else {
      console.log('无法请求剩余时间：multiplayerManager或roomId不存在')
    }
  }

  // 处理剩余时间更新
  handleRemainingTimeUpdate(data) {
    console.log('收到剩余时间更新:', data)
    const serverRemainingTime = Math.max(0, Math.ceil(data.remainingTime / 1000))

    // 设置客户端倒计时
    this.remainingTime = serverRemainingTime
    this.timerStartTime = Date.now()

    console.log('设置客户端倒计时:', serverRemainingTime, '秒')

    // 启动客户端倒计时
    this.startLocalCountdown()
  }

  // 启动本地倒计时
  startLocalCountdown() {
    console.log('启动本地倒计时，剩余时间:', this.remainingTime)

    // 只清除定时器，不重置时间相关变量
    if (this.clientTimerInterval) {
      clearInterval(this.clientTimerInterval)
      this.clientTimerInterval = null
    }

    if (this.remainingTime <= 0) {
      console.log('剩余时间为0，不启动倒计时')
      return
    }

    // 每秒更新一次本地倒计时
    this.clientTimerInterval = setInterval(() => {
      if (this.timerStartTime > 0) {
        const elapsed = Math.floor((Date.now() - this.timerStartTime) / 1000)
        const newRemainingTime = Math.max(0, this.remainingTime - elapsed)

        // 更新显示的剩余时间
        this.currentRemainingTime = newRemainingTime

        // 每5秒输出一次调试信息
        if (elapsed % 5 === 0) {
          console.log(`倒计时更新: 已过${elapsed}秒, 剩余${newRemainingTime}秒`)
        }

        // 如果时间到了，停止倒计时
        if (newRemainingTime <= 0) {
          console.log('倒计时结束，等待服务端超时处理...')
          this.clearClientTimer()

          // 显示等待提示
          this.showMessage('等待服务器处理超时...')

          // 确保渲染循环继续运行
          this.render()

          // 设置客户端超时保护，如果10秒内没有收到服务端响应，主动处理
          this.serverTimeoutProtection = setTimeout(() => {
            // 检查游戏是否已经结束
            if (this.gameState && this.gameState.gameEnded) {
              console.log('游戏已结束，忽略超时保护')
              return
            }

            // 检查是否在计分阶段
            if (this.gameState && this.gameState.phase === 'scoring') {
              console.log('正在计分阶段，忽略超时保护')
              return
            }

            // 检查是否有动画在播放
            if (this.isPlayingAnimation) {
              console.log('动画播放中，忽略超时保护')
              return
            }

            console.log('服务端超时保护触发，直接处理超时')
            this.handleServerTimeout()
          }, 10000) // 10秒超时保护
        }
      } else {
        console.log('timerStartTime为0，倒计时无法工作')
      }
    }, 1000)

    // 初始化当前剩余时间
    this.currentRemainingTime = this.remainingTime
    console.log('本地倒计时启动完成，当前剩余时间:', this.currentRemainingTime)
  }

  // 处理服务端超时
  handleServerTimeout() {
    console.log('处理服务端超时')
    this.showMessage('服务器响应超时，请重新开始游戏')

    // 清理游戏状态，但保留rankedUI引用
    this.isMultiplayerMode = false
    if (this.multiplayerManager) {
      this.multiplayerManager.cleanup()
    }

    // 确保rankedUI引用存在
    if (!this.rankedUI) {
      // 尝试多种方式恢复rankedUI引用
      if (window.game && window.game.rankedUI) {
        this.rankedUI = window.game.rankedUI
        console.log('从window.game恢复rankedUI引用')
      } else if (window.rankedUI) {
        this.rankedUI = window.rankedUI
        console.log('从window.rankedUI恢复引用')
      } else {
        console.log('无法恢复rankedUI引用，将重新加载页面')
      }
    }

    // 返回排位赛主界面
    setTimeout(() => {
      if (this.rankedUI) {
        this.rankedUI.showMainScreen()
      } else {
        // 如果没有rankedUI，尝试重新加载页面或返回主界面
        console.log('没有rankedUI，尝试重新加载')
        if (typeof wx !== 'undefined' && wx.exitMiniProgram) {
          wx.showModal({
            title: '游戏异常',
            content: '游戏出现异常，请重新进入',
            showCancel: false,
            confirmText: '确定',
            success: () => {
              wx.exitMiniProgram()
            }
          })
        } else {
          // 非微信环境，尝试刷新页面
          if (typeof location !== 'undefined') {
            location.reload()
          }
        }
      }
    }, 3000)

    // 初始化当前剩余时间
    this.currentRemainingTime = this.remainingTime
    console.log('本地倒计时启动完成，当前剩余时间:', this.currentRemainingTime)
  }

  // 处理游戏开始
  handleGameStarted(data) {
    console.log('处理游戏开始:', data)

    // 如果包含剩余时间信息，先设置倒计时
    if (data.remainingTime !== undefined) {
      console.log('游戏开始时设置倒计时:', data.remainingTime)
      this.handleRemainingTimeUpdate({
        remainingTime: data.remainingTime,
        currentPlayer: data.gameState ? data.gameState.currentPlayer : 0,
        timestamp: Date.now()
      })
    }

    // 更新游戏状态（这会触发 startTurnTimer，但如果已经有倒计时就不会重复请求）
    if (data.gameState) {
      this.updateGameState(data.gameState)
    }

    this.showMessage('游戏开始！')
  }

  // 处理玩家超时
  handlePlayerTimeout(data) {
    console.log('处理玩家超时:', data)
    console.log('当前游戏状态中的玩家:', this.gameState.players.map(p => ({ id: p.id, name: p.name })))

    // 清除客户端超时保护
    if (this.serverTimeoutProtection) {
      clearTimeout(this.serverTimeoutProtection)
      this.serverTimeoutProtection = null
    }

    // 使用正确的字段名
    const timeoutPlayerId = data.timeoutPlayerId || data.playerId
    console.log('超时玩家ID:', timeoutPlayerId)

    const timeoutPlayer = this.gameState.players.find(p => p.id === timeoutPlayerId)
    console.log('找到的超时玩家:', timeoutPlayer)

    if (timeoutPlayer) {
      this.showMessage(`${timeoutPlayer.name} 超时，AI代替操作`)
    } else {
      console.log('未找到超时玩家，使用ID显示')
      this.showMessage(`玩家 ${timeoutPlayerId} 超时，AI代替操作`)
    }

    // 显示AI动作信息
    if (data.aiMove) {
      console.log('AI执行动作:', data.aiMove)
    }

    // 更新游戏状态
    if (data.gameState) {
      this.updateGameState(data.gameState)
    }

    // 不在这里清除计时器，让updateGameState来处理新的计时器启动
  }

  // 处理房间关闭
  handleRoomClosed(data) {
    console.log('收到房间关闭消息:', data)

    // 检查是否是当前房间
    if (this.multiplayerManager && this.multiplayerManager.roomId) {
      const currentRoomId = this.multiplayerManager.roomId
      console.log('当前房间ID:', currentRoomId)

      // 如果是当前房间被关闭，需要处理
      if (data.roomId === currentRoomId || !data.roomId) {
        console.log('当前游戏房间被关闭，原因:', data.reason)

        // 清理游戏状态
        this.isMultiplayerMode = false
        this.multiplayerManager.cleanup()

        // 显示提示信息
        this.showMessage(`房间已关闭: ${data.reason}`)

        // 如果是排位赛，返回排位赛主界面
        if (currentRoomId && currentRoomId.startsWith('ranked_')) {
          setTimeout(() => {
            if (this.rankedUI) {
              this.rankedUI.showMainScreen()
            } else {
              this.showMainMenu()
            }
          }, 2000)
        } else {
          // 普通房间，返回主菜单
          setTimeout(() => {
            this.showMainMenu()
          }, 2000)
        }
      } else {
        console.log('收到其他房间的关闭消息，忽略')
      }
    } else {
      console.log('当前没有活跃房间，忽略房间关闭消息')
    }
  }

  // 重写计时器渲染方法（联机游戏专用）
  renderTimer() {
    // 只在收集阶段显示计时器
    if (this.gameState.phase !== 'collect') {
      return
    }

    // 使用本地倒计时的剩余时间
    const timeSeconds = this.currentRemainingTime || 0
    if (timeSeconds <= 0) {
      return
    }

    const safeAreaTop = 40  // 头部安全区域
    const { width, height } = this.getLogicalSize()

    // 计时器背景
    const timerWidth = 80
    const timerHeight = 30
    const timerX = width - timerWidth - 20  // 右侧位置
    const timerY = safeAreaTop + 30  // 适中位置，避开关闭按钮但不挡住工厂

    this.ctx.fillStyle = timeSeconds <= 5 ? '#dc3545' : '#28a745'  // 最后5秒变红
    this.ctx.fillRect(timerX, timerY, timerWidth, timerHeight)

    // 计时器边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(timerX, timerY, timerWidth, timerHeight)

    // 计时器文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText(`${timeSeconds}s`, timerX + timerWidth / 2, timerY + timerHeight / 2 + 6)

    // 当前玩家提示
    if (this.isMyTurn) {
      this.ctx.fillStyle = '#007bff'
      this.ctx.font = '12px Arial'
      this.ctx.fillText('你的回合', timerX + timerWidth / 2, timerY - 5)
    }
  }

  // 重写中央区域渲染（联机游戏专用）
  renderCenterArea() {
    // 安全检查：确保游戏状态和必要属性存在
    if (!this.gameState || !this.gameState.factories || !this.gameState.centerArea) {
      console.warn('游戏状态或中央区域不存在，跳过中央区域渲染')
      return
    }

    const safeAreaTop = 40  // 与头部保持一致的安全区域偏移

    // 动态计算中央区域位置，避免与工厂重合（与单机版本保持一致）
    const factorySize = 60
    const margin = 5
    const factoryStartY = 100 + safeAreaTop
    const { width } = this.getLogicalSize()
    const maxFactoriesPerRow = Math.floor((width - 40) / (factorySize + margin))
    const factoryRows = Math.ceil(this.gameState.factories.length / maxFactoriesPerRow)
    const factoryEndY = factoryStartY + factoryRows * (factorySize + margin + 10) - 10

    // 中央区域位置在工厂下方，留出适当间距
    const centerY = factoryEndY + 30

    // 标题
    this.ctx.fillStyle = '#495057'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('中央区域', width / 2, centerY)

    // 起始玩家磁砖 - 使用联机游戏的字段名
    if (this.gameState.firstPlayerMarker) {
      const tileSize = 32
      const tileX = width / 2 - tileSize / 2
      const tileY = centerY + 10

      // 背景
      this.ctx.fillStyle = '#ffd700'
      this.ctx.fillRect(tileX, tileY, tileSize, tileSize)

      // 边框
      this.ctx.strokeStyle = '#f39c12'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(tileX, tileY, tileSize, tileSize)

      // 文字
      this.ctx.fillStyle = '#2c3e50'
      this.ctx.font = 'bold 14px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('起始', width / 2, tileY + tileSize / 2)
    }

    // 中央磁砖 - 使用与单机版本相同的分组显示方式
    const centerTiles = this.groupCenterTiles()
    centerTiles.forEach((group, index) => {
      const x = width / 2 - (centerTiles.length * 35) / 2 + index * 35
      const y = centerY + 50

      this.renderTile(group.color, x, y, 25)

      if (group.count > 1) {
        this.ctx.fillStyle = '#e74c3c'
        this.ctx.fillRect(x + 20, y - 5, 15, 15)
        this.ctx.fillStyle = '#ffffff'
        this.ctx.font = '10px Arial'
        this.ctx.textAlign = 'center'
        this.ctx.fillText(group.count.toString(), x + 27, y + 5)
      }
    })
  }

  // 重写下一个玩家方法（联机游戏由服务端控制）
  nextPlayer() {
    console.log('联机游戏玩家切换由服务端控制')
    // 联机游戏的玩家切换由服务端控制，客户端只需要等待状态更新
  }

  // 重写AI检查方法（联机游戏可能没有AI或由服务端控制）
  checkAndExecuteAITurn() {
    console.log('联机游戏AI由服务端控制')
    // 联机游戏的AI由服务端控制
  }

  // 重写工厂选择方法（联机游戏专用）
  showFactorySelection(factoryIndex) {
    console.log(`联机游戏显示工厂${factoryIndex + 1}的颜色选择`)

    // 设置处理标志位，防止重复点击
    this.processingSelection = true

    this.gameState.showingFactorySelection = true
    this.gameState.selectedFactory = factoryIndex

    console.log('联机游戏设置工厂选择状态:', {
      showingFactorySelection: this.gameState.showingFactorySelection,
      selectedFactory: this.gameState.selectedFactory,
      factory: this.gameState.factories[factoryIndex]
    })

    // 强制立即渲染
    this.forceRender()

    // 延迟重置标志位，确保渲染完成
    setTimeout(() => {
      this.processingSelection = false
      console.log('重置处理选择标志位')
    }, 200)
  }

  // 重写中央区域选择方法（联机游戏专用）
  showCenterSelection() {
    console.log('显示中央区域的颜色选择')

    // 设置处理标志位，防止重复点击
    this.processingSelection = true

    this.gameState.showingCenterSelection = true

    // 立即重新渲染以设置点击区域
    this.render()

    // 延迟重置标志位，确保渲染完成
    setTimeout(() => {
      this.processingSelection = false
    }, 200)
  }

  // 强制渲染方法
  forceRender() {
    console.log('强制渲染联机游戏界面')
    console.log('当前游戏状态:', {
      showingFactorySelection: this.gameState.showingFactorySelection,
      selectedFactory: this.gameState.selectedFactory,
      factoryCount: this.gameState.factories.length
    })

    try {
      this.render()
      console.log('强制渲染完成')

      // 检查渲染后的状态
      setTimeout(() => {
        console.log('渲染后检查 factoryColorAreas:', this.factoryColorAreas ? this.factoryColorAreas.length : 'undefined')
      }, 100)
    } catch (error) {
      console.error('强制渲染失败:', error)
    }
  }

  // 处理玩家动作
  handlePlayerAction(actionData) {
    console.log('处理玩家动作:', actionData)

    // 这里可以添加动作动画或特效
    const playerName = actionData.playerInfo?.nickName || '玩家'
    this.showMessage(`${playerName} 进行了操作`)
  }

  // 处理游戏结束
  handleGameEnd(endData) {
    console.log('🎮 处理游戏结束:', endData)

    // 清除客户端超时保护
    if (this.serverTimeoutProtection) {
      clearTimeout(this.serverTimeoutProtection)
      this.serverTimeoutProtection = null
      console.log('游戏结束，清除超时保护')
    }

    // 在多人游戏中，需要特殊处理最终计分动画
    const isMultiplayerGame = this.isMultiplayer || (this.multiplayerManager && this.multiplayerManager.connected)

    if (isMultiplayerGame && endData.gameState && endData.finalScoringDetails) {
      // 保存服务端发送的最终分数
      const finalScores = {}
      endData.gameState.players.forEach(player => {
        finalScores[player.id] = player.score
      })

      // 计算每个玩家的原始分数（最终分数减去奖励分数）
      endData.finalScoringDetails.playerScores.forEach(playerScore => {
        const player = endData.gameState.players.find(p => p.id === playerScore.playerId)
        if (player) {
          const originalScore = playerScore.previousScore
          console.log(`恢复玩家 ${player.nickName} 的原始分数: ${player.score} → ${originalScore}`)
          player.score = originalScore // 恢复到动画前的分数
        }
      })
    }

    // 更新游戏状态
    if (endData.gameState) {
      this.updateGameState(endData.gameState)
    }

    this.gameState.gameEnded = true
    this.gameState.winner = endData.winner

    // 游戏结束时，停止所有游戏逻辑交互
    this.isMyTurn = false
    this.waitingForServer = false

    console.log('游戏状态已更新，游戏结束标志已设置，准备播放最终计分动画')

    // 播放最终计分动画
    if (endData.animationData && endData.finalScoringDetails) {
      this.playFinalScoringAnimation(endData.animationData, endData.finalScoringDetails, () => {
        // 最终计分动画完成后显示结束对话框
        console.log('最终计分动画完成，显示游戏结束对话框')
        this.showGameEndDialog(endData)
      })
    } else {
      // 没有动画数据，直接显示结束对话框
      this.render()
      setTimeout(() => {
        console.log('显示游戏结束对话框')
        this.showGameEndDialog(endData)
      }, 500)
    }
  }

  // 处理排位赛结束（积分变化）
  handleRankedGameCompleted(data) {
    console.log('🏆 处理排位赛结束数据:', data)
    console.log('🏆 当前游戏状态:', {
      hasGameState: !!this.gameState,
      gameEnded: this.gameState?.gameEnded,
      gameConfig: this.gameConfig
    })

    // 保存排位赛结果数据，用于在游戏结束对话框中显示
    this.rankedGameResult = data
    console.log('🏆 排位赛结果已保存:', this.rankedGameResult)

    // 如果游戏已经结束，显示积分变化动画
    if (this.gameState && this.gameState.gameEnded) {
      console.log('🏆 游戏已结束，显示积分变化动画')
      this.showRankedRatingAnimation(data)
    } else {
      console.log('🏆 游戏尚未结束，等待游戏结束后显示积分变化')
    }
  }

  // 显示排位赛积分变化动画
  showRankedRatingAnimation(rankedData) {
    if (!rankedData || !rankedData.ratingChange) {
      return
    }

    console.log('开始显示排位赛积分变化动画')

    const change = rankedData.ratingChange
    const isPositive = change.ratingChange > 0
    const changeText = isPositive ? `+${change.ratingChange}` : `${change.ratingChange}`

    // 延迟显示积分动画，让最终计分动画先完成
    setTimeout(() => {
      console.log('显示积分变化动画:', changeText)

      // 添加积分变化动画
      this.gameState.scoringAnimations.push({
        type: 'ratingChange',
        ratingChange: change.ratingChange,
        oldRating: change.oldRating,
        newRating: change.newRating,
        placement: change.placement,
        tierChanged: change.tierChanged,
        newTier: change.newTier,
        startTime: Date.now(),
        duration: 3000 // 3秒动画
      })

      // 显示积分变化Toast
      if (typeof wx !== 'undefined') {
        wx.showToast({
          title: `积分${changeText}`,
          icon: isPositive ? 'success' : 'none',
          duration: 2000
        })
      }

      console.log(`排位赛结果: 排名第${change.placement}名，积分${changeText}，新积分${change.newRating}`)
    }, 1000) // 延迟1秒，让最终计分动画先完成
  }

  // 显示排位赛积分变化信息（保留原方法作为备用）
  showRankedResultInfo(rankedData) {
    if (!rankedData || !rankedData.ratingChange) {
      return
    }

    const change = rankedData.ratingChange
    const isPositive = change.ratingChange > 0
    const changeText = isPositive ? `+${change.ratingChange}` : `${change.ratingChange}`

    let message = `排位赛结束！\n`
    message += `排名: 第${change.placement}名\n`
    message += `积分变化: ${changeText}\n`
    message += `当前积分: ${change.newRating}\n`
    message += `当前段位: ${change.newTier.tierName} ${change.newTier.division}`

    if (change.tierChanged) {
      message += `\n🎉 段位提升！`
    }

    // 显示积分变化提示
    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: `积分${changeText}`,
        icon: isPositive ? 'success' : 'none',
        duration: 3000
      })
    }

    console.log('排位赛结果:', message)
  }

  // 处理玩家断线
  handlePlayerDisconnected(data) {
    console.log('处理玩家断线:', data)

    const disconnectedPlayer = this.gameState.players.find(p => p.id === data.playerId)
    if (disconnectedPlayer) {
      this.showMessage(`${disconnectedPlayer.name} 断线了`)
    }
  }

  // 处理玩家重连
  handlePlayerReconnected(data) {
    console.log('处理玩家重连:', data)

    const reconnectedPlayer = this.gameState.players.find(p => p.id === data.playerId)
    if (reconnectedPlayer) {
      this.showMessage(`${reconnectedPlayer.name} 重新连接`)
    }
  }

  // 处理房间关闭
  handleRoomClosed(data) {
    console.log('处理房间关闭:', data)

    // 检查是否为游戏正常结束后的自动清理
    const reason = data.reason || '未知原因'
    const isGameEndCleanup = reason.includes('游戏结束后自动清理') ||
                            reason.includes('游戏结束') ||
                            reason.includes('自动清理')

    console.log('房间关闭检查:', {
      reason: reason,
      isGameEndCleanup: isGameEndCleanup,
      hasGameState: !!this.gameState,
      gameEnded: this.gameState?.gameEnded,
      gameEndDialogShown: this.gameEndDialogShown,
      showReturnButton: this.showReturnButton
    })

    // 如果是游戏结束后的自动清理
    if (isGameEndCleanup) {
      console.log('检测到游戏结束后自动清理')

      // 如果游戏结束对话框已经显示或者显示了返回按钮，不清理游戏资源，让用户自己选择
      if (this.gameEndDialogShown || this.showReturnButton) {
        console.log('游戏结束界面已显示，保持游戏界面，等待用户选择')
        // 只清理网络连接，但保留游戏界面
        this.cleanupNetworkOnly()
        return
      }

      // 如果对话框还没显示，清理游戏资源并静默返回大厅
      console.log('游戏结束对话框未显示，清理资源并静默返回大厅')
      this.cleanup()
      setTimeout(() => {
        this.returnToLobby()
      }, 100) // 稍微延迟，确保其他清理工作完成
      return
    }

    // 非游戏结束的房间关闭，正常清理资源
    this.cleanup()

    // 只有在异常情况下才显示房间关闭提示
    if (typeof wx !== 'undefined') {
      wx.showModal({
        title: '房间已关闭',
        content: `房间因为"${reason}"被关闭`,
        showCancel: false,
        confirmText: '返回大厅',
        success: () => {
          this.returnToLobby()
        }
      })
    } else {
      alert(`房间因为"${reason}"被关闭`)
      this.returnToLobby()
    }
  }

  // 处理自己断线
  handleSelfDisconnected() {
    console.log('处理自己断线')
    this.showMessage('与服务器断开连接，正在尝试重连...')

    // 标记重连状态
    this.isReconnecting = true
    this.render() // 重新渲染以显示重连状态
  }

  // 处理重连成功
  handleReconnected() {
    console.log('处理重连成功')
    this.showMessage('重新连接成功！')

    this.isReconnecting = false

    // 重连后重新请求游戏状态和倒计时
    if (this.multiplayerManager && this.multiplayerManager.roomId) {
      console.log('重连成功，重新同步游戏状态')
      // 重新启动倒计时
      this.startTurnTimer()
    }

    this.render() // 重新渲染
  }

  // 渲染重连状态
  renderReconnectingStatus() {
    const { width, height } = this.getLogicalSize()

    // 半透明遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(0, 0, width, height)

    // 重连提示框
    const boxWidth = 300
    const boxHeight = 120
    const boxX = (width - boxWidth) / 2
    const boxY = (height - boxHeight) / 2

    // 背景
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(boxX, boxY, boxWidth, boxHeight)

    // 边框
    this.ctx.strokeStyle = '#cccccc'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(boxX, boxY, boxWidth, boxHeight)

    // 文字
    this.ctx.fillStyle = '#333333'
    this.ctx.font = '20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('正在重连...', width / 2, boxY + 50)

    this.ctx.font = '16px Arial'
    this.ctx.fillStyle = '#666666'
    this.ctx.fillText('请稍候', width / 2, boxY + 80)
  }

  // 处理玩家离开
  handlePlayerLeft(data) {
    console.log('处理玩家离开:', data)

    this.showMessage(`玩家 ${data.playerName || '某位玩家'} 离开了游戏`)

    // 可以选择暂停游戏或继续游戏
    // 这里暂时显示提示
  }

  // 显示消息
  showMessage(text) {
    console.log('游戏消息:', text)

    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: text,
        icon: 'none',
        duration: 2000
      })
    } else {
      // 在非微信环境中，只在控制台输出，不显示UI提示
      // 避免触发不必要的加载动画或其他UI元素
      console.log('消息提示:', text)
    }
  }

  // 根据玩家ID获取玩家名称
  getPlayerNameById(playerId) {
    if (!this.gameState || !this.gameState.players) {
      return null
    }

    const player = this.gameState.players.find(p => p.id === playerId)
    return player ? (player.nickName || player.name || '未知玩家') : null
  }

  // 显示游戏结束对话框
  showGameEndDialog(endData) {
    console.log('显示游戏结束对话框:', endData)
    console.log('当前游戏配置:', this.gameConfig)
    console.log('排位赛结果数据:', this.rankedGameResult)

    const winner = endData.winner

    // 处理平局情况（winner可能是数组）
    let isWinner = false
    let title = '游戏结束'
    let message = ''

    if (Array.isArray(winner)) {
      // 平局情况
      const winnerNames = winner.map(w => w.nickName || w.name || '未知').join(', ')
      const winnerScore = winner.length > 0 ? winner[0].score : 0
      isWinner = winner.some(w => w.id === this.localPlayerId)
      title = isWinner ? '🎉 平局获胜！' : '平局'
      message = `平局获胜者: ${winnerNames}\n最终得分: ${winnerScore}`
    } else if (winner) {
      // 单独获胜者
      isWinner = winner.id === this.localPlayerId
      title = isWinner ? '🎉 恭喜获胜！' : '游戏结束'
      const winnerName = winner.nickName || winner.name || '未知'
      message = `获胜者: ${winnerName}\n最终得分: ${winner.score || 0}`
    } else {
      // 没有获胜者信息
      message = '游戏已结束'
    }

    // 播放游戏结束音效
    if (window.gameApp) {
      if (isWinner) {
        // 获胜音效
        window.gameApp.playSound('gameWin')
        console.log('🎵 播放获胜音效')
      } else {
        // 游戏结束音效
        window.gameApp.playSound('roundEnd')
        console.log('🎵 播放游戏结束音效')
      }
    }

    console.log('游戏结束对话框内容:', { title, message, isWinner })

    // 检查是否为排位赛
    const isRankedGame = this.gameConfig && this.gameConfig.isRanked
    console.log('排位赛检查:', {
      hasGameConfig: !!this.gameConfig,
      isRanked: this.gameConfig?.isRanked,
      roomId: this.gameConfig?.roomId,
      isRankedGame: isRankedGame
    })

    // 如果是排位赛且有积分变化数据，添加到消息中
    if (isRankedGame && this.rankedGameResult && this.rankedGameResult.ratingChange) {
      const change = this.rankedGameResult.ratingChange
      const isPositive = change.ratingChange > 0
      const changeText = isPositive ? `+${change.ratingChange}` : `${change.ratingChange}`

      // 简化排位赛结果显示
      message += `\n\n🏆 排位赛结果`
      message += `\n排名: 第${change.placement}名`
      message += `\n积分: ${changeText} (${change.newRating})`

      // 段位信息
      if (change.newTier && change.newTier.tierName) {
        const tierText = change.newTier.division ?
          `${change.newTier.tierName} ${change.newTier.division}` :
          change.newTier.tierName
        message += `\n段位: ${tierText}`
      }

      // 段位提升提示
      if (change.tierChanged) {
        message += `\n🎉 段位提升！`
      }
    }

    if (typeof wx !== 'undefined') {
      console.log('微信环境检测到wx.showModal可能不稳定，直接使用游戏内对话框')

      // 设置游戏结束标志，防止房间清理时自动返回大厅
      this.gameEndDialogShown = true

      // 直接显示游戏内自定义对话框
      this.showCustomGameEndDialog(title, message, isRankedGame, isWinner)
    } else {
      console.log('非微信环境，显示游戏结束信息')
      // 非微信环境，显示游戏结束信息但不自动返回大厅
      alert(message)
    }
  }

  // 显示自定义游戏结束对话框
  showCustomGameEndDialog(title, message, isRankedGame, isWinner) {
    console.log('显示自定义游戏结束对话框')

    // 设置对话框数据
    this.customDialog = {
      show: true,
      title: title,
      message: message,
      isRankedGame: isRankedGame,
      isWinner: isWinner,
      buttons: [
        {
          text: '继续查看',
          action: 'continue',
          color: '#666666'
        },
        {
          text: isRankedGame ? '返回排位赛' : '返回大厅',
          action: 'return',
          color: isRankedGame ? '#FF6B6B' : '#4CAF50'
        }
      ]
    }

    // 重新渲染以显示对话框
    this.render()

    // 绑定对话框事件
    this.bindCustomDialogEvents()
  }

  // 绑定自定义对话框事件
  bindCustomDialogEvents() {
    if (this.customDialogHandler) {
      return // 避免重复绑定
    }

    this.customDialogHandler = (e) => {
      if (!this.customDialog || !this.customDialog.show) {
        return
      }

      let x, y
      if (e.touches && e.touches.length > 0) {
        x = e.touches[0].clientX
        y = e.touches[0].clientY
      } else {
        x = e.clientX
        y = e.clientY
      }

      // 转换为Canvas坐标
      const rect = this.canvas.getBoundingClientRect ? this.canvas.getBoundingClientRect() : { left: 0, top: 0 }
      x = x - rect.left
      y = y - rect.top

      // 检查是否点击了对话框按钮
      const clickedButton = this.getClickedDialogButton(x, y)
      if (clickedButton) {
        console.log('点击了对话框按钮:', clickedButton.action)
        this.handleCustomDialogClick(clickedButton.action)
      }
    }

    if (typeof wx !== 'undefined') {
      wx.onTouchStart(this.customDialogHandler)
    } else if (this.canvas) {
      this.canvas.addEventListener('click', this.customDialogHandler)
    }
  }

  // 获取点击的对话框按钮
  getClickedDialogButton(x, y) {
    if (!this.customDialog || !this.customDialog.buttonRects) {
      return null
    }

    for (let i = 0; i < this.customDialog.buttonRects.length; i++) {
      const rect = this.customDialog.buttonRects[i]
      if (x >= rect.x && x <= rect.x + rect.width &&
          y >= rect.y && y <= rect.y + rect.height) {
        return this.customDialog.buttons[i]
      }
    }

    return null
  }

  // 处理自定义对话框点击
  handleCustomDialogClick(action) {
    console.log('处理自定义对话框点击:', action)

    // 隐藏对话框
    this.customDialog.show = false
    this.gameEndDialogShown = false

    // 清理事件监听器
    if (this.customDialogHandler) {
      if (typeof wx !== 'undefined') {
        wx.offTouchStart(this.customDialogHandler)
      } else if (this.canvas) {
        this.canvas.removeEventListener('click', this.customDialogHandler)
      }
      this.customDialogHandler = null
    }

    // 执行对应操作
    if (action === 'return') {
      console.log('用户选择返回')
      this.returnToLobby()
    } else if (action === 'continue') {
      console.log('用户选择继续查看游戏')
      // 显示返回按钮，让用户可以随时返回
      this.showGameEndReturnButton(this.customDialog.isRankedGame)
      // 重新渲染以隐藏对话框并显示返回按钮
      this.render()
    }
  }

  // 显示游戏结束返回按钮
  showGameEndReturnButton(isRankedGame) {
    console.log('显示游戏结束返回按钮')

    // 设置返回按钮标志
    this.showReturnButton = true
    this.isRankedGame = isRankedGame

    // 立即渲染并绑定事件（不需要延迟，因为用户已经看过对话框了）
    if (!this.destroyed) {
      console.log('渲染返回按钮')
      this.render()
      // 绑定返回按钮事件
      this.bindReturnButtonEvents()
    }
  }

  // 绑定返回按钮事件
  bindReturnButtonEvents() {
    if (this.returnButtonHandler) {
      return // 避免重复绑定
    }

    this.returnButtonHandler = (e) => {
      if (!this.showReturnButton) {
        return
      }

      let x, y
      if (e.touches && e.touches.length > 0) {
        x = e.touches[0].clientX
        y = e.touches[0].clientY
      } else {
        x = e.clientX
        y = e.clientY
      }

      // 转换为Canvas坐标
      const rect = this.canvas.getBoundingClientRect ? this.canvas.getBoundingClientRect() : { left: 0, top: 0 }
      x = x - rect.left
      y = y - rect.top

      // 检查是否点击了返回按钮
      if (this.isPointInReturnButton(x, y)) {
        console.log('点击了返回按钮')
        this.handleReturnButtonClick()
      }
    }

    if (typeof wx !== 'undefined') {
      wx.onTouchStart(this.returnButtonHandler)
    } else if (this.canvas) {
      this.canvas.addEventListener('click', this.returnButtonHandler)
    }
  }

  // 检查点击是否在返回按钮内
  isPointInReturnButton(x, y) {
    if (!this.returnButtonRect) {
      return false
    }

    const btn = this.returnButtonRect
    return x >= btn.x && x <= btn.x + btn.width &&
           y >= btn.y && y <= btn.y + btn.height
  }

  // 处理返回按钮点击
  handleReturnButtonClick() {
    console.log('处理返回按钮点击')

    // 清理返回按钮
    this.showReturnButton = false
    this.gameEndDialogShown = false

    // 清理事件监听器
    if (this.returnButtonHandler) {
      if (typeof wx !== 'undefined') {
        wx.offTouchStart(this.returnButtonHandler)
      } else if (this.canvas) {
        this.canvas.removeEventListener('click', this.returnButtonHandler)
      }
      this.returnButtonHandler = null
    }

    // 返回对应界面
    this.returnToLobby()
  }




  // 返回联机大厅
  returnToLobby() {
    console.log('返回联机大厅')

    // 清理游戏资源
    this.cleanup()

    // 通知GameApp返回对应菜单
    if (window.gameApp) {
      if (this.gameConfig && this.gameConfig.isRanked) {
        // 排位赛返回排位赛菜单
        console.log('返回排位赛菜单')
        window.gameApp.showRankedMenu()
      } else {
        // 普通联机返回联机菜单
        console.log('返回联机菜单')
        window.gameApp.showMultiplayerMenu()
      }
    }
  }

  // 清理资源（重写父类方法）
  cleanup() {
    console.log('清理联机游戏资源')

    // 停止游戏循环
    this.destroyed = true
    this.isRunning = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
      console.log('停止游戏循环')
    }

    // 清理自定义对话框事件监听器
    if (this.customDialogHandler) {
      if (typeof wx !== 'undefined') {
        wx.offTouchStart(this.customDialogHandler)
      } else if (this.canvas) {
        this.canvas.removeEventListener('click', this.customDialogHandler)
      }
      this.customDialogHandler = null
      console.log('清理自定义对话框事件监听器')
    }

    // 清理返回按钮事件监听器
    if (this.returnButtonHandler) {
      if (typeof wx !== 'undefined') {
        wx.offTouchStart(this.returnButtonHandler)
      } else if (this.canvas) {
        this.canvas.removeEventListener('click', this.returnButtonHandler)
      }
      this.returnButtonHandler = null
      console.log('清理返回按钮事件监听器')
    }

    // 清理游戏事件监听器
    if (typeof wx !== 'undefined') {
      // 在微信小游戏中，将事件处理器设置为null，并标记游戏为已销毁
      this.gameClickHandler = null
      this.gameTouchHandler = null

      // 设置一个简单的空函数作为全局事件处理器
      wx.onTouchStart(() => {})
      console.log('清理微信小游戏触摸事件监听器')
    } else {
      if (this.gameClickHandler) {
        this.canvas.removeEventListener('click', this.gameClickHandler)
        console.log('移除游戏点击事件监听器')
      }
      if (this.gameTouchHandler) {
        this.canvas.removeEventListener('touchstart', this.gameTouchHandler)
        console.log('移除游戏触摸事件监听器')
      }
    }

    // 清理联机事件监听器和离开房间
    if (this.multiplayerManager) {
      // 如果在房间中，先离开房间
      if (this.multiplayerManager.roomId) {
        console.log('离开当前房间:', this.multiplayerManager.roomId)
        this.multiplayerManager.leaveRoom()
      }

      // 移除所有事件监听器，防止重复绑定
      console.log('清理MultiplayerManager事件监听器')
      const eventTypes = [
        'gameStateUpdate', 'roundScoring', 'playerAction', 'gameEnded',
        'gameStarted', 'playerTimeout', 'remainingTime', 'playerDisconnected',
        'playerReconnected', 'roomClosed', 'disconnected', 'connected',
        'playerLeft', 'error'
      ]

      eventTypes.forEach(eventType => {
        // 清理该事件类型的所有监听器
        if (this.multiplayerManager.callbacks && this.multiplayerManager.callbacks[eventType]) {
          this.multiplayerManager.callbacks[eventType] = []
        }
      })

      console.log('MultiplayerManager事件监听器清理完成')
    }

    // 清理倒计时
    if (this.clientTimerInterval) {
      clearInterval(this.clientTimerInterval)
      this.clientTimerInterval = null
    }

    // 清理事件设置定时器
    if (this.eventSetupTimeout) {
      clearTimeout(this.eventSetupTimeout)
      this.eventSetupTimeout = null
      console.log('清理事件设置定时器')
    }

    // 重置游戏状态
    this.gameState = null
    this.currentScreen = null
    this.eventListenersSetup = false  // 重置事件监听器设置标志

    console.log('联机游戏资源清理完成')
  }

  // 只清理网络连接，保留游戏界面
  cleanupNetworkOnly() {
    console.log('只清理网络连接，保留游戏界面')

    // 清理联机事件监听器和离开房间
    if (this.multiplayerManager) {
      // 如果在房间中，先离开房间
      if (this.multiplayerManager.roomId) {
        console.log('离开当前房间:', this.multiplayerManager.roomId)
        this.multiplayerManager.leaveRoom()
      }

      // 移除所有事件监听器，防止重复绑定
      console.log('清理MultiplayerManager事件监听器')
      const eventTypes = [
        'gameStateUpdate', 'roundScoring', 'playerAction', 'gameEnded',
        'gameStarted', 'playerTimeout', 'remainingTime', 'playerDisconnected',
        'playerReconnected', 'roomClosed', 'disconnected', 'connected',
        'playerLeft', 'error'
      ]

      eventTypes.forEach(eventType => {
        // 清理该事件类型的所有监听器
        if (this.multiplayerManager.callbacks && this.multiplayerManager.callbacks[eventType]) {
          this.multiplayerManager.callbacks[eventType] = []
        }
      })

      console.log('MultiplayerManager事件监听器清理完成')
    }

    console.log('网络连接清理完成，游戏界面保留')
  }

  // 获取颜色的中文名称
  getColorName(color) {
    const colorNames = {
      'blue': '蓝色',
      'yellow': '黄色',
      'red': '红色',
      'black': '黑色',
      'teal': '青色',
      'white': '白色'
    }
    return colorNames[color] || color
  }
}

// 启动游戏
wx.onShow(() => {
  console.log('小游戏显示')
})

wx.onHide(() => {
  console.log('小游戏隐藏')
})

// 启动游戏应用的函数
function startGameApp() {
  console.log('🎮 启动花砖物语游戏应用...')

  // 检查微信小游戏环境
  if (typeof wx !== 'undefined') {
    console.log('检测到微信小游戏环境')

    // 在微信小游戏中，等待Canvas准备就绪
    let retryCount = 0
    const maxRetries = 10

    function tryCreateGameApp() {
      retryCount++
      console.log(`尝试创建GameApp (${retryCount}/${maxRetries})`)

      // 检查Canvas是否可用
      let canvasAvailable = false

      if (typeof canvas !== 'undefined' && canvas) {
        console.log('全局canvas变量可用')
        canvasAvailable = true
      } else if (wx.createCanvas) {
        console.log('wx.createCanvas可用')
        canvasAvailable = true
      }

      if (canvasAvailable) {
        console.log('Canvas可用，创建GameApp')
        new GameApp()
      } else if (retryCount < maxRetries) {
        console.log('Canvas暂不可用，500ms后重试...')
        setTimeout(tryCreateGameApp, 500)
      } else {
        console.error('Canvas始终不可用，游戏启动失败')
        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: '游戏启动失败：Canvas不可用',
            icon: 'none',
            duration: 3000
          })
        }
      }
    }

    // 延迟启动，确保微信小游戏环境完全初始化
    setTimeout(tryCreateGameApp, 100)

  } else {
    console.log('非微信小游戏环境，直接启动')
    new GameApp()
  }
}

// 紧急重置功能（全局函数，用于调试）
window.emergencyReset = function() {
  console.log('🚨 全局紧急重置被调用')
  if (window.gameApp && window.gameApp.gameScreen) {
    window.gameApp.gameScreen.emergencyReset()
  } else {
    console.log('游戏实例不存在，重新启动应用')
    if (window.gameApp) {
      window.gameApp.showMainMenu()
    }
  }
}

// 启动游戏
startGameApp()
