<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的单面板布局测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 20px;
        }
        
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .fix-info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .fix-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
        
        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 修复后的单面板布局测试</h1>
        
        <div class="fix-info">
            <h3>修复内容</h3>
            <ul>
                <li>删除了孤立的代码片段，修复了语法错误</li>
                <li>保留了新的单面板布局功能</li>
                <li>左侧头像列表可以点击切换玩家</li>
                <li>右侧显示选中玩家的详细信息</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testTwoPlayers()">测试2人游戏</button>
            <button onclick="testThreePlayers()">测试3人游戏</button>
            <button onclick="testFourPlayers()">测试4人游戏</button>
            <button onclick="testAvatarClick()">测试头像点击</button>
            <button onclick="testGameplay()">测试游戏操作</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`, 'success');
                }
            };
        }
        
        let gameApp;
        let azulGame;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('测试状态:', message);
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功', 'success');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`, 'error');
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function createTestGame(playerCount) {
            const app = initializeGameApp();
            if (app) {
                try {
                    const config = {
                        playerCount: playerCount,
                        aiCount: playerCount - 1,
                        aiDifficulty: 'medium'
                    };
                    
                    azulGame = new AzulGame(config, app.sharedCanvas);
                    app.gameScreen = azulGame;
                    app.currentScreen = 'game';
                    
                    // 模拟一些游戏状态
                    const gameState = azulGame.gameState;
                    gameState.players.forEach((player, index) => {
                        player.score = Math.floor(Math.random() * 50) + index * 10;
                        // 添加一些磁砖到准备区
                        if (index === 0) {
                            player.patternLines[0] = ['blue'];
                            player.patternLines[1] = ['red', 'red'];
                        }
                        if (index === 1) {
                            player.patternLines[2] = ['yellow', 'yellow', 'yellow'];
                        }
                    });
                    
                    azulGame.render();
                    updateStatus(`${playerCount}人游戏创建成功，当前查看玩家${azulGame.currentViewingPlayer + 1}`, 'success');
                    
                } catch (error) {
                    updateStatus(`创建${playerCount}人游戏失败: ${error.message}`, 'error');
                    console.error('游戏创建错误:', error);
                }
            }
        }
        
        function testTwoPlayers() {
            createTestGame(2);
        }
        
        function testThreePlayers() {
            createTestGame(3);
        }
        
        function testFourPlayers() {
            createTestGame(4);
        }
        
        function testAvatarClick() {
            if (azulGame && azulGame.playerAvatarAreas && azulGame.playerAvatarAreas.length > 1) {
                try {
                    const currentViewing = azulGame.currentViewingPlayer;
                    const nextPlayer = (currentViewing + 1) % azulGame.playerAvatarAreas.length;
                    const targetAvatar = azulGame.playerAvatarAreas[nextPlayer];
                    
                    const centerX = targetAvatar.x + targetAvatar.width / 2;
                    const centerY = targetAvatar.y + targetAvatar.height / 2;
                    
                    updateStatus(`点击玩家${nextPlayer + 1}的头像`, 'info');
                    azulGame.handleClick(centerX, centerY);
                    
                    updateStatus(`切换成功！当前查看玩家${azulGame.currentViewingPlayer + 1}`, 'success');
                    
                } catch (error) {
                    updateStatus(`头像点击测试失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('请先创建多人游戏', 'error');
            }
        }
        
        function testGameplay() {
            if (azulGame) {
                try {
                    const gameState = azulGame.gameState;
                    updateStatus(`游戏状态: 阶段=${gameState.phase}, 当前玩家=${gameState.currentPlayer + 1}, 查看玩家=${azulGame.currentViewingPlayer + 1}`, 'info');
                    
                    // 测试一些游戏功能
                    if (azulGame.playerAvatarAreas) {
                        updateStatus(`头像区域数量: ${azulGame.playerAvatarAreas.length}`, 'info');
                        azulGame.playerAvatarAreas.forEach((area, index) => {
                            updateStatus(`玩家${area.playerIndex + 1}头像位置: (${area.x}, ${area.y})`, 'info');
                        });
                    }
                    
                } catch (error) {
                    updateStatus(`游戏操作测试失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('请先创建游戏', 'error');
            }
        }
        
        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateStatus('页面加载完成，语法错误已修复，准备测试单面板布局...');
                // 自动创建一个3人游戏进行测试
                setTimeout(() => {
                    testThreePlayers();
                }, 1000);
            }, 500);
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>
