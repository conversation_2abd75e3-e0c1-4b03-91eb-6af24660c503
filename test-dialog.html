<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排位赛结束对话框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.5);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .dialog-preview {
            background: white;
            color: black;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .dialog-title {
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            margin-bottom: 15px;
            color: #333;
        }
        .dialog-content {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
            white-space: pre-line;
        }
        .dialog-buttons {
            text-align: center;
            margin-top: 20px;
        }
        .dialog-buttons button {
            background: #4CAF50;
            color: white;
            border: none;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 排位赛结束对话框测试</h1>
        
        <div class="test-section">
            <h2>📝 优化说明</h2>
            <p>✅ 简化了排位赛结果文字内容</p>
            <p>✅ 移除了所有玩家积分变化详情</p>
            <p>✅ 对话框高度自适应内容</p>
            <p>✅ 文字过长时自动截断</p>
            <p>✅ 优化了布局和间距</p>
        </div>

        <div class="test-section">
            <h2>🎮 测试不同情况</h2>
            <button onclick="showTestDialog('win')">获胜情况</button>
            <button onclick="showTestDialog('lose')">失败情况</button>
            <button onclick="showTestDialog('tie')">平局情况</button>
            <button onclick="showTestDialog('promotion')">段位提升</button>
        </div>

        <div class="test-section">
            <h2>📱 对话框预览</h2>
            <div id="dialogPreview" class="dialog-preview">
                <div class="dialog-title">点击上方按钮测试不同情况</div>
                <div class="dialog-content">这里将显示对话框内容预览</div>
                <div class="dialog-buttons">
                    <button>继续查看</button>
                    <button>返回排位赛</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 文字长度对比</h2>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h4>优化前（太长）：</h4>
                <div style="font-size: 12px; color: #ccc;">
                    获胜者: 玩家1<br>
                    最终得分: 85<br><br>
                    你的排位赛结果:<br>
                    排名: 第1名<br>
                    积分变化: +25<br>
                    当前积分: 1225<br>
                    当前段位: 青铜 3<br><br>
                    --- 积分变化 ---<br>
                    [你] 第1名 玩家1<br>
                    积分: +25 → 1225<br><br>
                    第2名 玩家2<br>
                    积分: -15 → 1180<br><br>
                    第3名 玩家3<br>
                    积分: -10 → 1190<br>
                </div>
            </div>
            <div style="background: rgba(76, 175, 80, 0.2); padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h4>优化后（简洁）：</h4>
                <div style="font-size: 12px; color: #ccc;">
                    🎉 恭喜获胜！<br>
                    获胜者: 玩家1<br>
                    最终得分: 85<br><br>
                    🏆 排位赛结果<br>
                    排名: 第1名<br>
                    积分: +25 (1225)<br>
                    段位: 青铜 3
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTestDialog(type) {
            const preview = document.getElementById('dialogPreview');
            let title, content;

            switch(type) {
                case 'win':
                    title = '🎉 恭喜获胜！';
                    content = `获胜者: 玩家1
最终得分: 85

🏆 排位赛结果
排名: 第1名
积分: +25 (1225)
段位: 青铜 3`;
                    break;

                case 'lose':
                    title = '游戏结束';
                    content = `获胜者: 玩家2
最终得分: 92

🏆 排位赛结果
排名: 第3名
积分: -10 (1190)
段位: 青铜 4`;
                    break;

                case 'tie':
                    title = '🎉 平局获胜！';
                    content = `平局获胜者: 玩家1, 玩家2
最终得分: 85

🏆 排位赛结果
排名: 第1名
积分: +20 (1220)
段位: 青铜 3`;
                    break;

                case 'promotion':
                    title = '🎉 恭喜获胜！';
                    content = `获胜者: 玩家1
最终得分: 95

🏆 排位赛结果
排名: 第1名
积分: +30 (1250)
段位: 青铜 2
🎉 段位提升！`;
                    break;
            }

            preview.innerHTML = `
                <div class="dialog-title">${title}</div>
                <div class="dialog-content">${content}</div>
                <div class="dialog-buttons">
                    <button>继续查看</button>
                    <button>返回排位赛</button>
                </div>
            `;
        }

        // 默认显示获胜情况
        showTestDialog('win');
    </script>
</body>
</html>
