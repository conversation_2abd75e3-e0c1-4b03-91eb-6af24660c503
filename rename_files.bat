@echo off
echo 正在重命名图片文件...

cd images

if exist "小白羊.gif" (
    ren "小白羊.gif" "white_sheep.gif"
    echo 重命名: 小白羊.gif -> white_sheep.gif
)

if exist "小黄羊.gif" (
    ren "小黄羊.gif" "yellow_sheep.gif"
    echo 重命名: 小黄羊.gif -> yellow_sheep.gif
)

if exist "小粉羊.gif" (
    ren "小粉羊.gif" "pink_sheep.gif"
    echo 重命名: 小粉羊.gif -> pink_sheep.gif
)

if exist "小黑羊.gif" (
    ren "小黑羊.gif" "black_sheep.gif"
    echo 重命名: 小黑羊.gif -> black_sheep.gif
)

if exist "小灰羊.gif" (
    ren "小灰羊.gif" "gray_sheep.gif"
    echo 重命名: 小灰羊.gif -> gray_sheep.gif
)

if exist "背景.jpg" (
    ren "背景.jpg" "background.jpg"
    echo 重命名: 背景.jpg -> background.jpg
)

if exist "棋盘.png" (
    ren "棋盘.png" "board.png"
    echo 重命名: 棋盘.png -> board.png
)

cd ..

echo 正在重命名音效文件...

cd sounds

if exist "游戏BGM1.m4a" (
    ren "游戏BGM1.m4a" "game_bgm1.m4a"
    echo 重命名: 游戏BGM1.m4a -> game_bgm1.m4a
)

if exist "游戏BGM2.mp3" (
    ren "游戏BGM2.mp3" "game_bgm2.mp3"
    echo 重命名: 游戏BGM2.mp3 -> game_bgm2.mp3
)

if exist "被选定音效.wav" (
    ren "被选定音效.wav" "tile_select.wav"
    echo 重命名: 被选定音效.wav -> tile_select.wav
)

if exist "咩咩音效.wav" (
    ren "咩咩音效.wav" "tile_place.wav"
    echo 重命名: 咩咩音效.wav -> tile_place.wav
)

if exist "成熟咩咩.wav" (
    ren "成熟咩咩.wav" "score_update.wav"
    echo 重命名: 成熟咩咩.wav -> score_update.wav
)

if exist "绵羊咩.wav" (
    ren "绵羊咩.wav" "round_end.wav"
    echo 重命名: 绵羊咩.wav -> round_end.wav
)

if exist "公羊咩咩.wav" (
    ren "公羊咩咩.wav" "game_win.wav"
    echo 重命名: 公羊咩咩.wav -> game_win.wav
)

if exist "魔性.m4a" (
    ren "魔性.m4a" "button_click.m4a"
    echo 重命名: 魔性.m4a -> button_click.m4a
)

cd ..

echo 文件重命名完成！
echo 现在可以重新运行游戏了。
pause
