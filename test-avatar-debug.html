<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像显示调试测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        canvas {
            border: 2px solid #333;
            background: white;
            display: block;
            margin: 20px auto;
        }
        .buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .debug-info {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>头像显示调试测试</h1>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="createRankedGame()">创建排位赛游戏</button>
            <button onclick="testAvatarClicks()">测试头像点击</button>
            <button onclick="showDebugInfo()">显示调试信息</button>
        </div>
        
        <div class="debug-info" id="debugInfo">
            等待创建游戏...
        </div>
    </div>

    <script src="game.js"></script>
    <script>
        let azulGame;
        
        function updateDebugInfo(info) {
            document.getElementById('debugInfo').textContent = info;
        }
        
        function createRankedGame() {
            try {
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 创建排位赛游戏配置
                const rankedGameData = {
                    players: [
                        { id: 'player1', nickName: '玩家1', rating: 1200 },
                        { id: 'player2', nickName: '玩家2', rating: 1250 },
                        { id: 'player3', nickName: '玩家3', rating: 1300 },
                        { id: 'player4', nickName: '玩家4', rating: 1350 }
                    ],
                    matchId: 'test-match-001',
                    gameState: {}
                };
                
                azulGame = new AzulGame(canvas, ctx);
                const gameConfig = azulGame.createRankedGameConfig(rankedGameData);
                azulGame.initGame(gameConfig);
                
                // 等待渲染完成后检查头像
                setTimeout(() => {
                    const avatarCount = azulGame.playerAvatarAreas ? azulGame.playerAvatarAreas.length : 0;
                    const playerCount = azulGame.gameState.players.length;
                    
                    let debugInfo = `游戏创建完成\n`;
                    debugInfo += `玩家数量: ${playerCount}\n`;
                    debugInfo += `头像区域数量: ${avatarCount}\n`;
                    debugInfo += `当前查看玩家: ${azulGame.currentViewingPlayer}\n\n`;
                    
                    if (azulGame.playerAvatarAreas) {
                        debugInfo += `头像区域详情:\n`;
                        azulGame.playerAvatarAreas.forEach((area, index) => {
                            debugInfo += `头像${index + 1}: x=${area.x}, y=${area.y}, w=${area.width}, h=${area.height}\n`;
                        });
                    }
                    
                    if (avatarCount === playerCount) {
                        debugInfo += `\n✅ 所有头像都正常显示！`;
                    } else {
                        debugInfo += `\n❌ 头像显示异常！应该显示${playerCount}个，实际显示${avatarCount}个`;
                    }
                    
                    updateDebugInfo(debugInfo);
                }, 100);
                
            } catch (error) {
                updateDebugInfo(`创建游戏失败: ${error.message}\n${error.stack}`);
            }
        }
        
        function testAvatarClicks() {
            if (!azulGame || !azulGame.playerAvatarAreas) {
                updateDebugInfo('请先创建游戏');
                return;
            }
            
            let debugInfo = `开始测试头像点击...\n\n`;
            
            azulGame.playerAvatarAreas.forEach((area, index) => {
                const centerX = area.x + area.width / 2;
                const centerY = area.y + area.height / 2;
                
                debugInfo += `测试头像${index + 1}: 点击坐标(${centerX}, ${centerY})\n`;
                
                // 模拟点击
                const oldViewing = azulGame.currentViewingPlayer;
                azulGame.handleClick(centerX, centerY);
                const newViewing = azulGame.currentViewingPlayer;
                
                if (newViewing === index) {
                    debugInfo += `✅ 点击成功，切换到玩家${index + 1}\n`;
                } else {
                    debugInfo += `❌ 点击失败，期望${index + 1}，实际${newViewing + 1}\n`;
                }
                debugInfo += `\n`;
            });
            
            updateDebugInfo(debugInfo);
        }
        
        function showDebugInfo() {
            if (!azulGame) {
                updateDebugInfo('请先创建游戏');
                return;
            }
            
            const { width, height } = azulGame.getLogicalSize();
            const playerCount = azulGame.gameState.players.length;
            
            let debugInfo = `游戏状态调试信息:\n\n`;
            debugInfo += `画布尺寸: ${width} x ${height}\n`;
            debugInfo += `玩家数量: ${playerCount}\n`;
            debugInfo += `当前查看玩家: ${azulGame.currentViewingPlayer + 1}\n`;
            debugInfo += `游戏模式: ${azulGame.gameState.gameMode || 'unknown'}\n\n`;
            
            // 计算面板高度
            const avatarSize = 50;
            const avatarSpacing = 10;
            const topBottomPadding = 40;
            const minPanelHeight = 200;
            const requiredHeightForAvatars = topBottomPadding + playerCount * (avatarSize + avatarSpacing) - avatarSpacing;
            const panelHeight = Math.max(minPanelHeight, requiredHeightForAvatars);
            
            debugInfo += `面板高度计算:\n`;
            debugInfo += `- 最小面板高度: ${minPanelHeight}px\n`;
            debugInfo += `- 头像所需高度: ${requiredHeightForAvatars}px\n`;
            debugInfo += `- 实际面板高度: ${panelHeight}px\n\n`;
            
            if (azulGame.playerAvatarAreas) {
                debugInfo += `头像区域 (${azulGame.playerAvatarAreas.length}个):\n`;
                azulGame.playerAvatarAreas.forEach((area, index) => {
                    debugInfo += `头像${index + 1}: (${area.x}, ${area.y}) ${area.width}x${area.height}\n`;
                });
            } else {
                debugInfo += `头像区域: 未初始化\n`;
            }
            
            updateDebugInfo(debugInfo);
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            updateDebugInfo('页面加载完成，点击"创建排位赛游戏"开始测试');
        });
    </script>
</body>
</html>
