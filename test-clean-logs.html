<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志清理验证测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .fix-info h3 {
            margin-top: 0;
            color: #FFD700;
        }
        .fix-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .fix-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
        }
        .fix-item h4 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
            font-size: 16px;
        }
        canvas {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: #f8f9fa;
            display: block;
            margin: 20px auto;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .buttons {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 8px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .console-monitor {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-count {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #FF9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 日志清理验证测试</h1>
        
        <div class="fix-info">
            <h3>🔧 日志优化修复</h3>
            <div class="fix-list">
                <div class="fix-item">
                    <h4>🐛 问题</h4>
                    <ul>
                        <li>头像加载调试日志过多</li>
                        <li>"xxx没有头像显示默认头像"刷屏</li>
                        <li>重复的状态检查日志</li>
                    </ul>
                </div>
                <div class="fix-item">
                    <h4>✅ 修复</h4>
                    <ul>
                        <li>移除重复的调试日志</li>
                        <li>只在关键事件时输出日志</li>
                        <li>简化头像状态检查逻辑</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="createGame()">🚀 创建游戏</button>
            <button onclick="monitorLogs()">📊 监控日志</button>
            <button onclick="testAvatarClicks()">👤 测试头像点击</button>
            <button onclick="clearConsole()">🧹 清理控制台</button>
        </div>
        
        <div class="log-count" id="logCount">
            控制台日志数量: 0
        </div>
        
        <div class="console-monitor" id="consoleMonitor">
            点击"监控日志"开始监控控制台输出...
        </div>
    </div>

    <script src="game.js"></script>
    <script>
        let azulGame;
        let originalConsoleLog = console.log;
        let originalConsoleWarn = console.warn;
        let originalConsoleError = console.error;
        let logCount = 0;
        let isMonitoring = false;
        
        function updateLogCount() {
            document.getElementById('logCount').textContent = `控制台日志数量: ${logCount}`;
        }
        
        function addToMonitor(message, type = 'info') {
            const monitor = document.getElementById('consoleMonitor');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            monitor.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            monitor.scrollTop = monitor.scrollHeight;
            
            // 限制显示的日志数量
            const logs = monitor.children;
            if (logs.length > 50) {
                monitor.removeChild(logs[0]);
            }
        }
        
        function createGame() {
            logCount = 0;
            updateLogCount();
            
            if (isMonitoring) {
                addToMonitor('🎮 创建排位赛游戏...', 'info');
            }
            
            try {
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 创建带头像的排位赛游戏配置
                const rankedGameData = {
                    players: [
                        { 
                            id: 'player1', 
                            nickName: '张三丰', 
                            avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxCqRzuYWQmpwiaqQEjNxbC7HaJaZTGVadSRHFKHMNYFGHaVm5WVoicWCqFqXYZxHMqS9XiaQOyJJaw/132',
                            rating: 1200, 
                            tier: { name: '青铜 5', icon: '🥉' } 
                        },
                        { 
                            id: 'player2', 
                            nickName: '李小龙', 
                            avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83eoj0hHXhgJNOTSOFsS4uZs8x1ConecaVOB8eIl115xmJZcT4oCicvia7wMEufibKtTLqmXSoXBZjcLRw/132',
                            rating: 1250, 
                            tier: { name: '青铜 4', icon: '🥉' } 
                        },
                        { 
                            id: 'player3', 
                            nickName: '王大锤', 
                            avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uJ3Hk4UESX2oqkekiSFPref5KVokCicxNKiaTHuibKVicWEUskLWCBhshzwjfDOYmBLvxuze7Xtxqg/132',
                            rating: 1300, 
                            tier: { name: '青铜 3', icon: '🥉' } 
                        },
                        { 
                            id: 'player4', 
                            nickName: '赵子龙', 
                            avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/ajNVdqHZLLBWHlwKFvQEdXOqRdJjNSwpCMuzj0c3arWnYialia8FmnWBDO0deMdk6O6B2QqWBQ6AiJU7U1Qlj6fQ/132',
                            rating: 1350, 
                            tier: { name: '青铜 2', icon: '🥉' } 
                        }
                    ],
                    matchId: 'test-match-' + Date.now(),
                    gameState: {}
                };
                
                azulGame = new AzulGame(canvas, ctx);
                const gameConfig = azulGame.createRankedGameConfig(rankedGameData);
                azulGame.initGame(gameConfig);
                
                if (isMonitoring) {
                    addToMonitor('✅ 游戏创建成功', 'success');
                }
                
            } catch (error) {
                if (isMonitoring) {
                    addToMonitor(`❌ 创建游戏失败: ${error.message}`, 'error');
                }
                console.error(error);
            }
        }
        
        function monitorLogs() {
            if (isMonitoring) {
                // 停止监控
                console.log = originalConsoleLog;
                console.warn = originalConsoleWarn;
                console.error = originalConsoleError;
                isMonitoring = false;
                document.querySelector('button[onclick="monitorLogs()"]').textContent = '📊 监控日志';
                addToMonitor('🛑 停止日志监控', 'warning');
            } else {
                // 开始监控
                isMonitoring = true;
                logCount = 0;
                document.getElementById('consoleMonitor').innerHTML = '';
                document.querySelector('button[onclick="monitorLogs()"]').textContent = '🛑 停止监控';
                
                // 拦截控制台输出
                console.log = function(...args) {
                    logCount++;
                    updateLogCount();
                    const message = args.join(' ');
                    addToMonitor(message, 'info');
                    originalConsoleLog.apply(console, args);
                };
                
                console.warn = function(...args) {
                    logCount++;
                    updateLogCount();
                    const message = args.join(' ');
                    addToMonitor(message, 'warning');
                    originalConsoleWarn.apply(console, args);
                };
                
                console.error = function(...args) {
                    logCount++;
                    updateLogCount();
                    const message = args.join(' ');
                    addToMonitor(message, 'error');
                    originalConsoleError.apply(console, args);
                };
                
                addToMonitor('🔍 开始监控控制台日志', 'info');
            }
        }
        
        function testAvatarClicks() {
            if (!azulGame || !azulGame.playerAvatarAreas) {
                if (isMonitoring) {
                    addToMonitor('❌ 请先创建游戏', 'error');
                }
                return;
            }
            
            if (isMonitoring) {
                addToMonitor('👤 开始测试头像点击...', 'info');
            }
            
            let clickIndex = 0;
            const clickInterval = setInterval(() => {
                if (clickIndex >= azulGame.playerAvatarAreas.length) {
                    clearInterval(clickInterval);
                    if (isMonitoring) {
                        addToMonitor('✅ 头像点击测试完成', 'success');
                        addToMonitor(`📊 总共产生了 ${logCount} 条日志`, 'info');
                    }
                    return;
                }
                
                const targetAvatar = azulGame.playerAvatarAreas[clickIndex];
                const centerX = targetAvatar.x + targetAvatar.width / 2;
                const centerY = targetAvatar.y + targetAvatar.height / 2;
                
                azulGame.handleClick(centerX, centerY);
                clickIndex++;
            }, 1000);
        }
        
        function clearConsole() {
            console.clear();
            logCount = 0;
            updateLogCount();
            document.getElementById('consoleMonitor').innerHTML = '';
            if (isMonitoring) {
                addToMonitor('🧹 控制台已清理', 'info');
            }
        }
        
        // 页面加载完成提示
        window.addEventListener('load', () => {
            console.log('🚀 页面加载完成，准备开始日志清理验证');
            console.log('💡 点击"监控日志"开始监控，然后创建游戏观察日志数量');
        });
    </script>
</body>
</html>
