# PowerShell脚本：重命名中文文件名为英文
Write-Host "正在重命名图片文件..." -ForegroundColor Green

# 重命名图片文件
$imageRenames = @{
    "images/小白羊.gif" = "images/white_sheep.gif"
    "images/小黄羊.gif" = "images/yellow_sheep.gif"
    "images/小粉羊.gif" = "images/pink_sheep.gif"
    "images/小黑羊.gif" = "images/black_sheep.gif"
    "images/小灰羊.gif" = "images/gray_sheep.gif"
    "images/背景.jpg" = "images/background.jpg"
    "images/棋盘.png" = "images/board.png"
}

foreach ($rename in $imageRenames.GetEnumerator()) {
    if (Test-Path $rename.Key) {
        Rename-Item -Path $rename.Key -NewName (Split-Path $rename.Value -Leaf)
        Write-Host "重命名: $($rename.Key) -> $($rename.Value)" -ForegroundColor Yellow
    } else {
        Write-Host "文件不存在: $($rename.Key)" -ForegroundColor Red
    }
}

Write-Host "正在重命名音效文件..." -ForegroundColor Green

# 重命名音效文件
$soundRenames = @{
    "sounds/游戏BGM1.m4a" = "sounds/game_bgm1.m4a"
    "sounds/游戏BGM2.mp3" = "sounds/game_bgm2.mp3"
    "sounds/被选定音效.wav" = "sounds/tile_select.wav"
    "sounds/咩咩音效.wav" = "sounds/tile_place.wav"
    "sounds/成熟咩咩.wav" = "sounds/score_update.wav"
    "sounds/绵羊咩.wav" = "sounds/round_end.wav"
    "sounds/公羊咩咩.wav" = "sounds/game_win.wav"
    "sounds/魔性.m4a" = "sounds/button_click.m4a"
}

foreach ($rename in $soundRenames.GetEnumerator()) {
    if (Test-Path $rename.Key) {
        Rename-Item -Path $rename.Key -NewName (Split-Path $rename.Value -Leaf)
        Write-Host "重命名: $($rename.Key) -> $($rename.Value)" -ForegroundColor Yellow
    } else {
        Write-Host "文件不存在: $($rename.Key)" -ForegroundColor Red
    }
}

Write-Host "文件重命名完成！" -ForegroundColor Green
Write-Host "现在可以重新运行游戏了。" -ForegroundColor Cyan

# 显示重命名后的文件列表
Write-Host "`n图片文件:" -ForegroundColor Magenta
Get-ChildItem -Path "images" -Filter "*.gif", "*.jpg", "*.png" | Format-Table Name

Write-Host "音效文件:" -ForegroundColor Magenta
Get-ChildItem -Path "sounds" -Filter "*.wav", "*.mp3", "*.m4a" | Format-Table Name

Read-Host "按回车键继续..."
