<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 明显动画效果测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(255, 0, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .success {
            background: rgba(0, 255, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>明显动画效果测试</h3>
        
        <div class="info">
            <strong>现实情况：</strong><br>
            Canvas无法播放GIF动画，这是技术限制。但我们可以创建非常明显的自定义动画效果。
        </div>
        
        <div class="success">
            <strong>新动画效果：</strong><br>
            • 大幅度亮度变化 (0.4-1.0)<br>
            • 明显的缩放动画 (0.84-1.0)<br>
            • 边框闪烁效果<br>
            • 小羊跳动动画<br>
            • 1.5秒快速周期
        </div>
        
        <button onclick="startObviousAnimation()">🎬 开始明显动画</button>
        <button onclick="stopAnimation()">⏹️ 停止动画</button>
        <button onclick="testStaticTiles()">🖼️ 测试静态瓷砖</button>
        <button onclick="showAnimationInfo()">ℹ️ 显示动画信息</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        let animationRunning = false;
        let animationId = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function startObviousAnimation() {
            updateStatus('=== 开始明显动画测试 ===');
            updateStatus('如果你看不到动画，说明有其他技术问题');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            animationRunning = true;
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
            let frameCount = 0;
            
            const animate = () => {
                if (!animationRunning) return;
                
                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.fillStyle = 'white';
                ctx.font = '28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('明显动画效果测试', canvas.width / 2, 50);
                
                // 绘制说明
                ctx.font = '16px Arial';
                ctx.fillText('观察：亮度变化、缩放效果、边框闪烁、小羊跳动', canvas.width / 2, 80);
                
                // 绘制动画瓷砖
                colors.forEach((color, index) => {
                    const x = 80 + index * 130;
                    const y = 200;
                    const size = 100; // 更大的瓷砖，更容易观察
                    
                    // 使用游戏的renderTile方法
                    gameApp.renderTile(color, x, y, size);
                    
                    // 添加颜色标签
                    ctx.fillStyle = 'white';
                    ctx.font = '18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(color, x + size/2, y - 15);
                });
                
                // 显示帧计数和时间
                const time = Date.now() / 1000;
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`帧数: ${frameCount}`, 20, canvas.height - 40);
                ctx.fillText(`时间: ${time.toFixed(1)}s`, 20, canvas.height - 20);
                
                // 显示动画相位
                const animPhase = (time / 1.5) % (Math.PI * 2);
                ctx.fillText(`动画相位: ${(animPhase / Math.PI * 180).toFixed(0)}°`, 200, canvas.height - 40);
                
                frameCount++;
                animationId = requestAnimationFrame(animate);
            };
            
            updateStatus('✅ 明显动画已启动');
            updateStatus('应该看到：大幅度亮度变化、缩放、闪烁、跳动');
            animate();
        }
        
        function stopAnimation() {
            updateStatus('=== 停止动画 ===');
            animationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            updateStatus('✅ 动画已停止');
        }
        
        function testStaticTiles() {
            updateStatus('=== 测试静态瓷砖 ===');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空Canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('静态瓷砖测试（当前时刻）', canvas.width / 2, 50);
            
            const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
            
            colors.forEach((color, index) => {
                const x = 80 + index * 130;
                const y = 200;
                const size = 100;
                
                updateStatus(`渲染${color}瓷砖...`);
                
                try {
                    gameApp.renderTile(color, x, y, size);
                    updateStatus(`✅ ${color}瓷砖渲染成功`);
                } catch (error) {
                    updateStatus(`❌ ${color}瓷砖渲染失败: ${error.message}`);
                }
                
                // 添加颜色标签
                ctx.fillStyle = 'white';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(color, x + size/2, y - 15);
            });
            
            updateStatus('静态瓷砖测试完成');
        }
        
        function showAnimationInfo() {
            updateStatus('=== 动画效果说明 ===');
            updateStatus('新的动画效果包括：');
            updateStatus('1. 亮度变化：0.4-1.0大幅度变化');
            updateStatus('2. 缩放动画：0.84-1.0明显缩放');
            updateStatus('3. 边框闪烁：透明度0.6-1.0闪烁');
            updateStatus('4. 小羊跳动：位置和大小变化');
            updateStatus('5. 动画周期：1.5秒快速循环');
            updateStatus('6. 如果GIF图片存在，会对图片进行缩放动画');
            updateStatus('7. 如果没有图片，会对颜色进行全套动画');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成');
                updateStatus('');
                updateStatus('🎯 关于GIF动画的现实：');
                updateStatus('• Canvas无法播放GIF动画，这是技术限制');
                updateStatus('• 所有浏览器和微信小游戏都有这个限制');
                updateStatus('• GIF在Canvas中只显示第一帧');
                updateStatus('');
                updateStatus('✨ 我们的解决方案：');
                updateStatus('• 创建非常明显的自定义动画效果');
                updateStatus('• 大幅度的视觉变化，绝对能看到');
                updateStatus('• 如果这个都看不到动画，说明有其他问题');
                
                // 显示动画信息
                showAnimationInfo();
                
                // 自动开始动画
                setTimeout(() => {
                    startObviousAnimation();
                }, 2000);
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
