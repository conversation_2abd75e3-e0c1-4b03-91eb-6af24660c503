<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 加载动画风格测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(0, 255, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>加载动画风格测试</h3>
        
        <div class="info">
            <strong>发现：</strong><br>
            加载转圈动画能动是因为：<br>
            • 使用requestAnimationFrame<br>
            • 每帧清空并重绘整个Canvas<br>
            • 基于Date.now()的时间计算<br>
            <br>
            <strong>解决方案：</strong><br>
            使用与加载动画完全相同的技术！
        </div>
        
        <button onclick="startLoadingStyleAnimation()">🎬 开始加载风格动画</button>
        <button onclick="stopAnimation()">⏹️ 停止动画</button>
        <button onclick="showOriginalLoading()">🔄 显示原始加载动画</button>
        <button onclick="compareAnimations()">🔍 对比两种动画</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        let animationRunning = false;
        let animationId = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function startLoadingStyleAnimation() {
            updateStatus('=== 开始加载风格动画测试 ===');
            updateStatus('使用与加载动画完全相同的技术');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            animationRunning = true;
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
            let frameCount = 0;
            
            const animate = () => {
                if (!animationRunning) return;
                
                // 关键：每帧清空整个Canvas（与加载动画相同）
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.fillStyle = 'white';
                ctx.font = '28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('加载动画风格的瓷砖动画', canvas.width / 2, 50);
                
                // 绘制说明
                ctx.font = '16px Arial';
                ctx.fillText('使用与加载转圈相同的技术：requestAnimationFrame + 每帧重绘', canvas.width / 2, 80);
                
                // 绘制动画瓷砖（使用游戏的renderTile方法）
                colors.forEach((color, index) => {
                    const x = 80 + index * 130;
                    const y = 200;
                    const size = 100;
                    
                    // 使用游戏的renderTile方法
                    gameApp.renderTile(color, x, y, size);
                    
                    // 添加颜色标签
                    ctx.fillStyle = 'white';
                    ctx.font = '18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(color, x + size/2, y - 15);
                });
                
                // 在底部显示原始加载动画作为对比
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText('对比：原始加载动画', canvas.width / 2, 400);
                
                // 绘制原始加载动画
                if (gameApp.drawLoadingAnimation) {
                    gameApp.drawLoadingAnimation(ctx, canvas.width / 2, 450);
                }
                
                // 显示帧计数
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`帧数: ${frameCount}`, 20, canvas.height - 20);
                
                frameCount++;
                animationId = requestAnimationFrame(animate);
            };
            
            updateStatus('✅ 加载风格动画已启动');
            updateStatus('观察：旋转、脉冲、光晕效果');
            animate();
        }
        
        function stopAnimation() {
            updateStatus('=== 停止动画 ===');
            animationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            updateStatus('✅ 动画已停止');
        }
        
        function showOriginalLoading() {
            updateStatus('=== 显示原始加载动画 ===');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空Canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 显示加载界面
            if (gameApp.showLoadingScreen) {
                gameApp.showLoadingScreen('这是原始的加载动画');
            }
            
            updateStatus('✅ 原始加载动画已显示');
        }
        
        function compareAnimations() {
            updateStatus('=== 动画技术对比 ===');
            updateStatus('');
            updateStatus('🔄 加载动画（能动）：');
            updateStatus('• 使用 requestAnimationFrame');
            updateStatus('• 每帧调用 ctx.clearRect() 清空Canvas');
            updateStatus('• 每帧重新绘制所有内容');
            updateStatus('• 基于 Date.now() / 50 计算时间');
            updateStatus('• 使用 Math.sin() 和 Math.cos() 计算位置');
            updateStatus('');
            updateStatus('🎮 瓷砖动画（现在应该能动）：');
            updateStatus('• 使用相同的 requestAnimationFrame');
            updateStatus('• 使用相同的时间计算 Date.now() / 50');
            updateStatus('• 使用相同的数学函数');
            updateStatus('• 添加旋转、脉冲、光晕效果');
            updateStatus('• 小羊围绕瓷砖旋转');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成');
                updateStatus('');
                updateStatus('💡 重要发现：');
                updateStatus('加载转圈动画能动的原因：');
                updateStatus('1. 使用 requestAnimationFrame 持续重绘');
                updateStatus('2. 每帧清空整个Canvas');
                updateStatus('3. 基于时间戳的动画计算');
                updateStatus('');
                updateStatus('🎯 解决方案：');
                updateStatus('让瓷砖动画使用完全相同的技术！');
                
                // 显示技术对比
                compareAnimations();
                
                // 自动开始动画
                setTimeout(() => {
                    startLoadingStyleAnimation();
                }, 2000);
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
