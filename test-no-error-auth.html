<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 无错误授权测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .controls button.danger {
            background: #f44336;
        }
        
        .controls button.danger:hover {
            background: #da190b;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(0, 200, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .highlight {
            background: rgba(255, 255, 0, 0.2);
            padding: 8px;
            border-radius: 3px;
            margin: 5px 0;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>无错误授权测试</h3>
        
        <div class="info">
            <strong>改进说明：</strong><br>
            用户拒绝授权不再抛出错误，而是通过事件机制优雅处理。这是正常的用户行为，不应该被当作错误。
        </div>
        
        <div class="highlight">
            <strong>测试要点：</strong><br>
            1. 不应该看到任何错误日志<br>
            2. 用户拒绝授权后应该显示友好对话框<br>
            3. 可以重复尝试授权
        </div>
        
        <button onclick="testRankedReject()" class="danger">测试拒绝授权（无错误）</button>
        <button onclick="testRankedAccept()">测试同意授权</button>
        <button onclick="testReauth()">测试重新授权流程</button>
        <button onclick="showMainMenu()">返回主菜单</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        let testMode = 'normal';
        let authAttempts = 0;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                    console.log('Toast:', options.title);
                },
                showModal: (options) => {
                    updateStatus(`🔔 Modal弹窗: ${options.title}`);
                    updateStatus(`内容: ${options.content}`);
                    console.log('Modal:', options);
                    
                    // 模拟用户选择
                    setTimeout(() => {
                        if (options.success) {
                            // 默认用户点击确认
                            options.success({ confirm: true, cancel: false });
                        }
                    }, 1000);
                },
                getUserProfile: (options) => {
                    authAttempts++;
                    updateStatus(`📱 第${authAttempts}次getUserProfile调用: "${options.desc}"`);
                    
                    let shouldReject = false;
                    if (testMode === 'reject') {
                        shouldReject = true;
                    } else if (testMode === 'reauth') {
                        shouldReject = authAttempts === 1; // 第一次拒绝，第二次同意
                    }
                    
                    setTimeout(() => {
                        if (shouldReject) {
                            updateStatus('❌ 模拟用户拒绝授权（正常行为，不是错误）');
                            if (options.fail) {
                                options.fail({
                                    errMsg: 'getUserProfile:fail auth deny',
                                    errCode: -1
                                });
                            }
                        } else {
                            updateStatus('✅ 模拟用户同意授权');
                            if (options.success) {
                                options.success({
                                    userInfo: {
                                        nickName: '测试玩家',
                                        avatarUrl: 'https://example.com/avatar.jpg'
                                    }
                                });
                            }
                        }
                    }, 500);
                },
                login: (options) => {
                    setTimeout(() => {
                        if (options.success) {
                            options.success({ code: 'mock_code_123' });
                        }
                    }, 100);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                }),
                getStorageSync: (key) => {
                    return localStorage.getItem(key);
                },
                setStorageSync: (key, value) => {
                    localStorage.setItem(key, value);
                }
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function testRankedReject() {
            updateStatus('=== 测试拒绝授权（无错误模式）===');
            updateStatus('预期：显示授权对话框，无错误日志');
            testMode = 'reject';
            authAttempts = 0;
            
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function testRankedAccept() {
            updateStatus('=== 测试同意授权 ===');
            testMode = 'accept';
            authAttempts = 0;
            
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function testReauth() {
            updateStatus('=== 测试重新授权流程 ===');
            updateStatus('第一次拒绝，第二次同意');
            testMode = 'reauth';
            authAttempts = 0;
            
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function showMainMenu() {
            if (gameApp) {
                gameApp.showMainMenu();
                updateStatus('返回主菜单');
                testMode = 'normal';
                authAttempts = 0;
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        // 监听控制台错误
        const originalError = console.error;
        console.error = function(...args) {
            updateStatus(`🚨 控制台错误: ${args.join(' ')}`);
            originalError.apply(console, args);
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('提示：现在用户拒绝授权不会产生错误日志');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 2000);
    </script>
</body>
</html>
