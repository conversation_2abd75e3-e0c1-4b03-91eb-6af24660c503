<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 高亮区域和GIF动画修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(200, 100, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .fix {
            background: rgba(0, 200, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>高亮区域和GIF动画修复测试</h3>
        
        <div class="info">
            <strong>问题1：</strong>高亮区域覆盖范围不对，与实际准备区位置不匹配<br>
            <strong>问题2：</strong>小羊图标都是GIF，但不会动画播放
        </div>
        
        <div class="fix">
            <strong>修复1：</strong>统一高亮区域和实际渲染的位置计算逻辑<br>
            <strong>修复2：</strong>改进GIF图片加载和渲染，确保动画能正常播放
        </div>
        
        <button onclick="testHighlightAccuracy()">测试高亮区域准确性</button>
        <button onclick="testGifAnimation()">测试GIF动画</button>
        <button onclick="checkImageLoading()">检查图片加载状态</button>
        <button onclick="simulateTileSelection()">模拟瓷砖选择</button>
        <button onclick="forceRender()">强制重新渲染</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function testHighlightAccuracy() {
            updateStatus('=== 测试高亮区域准确性 ===');
            
            if (gameApp && gameApp.gameState) {
                // 模拟选择瓷砖状态
                gameApp.gameState.selectedTiles = ['blue', 'blue'];
                gameApp.gameState.selectedColor = 'blue';
                
                updateStatus('设置选中瓷砖状态，触发高亮显示');
                
                try {
                    gameApp.render();
                    updateStatus('✅ 高亮区域渲染完成');
                    updateStatus('检查高亮区域是否与准备区位置匹配');
                    
                    // 检查点击区域
                    if (gameApp.placementClickAreas && gameApp.placementClickAreas.length > 0) {
                        updateStatus(`点击区域数量: ${gameApp.placementClickAreas.length}`);
                        gameApp.placementClickAreas.forEach((area, index) => {
                            updateStatus(`区域${index + 1}: ${area.type} - 位置:(${Math.round(area.x)}, ${Math.round(area.y)}) 大小:${Math.round(area.width)}x${Math.round(area.height)}`);
                        });
                    }
                } catch (error) {
                    updateStatus(`❌ 高亮区域测试失败: ${error.message}`);
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function testGifAnimation() {
            updateStatus('=== 测试GIF动画 ===');
            
            if (gameApp && gameApp.images) {
                updateStatus('检查瓷砖图片加载状态:');
                
                const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
                colors.forEach(color => {
                    const img = gameApp.images[color];
                    if (img) {
                        updateStatus(`${color}: ${img.complete ? '✅已加载' : '⏳加载中'} (${img.src ? img.src.split('/').pop() : '无源'})`);
                        
                        if (img.src && img.src.includes('.gif')) {
                            updateStatus(`  🎬 ${color}为GIF动画文件`);
                        }
                    } else {
                        updateStatus(`${color}: ❌未找到图片对象`);
                    }
                });
                
                updateStatus('强制重新渲染以显示动画效果');
                gameApp.render();
            } else {
                updateStatus('❌ 游戏或图片资源未初始化');
            }
        }
        
        function checkImageLoading() {
            updateStatus('=== 检查图片加载状态 ===');
            
            if (gameApp) {
                updateStatus(`图片对象存在: ${gameApp.images ? '是' : '否'}`);
                
                if (gameApp.images) {
                    const imageCount = Object.keys(gameApp.images).length;
                    updateStatus(`已加载图片数量: ${imageCount}`);
                    
                    Object.entries(gameApp.images).forEach(([color, img]) => {
                        updateStatus(`${color}: 完成=${img.complete}, 宽度=${img.naturalWidth}, 高度=${img.naturalHeight}`);
                    });
                }
                
                // 检查GAME_CONFIG
                if (window.GAME_CONFIG && window.GAME_CONFIG.TILE_IMAGES) {
                    updateStatus('瓷砖图片配置:');
                    Object.entries(window.GAME_CONFIG.TILE_IMAGES).forEach(([color, src]) => {
                        updateStatus(`${color}: ${src}`);
                    });
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function simulateTileSelection() {
            updateStatus('=== 模拟瓷砖选择流程 ===');
            
            if (gameApp && gameApp.gameState && gameApp.gameState.factories) {
                // 找到第一个非空工厂
                for (let i = 0; i < gameApp.gameState.factories.length; i++) {
                    const factory = gameApp.gameState.factories[i];
                    if (factory && factory.length > 0) {
                        updateStatus(`选择工厂${i + 1}: [${factory.join(', ')}]`);
                        
                        // 模拟选择第一种颜色
                        const color = factory[0];
                        const tiles = factory.filter(tile => tile === color);
                        
                        updateStatus(`选择颜色: ${color}, 数量: ${tiles.length}`);
                        
                        // 设置选择状态
                        gameApp.gameState.selectedTiles = tiles;
                        gameApp.gameState.selectedColor = color;
                        gameApp.gameState.showingFactorySelection = false;
                        
                        // 强制渲染显示高亮
                        gameApp.render();
                        
                        updateStatus('✅ 瓷砖选择模拟完成，检查准备区高亮是否正确');
                        break;
                    }
                }
            } else {
                updateStatus('❌ 游戏状态不可用');
            }
        }
        
        function forceRender() {
            updateStatus('=== 强制重新渲染 ===');
            
            if (gameApp) {
                try {
                    gameApp.render();
                    updateStatus('✅ 重新渲染完成');
                } catch (error) {
                    updateStatus(`❌ 渲染失败: ${error.message}`);
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('修复内容：');
                updateStatus('1. 高亮区域位置计算与实际渲染一致');
                updateStatus('2. GIF动画加载和渲染改进');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
