<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 工厂选择修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(200, 100, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .fix {
            background: rgba(0, 200, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>工厂选择修复测试</h3>
        
        <div class="info">
            <strong>问题：</strong><br>
            1. 点击工厂瓷砖后高亮区域与实际不一致<br>
            2. 会卡出加载的转圈圈动画（processingSelection标志位卡住）
        </div>
        
        <div class="fix">
            <strong>修复：</strong><br>
            1. 减少延迟时间，提高响应性<br>
            2. 添加安全机制，防止标志位永久卡住<br>
            3. 在取消选择时重置处理标志位<br>
            4. 添加工厂存在性检查
        </div>
        
        <button onclick="testFactorySelection()">测试工厂选择</button>
        <button onclick="testProcessingFlag()">测试处理标志位</button>
        <button onclick="simulateStuckFlag()">模拟卡住状态</button>
        <button onclick="forceResetFlag()">强制重置标志位</button>
        <button onclick="checkGameState()">检查游戏状态</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                    console.log('Toast:', options.title);
                },
                showModal: (options) => {
                    updateStatus(`Modal: ${options.title}`);
                    console.log('Modal:', options);
                    
                    // 模拟用户选择
                    setTimeout(() => {
                        if (options.success) {
                            options.success({ confirm: true, cancel: false });
                        }
                    }, 1000);
                },
                getUserProfile: (options) => {
                    setTimeout(() => {
                        if (options.success) {
                            options.success({
                                userInfo: {
                                    nickName: '测试玩家',
                                    avatarUrl: 'https://example.com/avatar.jpg'
                                }
                            });
                        }
                    }, 500);
                },
                login: (options) => {
                    setTimeout(() => {
                        if (options.success) {
                            options.success({ code: 'mock_code_123' });
                        }
                    }, 100);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                }),
                getStorageSync: (key) => {
                    return localStorage.getItem(key);
                },
                setStorageSync: (key, value) => {
                    localStorage.setItem(key, value);
                }
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function testFactorySelection() {
            updateStatus('=== 测试工厂选择功能 ===');
            
            if (gameApp && gameApp.gameState) {
                updateStatus('当前游戏状态:');
                updateStatus(`- 阶段: ${gameApp.gameState.phase}`);
                updateStatus(`- 当前玩家: ${gameApp.gameState.currentPlayer}`);
                updateStatus(`- 工厂数量: ${gameApp.gameState.factories.length}`);
                
                // 检查第一个非空工厂
                for (let i = 0; i < gameApp.gameState.factories.length; i++) {
                    const factory = gameApp.gameState.factories[i];
                    if (factory && factory.length > 0) {
                        updateStatus(`测试工厂${i + 1}: [${factory.join(', ')}]`);
                        
                        // 模拟点击工厂
                        try {
                            gameApp.showFactorySelection(i);
                            updateStatus(`✅ 成功显示工厂${i + 1}选择界面`);
                        } catch (error) {
                            updateStatus(`❌ 显示工厂选择失败: ${error.message}`);
                        }
                        break;
                    }
                }
            } else {
                updateStatus('❌ 游戏未初始化或游戏状态不存在');
            }
        }
        
        function testProcessingFlag() {
            updateStatus('=== 测试处理标志位 ===');
            
            if (gameApp) {
                updateStatus(`当前处理标志位: ${gameApp.processingSelection ? '已设置' : '未设置'}`);
                
                if (gameApp.processingSelection) {
                    updateStatus('⚠️ 检测到处理标志位已设置，可能导致卡住');
                } else {
                    updateStatus('✅ 处理标志位正常');
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function simulateStuckFlag() {
            updateStatus('=== 模拟卡住状态 ===');
            
            if (gameApp) {
                gameApp.processingSelection = true;
                updateStatus('⚠️ 已设置处理标志位，模拟卡住状态');
                updateStatus('提示：2秒后应该自动重置（安全机制）');
                
                // 测试安全机制
                setTimeout(() => {
                    updateStatus(`2秒后检查: ${gameApp.processingSelection ? '仍然卡住' : '已自动重置'}`);
                }, 2100);
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function forceResetFlag() {
            updateStatus('=== 强制重置标志位 ===');
            
            if (gameApp) {
                const wasSt = gameApp.processingSelection;
                gameApp.processingSelection = false;
                updateStatus(`标志位重置: ${wasSt ? '是' : '否'} → 否`);
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function checkGameState() {
            updateStatus('=== 检查游戏状态 ===');
            
            if (gameApp && gameApp.gameState) {
                const state = gameApp.gameState;
                updateStatus(`游戏阶段: ${state.phase}`);
                updateStatus(`显示工厂选择: ${state.showingFactorySelection ? '是' : '否'}`);
                updateStatus(`选中工厂: ${state.selectedFactory !== null ? state.selectedFactory + 1 : '无'}`);
                updateStatus(`处理标志位: ${gameApp.processingSelection ? '已设置' : '未设置'}`);
                
                if (gameApp.factoryColorAreas) {
                    updateStatus(`颜色点击区域: ${gameApp.factoryColorAreas.length}个`);
                } else {
                    updateStatus('颜色点击区域: 未设置');
                }
            } else {
                updateStatus('❌ 游戏状态不存在');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('提示：修复包括减少延迟、安全机制、标志位重置等');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
