<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 重新授权流程测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .controls button.danger {
            background: #f44336;
        }
        
        .controls button.danger:hover {
            background: #da190b;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(0, 100, 200, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .step {
            background: rgba(100, 100, 100, 0.2);
            padding: 8px;
            border-radius: 3px;
            margin: 5px 0;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>重新授权流程测试</h3>
        
        <div class="info">
            <strong>测试场景：</strong><br>
            1. 用户首次拒绝排位赛授权<br>
            2. 显示授权要求对话框<br>
            3. 用户点击"重新授权"<br>
            4. 应该再次弹出微信授权窗口
        </div>
        
        <button onclick="startReauthTest()" class="danger">开始重新授权测试</button>
        <button onclick="resetTest()">重置测试</button>
        <button onclick="showMainMenu()">返回主菜单</button>
        
        <div class="step" id="currentStep">等待开始测试...</div>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        let testStep = 0;
        let authAttempts = 0;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                    console.log('Toast:', options.title);
                },
                showModal: (options) => {
                    updateStatus(`🔔 Modal弹窗: ${options.title}`);
                    updateStatus(`内容: ${options.content}`);
                    console.log('Modal:', options);
                    
                    // 模拟用户选择
                    setTimeout(() => {
                        if (options.success) {
                            if (testStep === 1) {
                                // 第一次显示授权要求对话框，用户点击"重新授权"
                                updateStep('用户点击"重新授权"按钮');
                                testStep = 2;
                                options.success({ confirm: true, cancel: false });
                            } else {
                                // 其他情况默认确认
                                options.success({ confirm: true, cancel: false });
                            }
                        }
                    }, 1000);
                },
                getUserProfile: (options) => {
                    authAttempts++;
                    updateStatus(`📱 第${authAttempts}次getUserProfile调用: "${options.desc}"`);
                    
                    if (authAttempts === 1) {
                        // 第一次授权，模拟用户拒绝
                        updateStep('第一次授权请求 - 模拟用户拒绝');
                        testStep = 1;
                        setTimeout(() => {
                            updateStatus('❌ 第一次授权：用户拒绝');
                            if (options.fail) {
                                options.fail({
                                    errMsg: 'getUserProfile:fail auth deny',
                                    errCode: -1
                                });
                            }
                        }, 500);
                    } else if (authAttempts === 2) {
                        // 第二次授权（重新授权），模拟用户同意
                        updateStep('重新授权请求 - 模拟用户同意');
                        testStep = 3;
                        setTimeout(() => {
                            updateStatus('✅ 重新授权：用户同意');
                            if (options.success) {
                                options.success({
                                    userInfo: {
                                        nickName: '测试玩家',
                                        avatarUrl: 'https://example.com/avatar.jpg'
                                    }
                                });
                            }
                        }, 500);
                    } else {
                        // 后续授权，默认同意
                        setTimeout(() => {
                            updateStatus('✅ 后续授权：默认同意');
                            if (options.success) {
                                options.success({
                                    userInfo: {
                                        nickName: '测试玩家',
                                        avatarUrl: 'https://example.com/avatar.jpg'
                                    }
                                });
                            }
                        }, 500);
                    }
                },
                login: (options) => {
                    setTimeout(() => {
                        if (options.success) {
                            options.success({ code: 'mock_code_123' });
                        }
                    }, 100);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                }),
                getStorageSync: (key) => {
                    return localStorage.getItem(key);
                },
                setStorageSync: (key, value) => {
                    localStorage.setItem(key, value);
                }
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function updateStep(step) {
            document.getElementById('currentStep').textContent = `当前步骤: ${step}`;
            updateStatus(`🔄 ${step}`);
        }
        
        function startReauthTest() {
            updateStatus('=== 开始重新授权流程测试 ===');
            updateStep('开始测试 - 点击排位赛按钮');
            testStep = 0;
            authAttempts = 0;
            
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function resetTest() {
            updateStatus('=== 重置测试 ===');
            updateStep('测试已重置');
            testStep = 0;
            authAttempts = 0;
            
            if (gameApp) {
                gameApp.showMainMenu();
            }
        }
        
        function showMainMenu() {
            if (gameApp) {
                gameApp.showMainMenu();
                updateStatus('返回主菜单');
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('提示：测试重新授权时是否会再次弹出授权窗口');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 2000);
    </script>
</body>
</html>
