<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 瓷砖动画测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(0, 200, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>瓷砖动画测试</h3>
        
        <div class="info">
            <strong>新方案：</strong><br>
            由于Canvas无法播放GIF动画，我们实现了自定义的瓷砖动画系统：<br>
            • 亮度变化动画<br>
            • 轻微缩放效果<br>
            • 小羊表情符号装饰<br>
            • 每种颜色独立的动画速度
        </div>
        
        <button onclick="testTileAnimations()">测试瓷砖动画</button>
        <button onclick="initAnimationSystem()">初始化动画系统</button>
        <button onclick="drawAnimatedTiles()">绘制动画瓷砖</button>
        <button onclick="checkAnimationStatus()">检查动画状态</button>
        <button onclick="clearCanvas()">清空Canvas</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    return img;
                },
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function testTileAnimations() {
            updateStatus('=== 测试瓷砖动画 ===');
            
            if (gameApp) {
                // 强制初始化动画系统
                if (!gameApp.tileAnimations) {
                    gameApp.initTileAnimations();
                    updateStatus('✅ 动画系统已初始化');
                }
                
                // 绘制测试瓷砖
                drawAnimatedTiles();
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function initAnimationSystem() {
            updateStatus('=== 初始化动画系统 ===');
            
            if (gameApp) {
                gameApp.initTileAnimations();
                updateStatus('✅ 瓷砖动画系统已初始化');
                updateStatus('动画配置:');
                if (gameApp.tileAnimations) {
                    Object.entries(gameApp.tileAnimations).forEach(([color, config]) => {
                        updateStatus(`  ${color}: ${config.frames}帧, ${config.speed}ms`);
                    });
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function drawAnimatedTiles() {
            updateStatus('=== 绘制动画瓷砖 ===');
            
            if (gameApp) {
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
                colors.forEach((color, index) => {
                    const x = 100 + index * 120;
                    const y = 200;
                    const size = 80;
                    
                    // 使用游戏的渲染方法
                    gameApp.renderTile(color, x, y, size);
                    
                    // 添加标签
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(color, x + size/2, y - 10);
                    
                    updateStatus(`✅ 绘制${color}动画瓷砖`);
                });
                
                updateStatus('动画瓷砖绘制完成，观察是否有动画效果');
                
                // 持续重绘以显示动画
                let frameCount = 0;
                const animateLoop = () => {
                    if (frameCount < 300) { // 绘制5秒
                        colors.forEach((color, index) => {
                            const x = 100 + index * 120;
                            const y = 200;
                            const size = 80;
                            gameApp.renderTile(color, x, y, size);
                        });
                        frameCount++;
                        requestAnimationFrame(animateLoop);
                    }
                };
                animateLoop();
                
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function checkAnimationStatus() {
            updateStatus('=== 检查动画状态 ===');
            
            if (gameApp) {
                updateStatus(`动画系统存在: ${gameApp.tileAnimations ? '是' : '否'}`);
                updateStatus(`当前帧状态: ${gameApp.currentFrames ? '是' : '否'}`);
                updateStatus(`动画计时器: ${gameApp.animationTimers ? '是' : '否'}`);
                
                if (gameApp.currentFrames) {
                    Object.entries(gameApp.currentFrames).forEach(([color, frame]) => {
                        updateStatus(`  ${color}: 第${frame}帧`);
                    });
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function clearCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateStatus('Canvas已清空');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('新动画系统特点：');
                updateStatus('• 替代GIF动画，解决Canvas播放问题');
                updateStatus('• 亮度和缩放变化营造动画效果');
                updateStatus('• 小羊表情符号增加趣味性');
                updateStatus('• 每种颜色独立的动画速度');
                
                // 自动运行测试
                setTimeout(() => {
                    testTileAnimations();
                }, 1000);
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
