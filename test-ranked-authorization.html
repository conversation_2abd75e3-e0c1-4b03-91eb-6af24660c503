<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 排位赛授权测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .controls button.danger {
            background: #f44336;
        }
        
        .controls button.danger:hover {
            background: #da190b;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .scenario {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        
        .scenario h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>排位赛授权测试</h3>
        
        <div class="scenario">
            <h4>测试场景</h4>
            <button onclick="testRankedWithAuth()">模拟用户同意授权</button>
            <button onclick="testRankedWithoutAuth()" class="danger">模拟用户拒绝授权</button>
            <button onclick="testNormalMultiplayer()">测试普通联机（不需要授权）</button>
        </div>
        
        <div class="scenario">
            <h4>直接测试</h4>
            <button onclick="directTestRanked()">直接点击排位赛按钮</button>
            <button onclick="showMainMenu()">返回主菜单</button>
        </div>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        let originalGetUserProfile = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                    console.log('Toast:', options.title);
                },
                showModal: (options) => {
                    updateStatus(`Modal: ${options.title} - ${options.content}`);
                    console.log('Modal:', options);
                    
                    // 模拟用户选择
                    setTimeout(() => {
                        if (options.success) {
                            // 默认用户点击确认
                            options.success({ confirm: true, cancel: false });
                        }
                    }, 1000);
                },
                getUserProfile: (options) => {
                    // 这个方法会被动态替换来模拟不同的授权结果
                    console.log('getUserProfile被调用:', options);
                    updateStatus(`getUserProfile被调用: ${options.desc}`);
                },
                login: (options) => {
                    setTimeout(() => {
                        if (options.success) {
                            options.success({ code: 'mock_code_123' });
                        }
                    }, 100);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                }),
                getStorageSync: (key) => {
                    return localStorage.getItem(key);
                },
                setStorageSync: (key, value) => {
                    localStorage.setItem(key, value);
                }
            };
            
            // 保存原始的getUserProfile方法
            originalGetUserProfile = wx.getUserProfile;
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function testRankedWithAuth() {
            updateStatus('=== 测试排位赛授权成功场景 ===');

            // 模拟用户同意授权
            wx.getUserProfile = (options) => {
                updateStatus(`getUserProfile调用: ${options.desc}`);
                updateStatus('显示微信授权弹窗...');
                setTimeout(() => {
                    if (options.success) {
                        options.success({
                            userInfo: {
                                nickName: '测试玩家',
                                avatarUrl: 'https://example.com/avatar.jpg'
                            }
                        });
                        updateStatus('✅ 模拟用户同意授权');
                    }
                }, 1000); // 增加延迟模拟真实授权过程
            };

            // 点击排位赛按钮
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }

        function testRankedWithoutAuth() {
            updateStatus('=== 测试排位赛授权拒绝场景 ===');

            // 模拟用户拒绝授权
            wx.getUserProfile = (options) => {
                updateStatus(`getUserProfile调用: ${options.desc}`);
                updateStatus('显示微信授权弹窗...');
                setTimeout(() => {
                    if (options.fail) {
                        options.fail({
                            errMsg: 'getUserProfile:fail auth deny',
                            errCode: -1
                        });
                        updateStatus('❌ 模拟用户拒绝授权');
                    }
                }, 1000); // 增加延迟模拟真实授权过程
            };

            // 点击排位赛按钮
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function testNormalMultiplayer() {
            updateStatus('=== 测试普通联机（不需要强制授权）===');
            
            // 模拟用户拒绝授权（普通联机应该能继续）
            wx.getUserProfile = (options) => {
                updateStatus(`getUserProfile调用: ${options.desc}`);
                setTimeout(() => {
                    if (options.fail) {
                        options.fail({
                            errMsg: 'getUserProfile:fail auth deny',
                            errCode: -1
                        });
                        updateStatus('模拟用户拒绝授权（普通联机应该继续）');
                    }
                }, 500);
            };
            
            // 点击联机按钮
            if (gameApp) {
                gameApp.showMultiplayerMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function directTestRanked() {
            updateStatus('=== 直接测试排位赛按钮 ===');
            
            // 恢复原始的getUserProfile方法
            if (originalGetUserProfile) {
                wx.getUserProfile = originalGetUserProfile;
            }
            
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function showMainMenu() {
            if (gameApp) {
                gameApp.showMainMenu();
                updateStatus('返回主菜单');
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('提示：排位赛需要用户授权，普通联机不需要强制授权');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 2000);
    </script>
</body>
</html>
