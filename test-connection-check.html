<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 连接状态检查测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(0, 100, 200, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .fix {
            background: rgba(0, 200, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>连接状态检查测试</h3>
        
        <div class="info">
            <strong>问题：</strong><br>
            MultiplayerManager已存在时，跳过了连接检查，导致后续操作失败
        </div>
        
        <div class="fix">
            <strong>修复：</strong><br>
            即使MultiplayerManager已存在，也要检查连接状态，必要时重新连接
        </div>
        
        <button onclick="testRankedWithServer()">测试排位赛（服务器开启）</button>
        <button onclick="checkConnectionStatus()">检查连接状态</button>
        <button onclick="showMainMenu()">返回主菜单</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                    console.log('Toast:', options.title);
                },
                showModal: (options) => {
                    updateStatus(`🔔 Modal弹窗: ${options.title}`);
                    updateStatus(`内容: ${options.content}`);
                    console.log('Modal:', options);
                    
                    // 模拟用户选择
                    setTimeout(() => {
                        if (options.success) {
                            // 默认用户点击确认
                            options.success({ confirm: true, cancel: false });
                        }
                    }, 1000);
                },
                getUserProfile: (options) => {
                    updateStatus(`📱 getUserProfile调用: "${options.desc}"`);
                    
                    // 模拟用户同意授权
                    setTimeout(() => {
                        updateStatus('✅ 模拟用户同意授权');
                        if (options.success) {
                            options.success({
                                userInfo: {
                                    nickName: '测试玩家',
                                    avatarUrl: 'https://example.com/avatar.jpg'
                                }
                            });
                        }
                    }, 500);
                },
                login: (options) => {
                    setTimeout(() => {
                        if (options.success) {
                            options.success({ code: 'mock_code_123' });
                        }
                    }, 100);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                }),
                getStorageSync: (key) => {
                    return localStorage.getItem(key);
                },
                setStorageSync: (key, value) => {
                    localStorage.setItem(key, value);
                }
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function testRankedWithServer() {
            updateStatus('=== 测试排位赛（服务器开启）===');
            updateStatus('预期：应该能正常连接并进入排位赛界面');
            
            if (gameApp) {
                gameApp.showRankedMenu();
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        function checkConnectionStatus() {
            updateStatus('=== 检查连接状态 ===');
            
            if (gameApp && gameApp.multiplayerManager) {
                const manager = gameApp.multiplayerManager;
                updateStatus(`MultiplayerManager存在: ${!!manager}`);
                updateStatus(`连接状态: ${manager.connected ? '已连接' : '未连接'}`);
                updateStatus(`服务器URL: ${manager.serverUrl || '未设置'}`);
                updateStatus(`玩家ID: ${manager.playerId || '未设置'}`);
                updateStatus(`授权拒绝标识: ${manager.authorizationDenied ? '是' : '否'}`);
            } else {
                updateStatus('MultiplayerManager不存在');
            }
        }
        
        function showMainMenu() {
            if (gameApp) {
                gameApp.showMainMenu();
                updateStatus('返回主菜单');
            } else {
                updateStatus('GameApp未初始化');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('提示：现在会检查连接状态并在必要时重新连接');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 2000);
    </script>
</body>
</html>
