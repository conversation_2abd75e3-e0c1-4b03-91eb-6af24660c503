<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 高亮区域修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(200, 100, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .fix {
            background: rgba(0, 200, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>高亮区域修复测试</h3>
        
        <div class="info">
            <strong>问题：</strong><br>
            点击工厂瓷砖后，高亮区域与实际准备区位置不一致，特别是地板线的高亮位置错误
        </div>
        
        <div class="fix">
            <strong>修复：</strong><br>
            1. 修复地板线高亮位置，使用与renderFloorLineInArea一致的计算<br>
            2. 统一高亮区域和点击区域的位置计算<br>
            3. 确保高亮区域与实际渲染位置完全匹配
        </div>
        
        <button onclick="testHighlightAreas()">测试高亮区域</button>
        <button onclick="simulateTileSelection()">模拟瓷砖选择</button>
        <button onclick="checkClickAreas()">检查点击区域</button>
        <button onclick="comparePositions()">对比位置计算</button>
        <button onclick="testFloorLineHighlight()">测试地板线高亮</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function testHighlightAreas() {
            updateStatus('=== 测试高亮区域 ===');
            
            if (gameApp && gameApp.gameState) {
                // 模拟选择瓷砖状态
                gameApp.gameState.selectedTiles = ['blue', 'blue'];
                gameApp.gameState.selectedColor = 'blue';
                
                updateStatus('设置选中瓷砖状态');
                updateStatus('强制渲染以显示高亮区域');
                
                try {
                    gameApp.render();
                    updateStatus('✅ 高亮区域渲染成功');
                } catch (error) {
                    updateStatus(`❌ 高亮区域渲染失败: ${error.message}`);
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function simulateTileSelection() {
            updateStatus('=== 模拟瓷砖选择流程 ===');
            
            if (gameApp && gameApp.gameState && gameApp.gameState.factories) {
                // 找到第一个非空工厂
                for (let i = 0; i < gameApp.gameState.factories.length; i++) {
                    const factory = gameApp.gameState.factories[i];
                    if (factory && factory.length > 0) {
                        updateStatus(`选择工厂${i + 1}: [${factory.join(', ')}]`);
                        
                        // 模拟选择第一种颜色
                        const color = factory[0];
                        const tiles = factory.filter(tile => tile === color);
                        
                        updateStatus(`选择颜色: ${color}, 数量: ${tiles.length}`);
                        
                        // 设置选择状态
                        gameApp.gameState.selectedTiles = tiles;
                        gameApp.gameState.selectedColor = color;
                        gameApp.gameState.showingFactorySelection = false;
                        
                        // 强制渲染显示高亮
                        gameApp.render();
                        
                        updateStatus('✅ 瓷砖选择模拟完成，应该看到准备区高亮');
                        break;
                    }
                }
            } else {
                updateStatus('❌ 游戏状态不可用');
            }
        }
        
        function checkClickAreas() {
            updateStatus('=== 检查点击区域 ===');
            
            if (gameApp && gameApp.placementClickAreas) {
                updateStatus(`点击区域数量: ${gameApp.placementClickAreas.length}`);
                
                gameApp.placementClickAreas.forEach((area, index) => {
                    updateStatus(`区域${index + 1}: ${area.type} (行${area.lineIndex + 1}) - 位置:(${Math.round(area.x)}, ${Math.round(area.y)}) 大小:${Math.round(area.width)}x${Math.round(area.height)}`);
                });
            } else {
                updateStatus('❌ 点击区域未设置');
            }
        }
        
        function comparePositions() {
            updateStatus('=== 对比位置计算 ===');
            
            if (gameApp && gameApp.gameState && gameApp.gameState.players) {
                const player = gameApp.gameState.players[0];
                if (player) {
                    updateStatus('准备区位置计算:');
                    
                    // 模拟面板位置
                    const panelPos = gameApp.getPlayerPanelPosition(0);
                    if (panelPos) {
                        updateStatus(`面板位置: (${panelPos.x}, ${panelPos.y}) ${panelPos.width}x${panelPos.height}`);
                        updateStatus(`内容区域: (${panelPos.contentX}, ${panelPos.y + 35})`);
                        
                        // 计算地板线位置
                        const gameAreaX = panelPos.contentX;
                        const gameAreaY = panelPos.y + 35;
                        const gameAreaHeight = panelPos.height - 35;
                        const floorAreaHeight = 35;
                        const floorX = gameAreaX + 10;
                        const floorY = gameAreaY + gameAreaHeight - floorAreaHeight - 10;
                        
                        updateStatus(`地板线计算位置: (${Math.round(floorX)}, ${Math.round(floorY)})`);
                        updateStatus(`槽位位置: (${Math.round(floorX + 60)}, ${Math.round(floorY + 5)})`);
                    } else {
                        updateStatus('❌ 无法获取面板位置');
                    }
                } else {
                    updateStatus('❌ 玩家数据不存在');
                }
            } else {
                updateStatus('❌ 游戏数据不可用');
            }
        }
        
        function testFloorLineHighlight() {
            updateStatus('=== 测试地板线高亮 ===');
            
            if (gameApp && gameApp.gameState) {
                // 设置选中状态以触发地板线高亮
                gameApp.gameState.selectedTiles = ['red'];
                gameApp.gameState.selectedColor = 'red';
                
                updateStatus('设置选中状态，地板线应该高亮显示');
                
                try {
                    gameApp.render();
                    updateStatus('✅ 地板线高亮渲染完成');
                    updateStatus('检查地板线是否正确高亮（红色边框）');
                } catch (error) {
                    updateStatus(`❌ 地板线高亮失败: ${error.message}`);
                }
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('提示：修复了地板线高亮位置计算，现在应该与实际渲染位置一致');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
