<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>棋盘背景测试 - 花砖物语</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .preview-area {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .board-preview {
            width: 100%;
            max-width: 600px;
            height: 200px;
            border: 2px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            margin: 10px auto;
            display: block;
            position: relative;
        }
        .overlay-demo {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            background: rgba(33, 150, 243, 0.2);
            border: 2px solid #2196f3;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2196f3;
            font-weight: bold;
            font-size: 18px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .loading { background: rgba(255, 193, 7, 0.3); }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.5);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 棋盘背景测试</h1>
        
        <div id="status" class="status loading">正在加载棋盘图片...</div>
        
        <div class="preview-area">
            <h2>🏁 棋盘图片预览 (5x5色块)</h2>
            <div id="boardPreview" class="board-preview">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    加载中...
                </div>
            </div>
        </div>

        <div class="preview-area">
            <h2>🏰 墙壁效果预览</h2>
            <div style="background: white; padding: 20px; border-radius: 8px;">
                <canvas id="wallPreview" width="400" height="300" style="border: 1px solid #ccc; border-radius: 4px;"></canvas>
                <p style="color: #666; margin-top: 10px;">模拟墙壁的5x5网格，空槽位显示棋盘图片的对应色块</p>
            </div>
        </div>
        
        <div class="info">
            <h3>📝 修改说明</h3>
            <p>✅ 已添加 <code>renderWallSlot</code> 方法</p>
            <p>✅ 墙壁空槽位现在使用 <code>images/board.png</code> 的对应部分</p>
            <p>✅ 棋盘图片按5x5网格分割，每个槽位显示对应的色块</p>
            <p>✅ 如果棋盘图片加载失败，自动回退到浅色默认颜色</p>
            <p>✅ 添加半透明白色边框以区分槽位</p>
            <p>✅ 已填充的墙壁格子仍显示瓷砖图片</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.location.href='index.html'">启动游戏测试</button>
            <button onclick="location.reload()">重新加载</button>
            <button onclick="window.location.href='verify_resources.html'">验证所有资源</button>
        </div>
    </div>

    <script>
        async function testBoardBackground() {
            const status = document.getElementById('status');
            const boardPreview = document.getElementById('boardPreview');
            const wallCanvas = document.getElementById('wallPreview');

            try {
                // 测试棋盘图片加载
                let img;
                if (typeof wx !== 'undefined' && wx.createImage) {
                    img = wx.createImage();
                } else {
                    img = new Image();
                }

                await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = reject;
                    img.src = 'images/board.png';
                });

                // 设置棋盘预览背景
                boardPreview.style.backgroundImage = 'url(images/board.png)';
                boardPreview.innerHTML = '';

                // 绘制墙壁预览
                drawWallPreview(wallCanvas, img);

                status.textContent = '✅ 棋盘图片加载成功！墙壁空槽位已更新为5x5色块。';
                status.className = 'status success';

            } catch (error) {
                status.textContent = `❌ 棋盘图片加载失败: ${error.message}`;
                status.className = 'status error';

                // 显示错误状态
                boardPreview.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #ff4444; font-weight: bold;">❌ 加载失败</div>';
                boardPreview.style.backgroundColor = '#ff4444';
            }
        }

        function drawWallPreview(canvas, boardImg) {
            const ctx = canvas.getContext('2d');
            const slotSize = 35;
            const gap = 3;
            const startX = 50;
            const startY = 30;

            // 清空画布
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 添加标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('墙壁:', startX, startY - 10);

            // 绘制5x5墙壁网格
            for (let row = 0; row < 5; row++) {
                for (let col = 0; col < 5; col++) {
                    const x = startX + col * (slotSize + gap);
                    const y = startY + row * (slotSize + gap);

                    // 模拟一些已填充的格子（显示瓷砖）
                    const isFilled = (row === 0 && col === 0) || (row === 1 && col === 1) || (row === 2 && col === 2);

                    if (isFilled) {
                        // 已填充：显示彩色瓷砖（模拟）
                        const colors = ['#2196f3', '#ffc107', '#f44336', '#424242', '#009688'];
                        ctx.fillStyle = colors[row];
                        ctx.fillRect(x, y, slotSize, slotSize);
                        ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(x, y, slotSize, slotSize);
                    } else {
                        // 未填充：显示棋盘图片的对应部分
                        const sourceSize = boardImg.width / 5;
                        const sourceX = col * sourceSize;
                        const sourceY = row * sourceSize;

                        // 绘制棋盘图片的对应部分
                        ctx.drawImage(
                            boardImg,
                            sourceX, sourceY, sourceSize, sourceSize,
                            x, y, slotSize, slotSize
                        );

                        // 添加边框
                        ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                        ctx.lineWidth = 1;
                        ctx.strokeRect(x, y, slotSize, slotSize);
                    }
                }
            }

            // 添加说明文字
            ctx.fillStyle = '#495057';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('墙壁预览：空槽位显示棋盘图片对应位置的色块，已填充格子显示瓷砖', 20, canvas.height - 20);
        }

        // 页面加载完成后开始测试
        window.addEventListener('load', testBoardBackground);
    </script>
</body>
</html>
