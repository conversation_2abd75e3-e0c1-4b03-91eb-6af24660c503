<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>队列界面BGM开关修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 20px;
        }
        
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .fix-info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .fix-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
        
        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 队列界面BGM开关修复测试</h1>
        
        <div class="fix-info">
            <h3>修复内容</h3>
            <ul>
                <li>在队列界面的触摸事件处理中添加了BGM按钮检测</li>
                <li>在排行榜界面的触摸事件处理中添加了BGM按钮检测</li>
                <li>更新了updateBgmButtonState()方法以支持队列和排行榜界面</li>
                <li>现在所有排位赛相关界面都支持BGM开关</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testQueueBgm()">测试队列界面BGM</button>
            <button onclick="testLeaderboardBgm()">测试排行榜界面BGM</button>
            <button onclick="simulateQueueClick()">模拟队列界面点击</button>
            <button onclick="showButtonInfo()">显示按钮信息</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`, 'success');
                },
                onTouchStart: (handler) => {
                    console.log('设置触摸事件监听器');
                    window.wxTouchHandler = handler;
                },
                offTouchStart: (handler) => {
                    console.log('移除触摸事件监听器');
                    if (window.wxTouchHandler === handler) {
                        window.wxTouchHandler = null;
                    }
                }
            };
        }
        
        let gameApp;
        let rankedUI;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('测试状态:', message);
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功', 'success');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`, 'error');
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function initializeRankedUI() {
            const app = initializeGameApp();
            if (app && !rankedUI) {
                try {
                    rankedUI = new RankedUI(app.sharedCanvas, null);
                    rankedUI.userRanking = {
                        rating: 1587,
                        tier: { name: '白银 3', icon: '🥈', color: '#C0C0C0', progress: 65 },
                        rank: 4,
                        totalGames: 115,
                        wins: 19,
                        losses: 96,
                        winRate: 0.165,
                        currentStreak: 0,
                        highestRating: 1587
                    };
                    app.rankedUI = rankedUI;
                    app.currentScreen = 'ranked';
                    updateStatus('排位赛UI初始化成功', 'success');
                } catch (error) {
                    updateStatus(`排位赛UI初始化失败: ${error.message}`, 'error');
                }
            }
            return rankedUI;
        }
        
        function testQueueBgm() {
            const ui = initializeRankedUI();
            if (ui) {
                try {
                    // 模拟进入队列界面
                    ui.currentScreen = 'queue';
                    ui.updateQueueDisplay();
                    
                    updateStatus('队列界面显示成功，请点击左上角BGM开关测试', 'info');
                    
                    // 显示按钮信息
                    setTimeout(() => {
                        showButtonInfo();
                    }, 1000);
                    
                } catch (error) {
                    updateStatus(`队列界面测试失败: ${error.message}`, 'error');
                }
            }
        }
        
        function testLeaderboardBgm() {
            const ui = initializeRankedUI();
            if (ui) {
                try {
                    // 模拟进入排行榜界面
                    ui.currentScreen = 'leaderboard';
                    ui.showLeaderboardScreen();
                    
                    updateStatus('排行榜界面显示成功，请点击左上角BGM开关测试', 'info');
                    
                    // 显示按钮信息
                    setTimeout(() => {
                        showButtonInfo();
                    }, 1000);
                    
                } catch (error) {
                    updateStatus(`排行榜界面测试失败: ${error.message}`, 'error');
                }
            }
        }
        
        function simulateQueueClick() {
            if (rankedUI && window.wxTouchHandler) {
                try {
                    // 模拟点击BGM按钮位置
                    const touchEvent = {
                        touches: [{
                            clientX: 45,  // BGM按钮的大概位置
                            clientY: 45
                        }]
                    };
                    
                    updateStatus('模拟点击BGM按钮位置 (45, 45)', 'info');
                    window.wxTouchHandler(touchEvent);
                    
                    // 检查BGM状态变化
                    setTimeout(() => {
                        if (gameApp) {
                            const bgmStatus = gameApp.bgmEnabled ? '开启' : '关闭';
                            updateStatus(`BGM状态: ${bgmStatus}`, 'success');
                        }
                    }, 500);
                    
                } catch (error) {
                    updateStatus(`模拟点击失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('请先测试队列界面或排行榜界面', 'error');
            }
        }
        
        function showButtonInfo() {
            if (rankedUI) {
                const buttons = rankedUI.buttons || {};
                const buttonNames = Object.keys(buttons);
                const currentScreen = rankedUI.currentScreen;
                const bgmButton = buttons.bgmToggle;
                
                let info = `当前界面: ${currentScreen}\n`;
                info += `可用按钮: [${buttonNames.join(', ')}]\n`;
                
                if (bgmButton) {
                    info += `BGM按钮位置: (${bgmButton.x}, ${bgmButton.y}) 尺寸: ${bgmButton.width}x${bgmButton.height}\n`;
                } else {
                    info += 'BGM按钮: 未找到\n';
                }
                
                if (gameApp) {
                    info += `BGM状态: ${gameApp.bgmEnabled ? '开启' : '关闭'}`;
                }
                
                updateStatus(info, 'info');
            } else {
                updateStatus('排位赛UI未初始化', 'error');
            }
        }
        
        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateStatus('页面加载完成，准备测试队列界面BGM开关修复...');
            }, 500);
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>
