<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BGM开关循环问题修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 20px;
        }
        
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .fix-info h3 {
            margin-top: 0;
            color: #FF5722;
        }
        
        .fix-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-counter {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 BGM开关循环问题修复测试</h1>
        
        <div class="fix-info">
            <h3>问题描述</h3>
            <ul>
                <li>之前点击BGM开关会触发无限循环</li>
                <li>每次点击都会重新初始化整个排位赛界面</li>
                <li>导致大量重复日志输出</li>
            </ul>
            <h3>修复方案</h3>
            <ul>
                <li>移除refreshCurrentScreen()方法</li>
                <li>添加updateBgmButtonState()方法</li>
                <li>只更新BGM按钮区域，不重新渲染整个界面</li>
            </ul>
        </div>
        
        <div class="log-counter" id="logCounter">
            日志计数器：等待测试...
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testBgmToggle()">测试BGM开关</button>
            <button onclick="startLogMonitoring()">开始日志监控</button>
            <button onclick="stopLogMonitoring()">停止日志监控</button>
            <button onclick="clearLogCounter()">清除计数器</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`, 'success');
                },
                onTouchStart: (handler) => {
                    console.log('设置触摸事件监听器');
                    window.wxTouchHandler = handler;
                },
                offTouchStart: (handler) => {
                    console.log('移除触摸事件监听器');
                    if (window.wxTouchHandler === handler) {
                        window.wxTouchHandler = null;
                    }
                }
            };
        }
        
        let gameApp;
        let rankedUI;
        let logCounts = {};
        let logMonitoring = false;
        let originalConsoleLog = console.log;
        
        // 重写console.log来监控日志
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            if (logMonitoring) {
                const message = args.join(' ');
                
                // 统计特定日志的出现次数
                const patterns = [
                    '绑定主界面事件',
                    '设置活跃排位赛UI实例',
                    '清除活跃排位赛UI实例',
                    '清理排位赛UI主界面事件监听器',
                    'BGM开关',
                    '排位赛UI触摸事件',
                    '排位赛UI点击坐标'
                ];
                
                patterns.forEach(pattern => {
                    if (message.includes(pattern)) {
                        logCounts[pattern] = (logCounts[pattern] || 0) + 1;
                    }
                });
                
                updateLogCounter();
            }
        };
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            originalConsoleLog('测试状态:', message);
        }
        
        function updateLogCounter() {
            const counterDiv = document.getElementById('logCounter');
            let counterText = '日志计数器:\n';
            
            for (const [pattern, count] of Object.entries(logCounts)) {
                counterText += `${pattern}: ${count}次\n`;
            }
            
            counterDiv.textContent = counterText;
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功', 'success');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`, 'error');
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function testBgmToggle() {
            const app = initializeGameApp();
            if (app) {
                try {
                    // 初始化排位赛UI
                    if (!rankedUI) {
                        rankedUI = new RankedUI(app.sharedCanvas, null);
                        rankedUI.userRanking = {
                            rating: 1587,
                            tier: { name: '白银 3', icon: '🥈', color: '#C0C0C0', progress: 65 },
                            rank: 4,
                            totalGames: 115,
                            wins: 19,
                            losses: 96,
                            winRate: 0.165,
                            currentStreak: 0,
                            highestRating: 1587
                        };
                        app.rankedUI = rankedUI;
                        app.currentScreen = 'ranked';
                    }
                    
                    rankedUI.showMainScreen();
                    updateStatus('排位赛界面初始化完成', 'success');
                    
                    // 开始日志监控
                    startLogMonitoring();
                    
                    // 测试BGM开关
                    setTimeout(() => {
                        updateStatus('开始测试BGM开关...', 'info');
                        const initialBgmState = app.bgmEnabled;
                        
                        // 点击BGM开关
                        app.toggleBgm();
                        
                        setTimeout(() => {
                            const finalBgmState = app.bgmEnabled;
                            updateStatus(`BGM状态从 ${initialBgmState ? '开启' : '关闭'} 切换到 ${finalBgmState ? '开启' : '关闭'}`, 'success');
                            
                            // 检查是否有循环
                            const bindingCount = logCounts['绑定主界面事件'] || 0;
                            if (bindingCount <= 2) {
                                updateStatus(`✅ 修复成功！绑定事件只执行了${bindingCount}次，没有循环`, 'success');
                            } else {
                                updateStatus(`❌ 仍有问题！绑定事件执行了${bindingCount}次，可能存在循环`, 'error');
                            }
                        }, 1000);
                    }, 1000);
                    
                } catch (error) {
                    updateStatus(`BGM开关测试失败: ${error.message}`, 'error');
                    console.error('测试错误:', error);
                }
            }
        }
        
        function startLogMonitoring() {
            logMonitoring = true;
            logCounts = {};
            updateStatus('开始监控日志...', 'info');
            updateLogCounter();
        }
        
        function stopLogMonitoring() {
            logMonitoring = false;
            updateStatus('停止监控日志', 'info');
        }
        
        function clearLogCounter() {
            logCounts = {};
            updateLogCounter();
            updateStatus('清除日志计数器', 'info');
        }
        
        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateStatus('页面加载完成，准备测试BGM开关循环修复...');
            }, 500);
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>
