# 花砖物语 - 资源文件重命名说明

## 问题描述
微信小游戏环境对中文文件名的处理存在编码问题，导致资源文件无法正确加载。错误信息显示文件名被URL编码，例如：
- `小白羊.gif` 被编码为 `%E5%B0%8F%E7%99%BD%E7%BE%8A.gif`

## 解决方案
将所有中文文件名重命名为英文文件名，以避免编码问题。

## 文件重命名对照表

### 图片文件 (images/)
| 原文件名 | 新文件名 | 说明 |
|---------|---------|------|
| 小白羊.gif | white_sheep.gif | 蓝色瓷砖 |
| 小黄羊.gif | yellow_sheep.gif | 黄色瓷砖 |
| 小粉羊.gif | pink_sheep.gif | 红色瓷砖 |
| 小黑羊.gif | black_sheep.gif | 黑色瓷砖 |
| 小灰羊.gif | gray_sheep.gif | 青色瓷砖 |
| 背景.jpg | background.jpg | 游戏背景 |
| 棋盘.png | board.png | 游戏棋盘 |

### 音效文件 (sounds/)
| 原文件名 | 新文件名 | 说明 |
|---------|---------|------|
| 游戏BGM1.m4a | game_bgm1.m4a | 背景音乐1 |
| 游戏BGM2.mp3 | game_bgm2.mp3 | 背景音乐2 |
| 被选定音效.wav | tile_select.wav | 瓷砖选择音效 |
| 咩咩音效.wav | tile_place.wav | 瓷砖放置音效 |
| 成熟咩咩.wav | score_update.wav | 得分音效 |
| 绵羊咩.wav | round_end.wav | 回合结束音效 |
| 公羊咩咩.wav | game_win.wav | 胜利音效 |
| 魔性.m4a | button_click.m4a | 按钮点击音效 |

## 重命名方法

### 方法1：使用批处理文件 (Windows)
1. 双击运行 `rename_files.bat`
2. 等待重命名完成

### 方法2：使用PowerShell脚本 (Windows)
1. 右键点击 `rename_files.ps1`
2. 选择"使用PowerShell运行"
3. 如果提示执行策略问题，运行：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### 方法3：手动重命名
按照上面的对照表，手动将每个文件重命名为对应的英文名称。

## 重命名后的效果
- ✅ 微信小游戏环境可以正确加载所有资源
- ✅ 瓷砖显示为可爱的小羊图片
- ✅ 背景音乐和音效正常播放
- ✅ 游戏界面显示背景图片

## 注意事项
1. 重命名前请确保游戏已关闭
2. 重命名后需要重新启动微信开发者工具
3. 如果重命名失败，请检查文件是否被其他程序占用
4. 建议在重命名前备份原文件

## 验证重命名结果
重命名完成后，可以：
1. 打开 `test-resources.html` 测试资源加载
2. 启动游戏查看瓷砖和背景是否正确显示
3. 测试音效是否正常播放

如果仍有问题，请检查文件名是否完全匹配代码中的配置。
