<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 简单动画测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(0, 200, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>简单动画测试</h3>
        
        <div class="info">
            <strong>新方案：</strong><br>
            直接在renderTile方法中集成简单动画：<br>
            • 基于时间戳的亮度变化<br>
            • 动画高光效果<br>
            • 小羊表情符号装饰<br>
            • 2秒一个动画周期
        </div>
        
        <button onclick="startAnimation()">🎬 开始动画测试</button>
        <button onclick="stopAnimation()">⏹️ 停止动画</button>
        <button onclick="testSingleFrame()">🖼️ 测试单帧</button>
        <button onclick="clearCanvas()">🧹 清空Canvas</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        let animationRunning = false;
        let animationId = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function startAnimation() {
            updateStatus('=== 开始动画测试 ===');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            animationRunning = true;
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
            let frameCount = 0;
            
            const animate = () => {
                if (!animationRunning) return;
                
                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('瓷砖动画测试', canvas.width / 2, 50);
                
                // 绘制动画瓷砖
                colors.forEach((color, index) => {
                    const x = 100 + index * 120;
                    const y = 200;
                    const size = 80;
                    
                    // 使用游戏的renderTile方法
                    gameApp.renderTile(color, x, y, size);
                    
                    // 添加颜色标签
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(color, x + size/2, y - 10);
                });
                
                // 显示帧计数
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`帧数: ${frameCount}`, 20, canvas.height - 20);
                
                frameCount++;
                animationId = requestAnimationFrame(animate);
            };
            
            updateStatus('✅ 动画已启动，观察瓷砖亮度变化');
            animate();
        }
        
        function stopAnimation() {
            updateStatus('=== 停止动画 ===');
            animationRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            updateStatus('✅ 动画已停止');
        }
        
        function testSingleFrame() {
            updateStatus('=== 测试单帧渲染 ===');
            
            if (!gameApp) {
                updateStatus('❌ 游戏未初始化');
                return;
            }
            
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空Canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
            
            colors.forEach((color, index) => {
                const x = 100 + index * 120;
                const y = 200;
                const size = 80;
                
                updateStatus(`渲染${color}瓷砖...`);
                
                try {
                    gameApp.renderTile(color, x, y, size);
                    updateStatus(`✅ ${color}瓷砖渲染成功`);
                } catch (error) {
                    updateStatus(`❌ ${color}瓷砖渲染失败: ${error.message}`);
                }
                
                // 添加颜色标签
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(color, x + size/2, y - 10);
            });
            
            updateStatus('单帧测试完成');
        }
        
        function clearCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateStatus('Canvas已清空');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成');
                updateStatus('新动画方案：直接集成到renderTile方法');
                updateStatus('• 基于时间戳的平滑动画');
                updateStatus('• 亮度变化效果');
                updateStatus('• 动画高光效果');
                updateStatus('• 小羊表情符号装饰');
                
                // 自动测试单帧
                setTimeout(() => {
                    testSingleFrame();
                }, 1000);
                
                // 自动开始动画
                setTimeout(() => {
                    startAnimation();
                }, 2000);
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
