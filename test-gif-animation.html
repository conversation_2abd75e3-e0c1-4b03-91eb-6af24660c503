<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - GIF动画专项测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 450px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .info {
            background: rgba(200, 100, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .test-area {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        
        .gif-test {
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #333;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>GIF动画专项测试</h3>
        
        <div class="info">
            <strong>测试目标：</strong><br>
            检查小羊GIF动画是否能在Canvas中正常播放
        </div>
        
        <button onclick="runGifTest()">运行GIF动画测试</button>
        <button onclick="testDirectGifLoad()">直接测试GIF加载</button>
        <button onclick="checkImageProperties()">检查图片属性</button>
        <button onclick="drawTestTiles()">绘制测试瓷砖</button>
        <button onclick="clearCanvas()">清空Canvas</button>
        
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <div class="test-area">
        <h4>直接GIF测试</h4>
        <div id="gifTestArea">
            <!-- 这里会显示直接加载的GIF -->
        </div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    return img;
                },
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('状态:', message);
        }
        
        function runGifTest() {
            updateStatus('=== 运行GIF动画测试 ===');
            
            if (gameApp && gameApp.testGifAnimation) {
                gameApp.testGifAnimation();
                updateStatus('✅ GIF动画测试已运行，检查控制台和Canvas');
            } else {
                updateStatus('❌ 游戏未初始化或测试方法不存在');
            }
        }
        
        function testDirectGifLoad() {
            updateStatus('=== 直接测试GIF加载 ===');
            
            const gifFiles = [
                'images/white_sheep.gif',
                'images/yellow_sheep.gif', 
                'images/pink_sheep.gif',
                'images/black_sheep.gif',
                'images/gray_sheep.gif'
            ];
            
            const testArea = document.getElementById('gifTestArea');
            testArea.innerHTML = '';
            
            gifFiles.forEach((src, index) => {
                const div = document.createElement('div');
                div.className = 'gif-test';
                
                const img = new Image();
                img.src = src;
                img.width = 50;
                img.height = 50;
                img.style.border = '1px solid white';
                
                img.onload = () => {
                    updateStatus(`✅ 直接加载成功: ${src}`);
                };
                
                img.onerror = () => {
                    updateStatus(`❌ 直接加载失败: ${src}`);
                };
                
                div.appendChild(img);
                div.appendChild(document.createTextNode(` ${src.split('/').pop()}`));
                testArea.appendChild(div);
            });
            
            updateStatus('直接GIF测试已启动，查看右侧测试区域');
        }
        
        function checkImageProperties() {
            updateStatus('=== 检查图片属性 ===');
            
            if (gameApp && gameApp.images) {
                const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
                colors.forEach(color => {
                    const img = gameApp.images[color];
                    if (img) {
                        updateStatus(`${color}:`);
                        updateStatus(`  源: ${img.src}`);
                        updateStatus(`  完成: ${img.complete}`);
                        updateStatus(`  宽度: ${img.naturalWidth || img.width}`);
                        updateStatus(`  高度: ${img.naturalHeight || img.height}`);
                        updateStatus(`  GIF: ${img.src && img.src.includes('.gif') ? '是' : '否'}`);
                    } else {
                        updateStatus(`${color}: 未找到`);
                    }
                });
            } else {
                updateStatus('❌ 游戏图片资源未加载');
            }
        }
        
        function drawTestTiles() {
            updateStatus('=== 绘制测试瓷砖 ===');
            
            if (gameApp && gameApp.images) {
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const colors = ['blue', 'yellow', 'red', 'black', 'teal'];
                colors.forEach((color, index) => {
                    const img = gameApp.images[color];
                    if (img && img.complete) {
                        const x = 50 + index * 80;
                        const y = 100;
                        const size = 60;
                        
                        try {
                            ctx.drawImage(img, x, y, size, size);
                            
                            // 添加标签
                            ctx.fillStyle = 'white';
                            ctx.font = '14px Arial';
                            ctx.fillText(color, x, y - 10);
                            
                            updateStatus(`✅ 绘制${color}瓷砖成功`);
                        } catch (error) {
                            updateStatus(`❌ 绘制${color}瓷砖失败: ${error.message}`);
                        }
                    } else {
                        updateStatus(`❌ ${color}图片未加载或不完整`);
                    }
                });
                
                updateStatus('测试瓷砖绘制完成，检查Canvas中间区域');
            } else {
                updateStatus('❌ 游戏未初始化');
            }
        }
        
        function clearCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateStatus('Canvas已清空');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待GameApp初始化...');
            
            // 立即测试直接GIF加载
            setTimeout(() => {
                testDirectGifLoad();
            }, 1000);
        });
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
                updateStatus('提示：如果右侧GIF能动但Canvas中不动，说明是Canvas渲染问题');
                
                // 自动运行一次测试
                setTimeout(() => {
                    runGifTest();
                }, 1000);
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 3000);
    </script>
</body>
</html>
