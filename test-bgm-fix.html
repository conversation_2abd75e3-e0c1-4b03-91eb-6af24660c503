<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BGM开关修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 20px;
        }
        
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .fix-info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .fix-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
        }
        
        .debug-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 BGM开关修复测试</h1>
        
        <div class="fix-info">
            <h3>修复内容</h3>
            <ul>
                <li>修复了RankedUI中按钮列表被覆盖的问题</li>
                <li>修复了MultiplayerUI中按钮列表被清空的问题</li>
                <li>在队列界面和排行榜界面添加了BGM开关</li>
                <li>确保BGM按钮在界面切换时不会丢失</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testRankedUI()">测试排位赛界面</button>
            <button onclick="simulateClick(50, 47)">模拟点击BGM按钮</button>
            <button onclick="showDebugInfo()">显示调试信息</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
        
        <div class="debug-info" id="debugInfo">
            调试信息将在这里显示...
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                },
                onTouchStart: (handler) => {
                    console.log('设置触摸事件监听器');
                    window.wxTouchHandler = handler;
                },
                offTouchStart: (handler) => {
                    console.log('移除触摸事件监听器');
                    if (window.wxTouchHandler === handler) {
                        window.wxTouchHandler = null;
                    }
                }
            };
        }
        
        let gameApp;
        let rankedUI;
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `状态：${message}`;
            console.log('测试状态:', message);
        }
        
        function updateDebugInfo(info) {
            document.getElementById('debugInfo').innerHTML = info;
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    // 加载game.js中的类
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`);
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function testRankedUI() {
            const app = initializeGameApp();
            if (app) {
                try {
                    // 模拟排位赛UI初始化
                    if (!rankedUI) {
                        rankedUI = new RankedUI(app.sharedCanvas, null);
                        rankedUI.userRanking = {
                            rating: 1587,
                            tier: { name: '白银 3', icon: '🥈', color: '#C0C0C0', progress: 65 },
                            rank: 4,
                            totalGames: 115,
                            wins: 19,
                            losses: 96,
                            winRate: 0.165,
                            currentStreak: 0,
                            highestRating: 1587
                        };
                    }
                    
                    rankedUI.showMainScreen();
                    updateStatus('排位赛界面显示成功 - 请点击左上角BGM开关测试');
                    
                    // 显示调试信息
                    showDebugInfo();
                    
                } catch (error) {
                    updateStatus(`排位赛界面测试失败: ${error.message}`);
                    console.error('测试错误:', error);
                }
            }
        }
        
        function simulateClick(x, y) {
            if (rankedUI) {
                try {
                    // 模拟触摸事件
                    const touchEvent = {
                        touches: [{
                            clientX: x,
                            clientY: y
                        }]
                    };
                    
                    if (window.wxTouchHandler) {
                        window.wxTouchHandler(touchEvent);
                        updateStatus(`模拟点击坐标 (${x}, ${y})`);
                    } else {
                        updateStatus('没有找到触摸事件处理器');
                    }
                    
                    // 更新调试信息
                    setTimeout(showDebugInfo, 100);
                    
                } catch (error) {
                    updateStatus(`模拟点击失败: ${error.message}`);
                    console.error('点击错误:', error);
                }
            } else {
                updateStatus('请先测试排位赛界面');
            }
        }
        
        function showDebugInfo() {
            if (rankedUI) {
                const buttons = rankedUI.buttons || {};
                const buttonNames = Object.keys(buttons);
                const bgmButton = buttons.bgmToggle;
                const bgmEnabled = gameApp ? gameApp.bgmEnabled : 'unknown';
                
                const info = `
调试信息:
- 可用按钮: [${buttonNames.join(', ')}]
- BGM按钮存在: ${bgmButton ? 'YES' : 'NO'}
- BGM按钮位置: ${bgmButton ? `(${bgmButton.x}, ${bgmButton.y}, ${bgmButton.width}x${bgmButton.height})` : 'N/A'}
- BGM状态: ${bgmEnabled}
- 当前界面: ${rankedUI.currentScreen}
- 实例ID: ${rankedUI.instanceId}
                `.trim();
                
                updateDebugInfo(info);
            } else {
                updateDebugInfo('RankedUI未初始化');
            }
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateStatus('页面加载完成，准备测试...');
            }, 500);
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>
