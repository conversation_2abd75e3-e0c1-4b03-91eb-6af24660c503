<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语 - 加载动画测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #45a049;
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>加载动画测试</h3>
        <button onclick="testLoadingAnimation()">测试加载动画</button>
        <button onclick="testResourceLoading()">测试资源加载</button>
        <button onclick="showMainMenu()">显示主菜单</button>
        <div class="status" id="status">等待测试...</div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>

    <script>
        let gameApp = null;
        
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`);
                    console.log('Toast:', options.title);
                },
                getSystemInfoSync: () => ({
                    windowWidth: 800,
                    windowHeight: 600
                })
            };
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('状态:', message);
        }
        
        function testLoadingAnimation() {
            updateStatus('测试加载动画...');
            
            if (!gameApp) {
                updateStatus('GameApp未初始化，先创建实例');
                return;
            }
            
            // 显示加载动画
            gameApp.showInitialLoadingScreen();
            gameApp.startLoadingAnimation();
            
            // 模拟不同的加载状态
            setTimeout(() => {
                gameApp.updateLoadingStatus('正在加载图片资源...');
            }, 1000);
            
            setTimeout(() => {
                gameApp.updateLoadingStatus('正在加载音效资源...');
            }, 2000);
            
            setTimeout(() => {
                gameApp.updateLoadingStatus('资源加载完成！');
            }, 3000);
            
            setTimeout(() => {
                gameApp.stopLoadingAnimation();
                updateStatus('加载动画测试完成');
            }, 4000);
        }
        
        function testResourceLoading() {
            updateStatus('测试完整资源加载流程...');
            
            if (!gameApp) {
                updateStatus('GameApp未初始化，先创建实例');
                return;
            }
            
            // 重新初始化以测试完整流程
            gameApp.showInitialLoadingScreen();
            gameApp.startLoadingAnimation();
            
            // 模拟资源加载
            gameApp.preloadResources().then(() => {
                gameApp.stopLoadingAnimation();
                updateStatus('资源加载测试完成');
                
                // 显示主菜单
                setTimeout(() => {
                    gameApp.showMainMenu();
                }, 500);
            });
        }
        
        function showMainMenu() {
            if (!gameApp) {
                updateStatus('GameApp未初始化，先创建实例');
                return;
            }
            
            gameApp.stopLoadingAnimation();
            gameApp.showMainMenu();
            updateStatus('显示主菜单');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，等待用户操作...');
        });
        
        // 手动初始化GameApp的函数
        function initGameApp() {
            if (gameApp) {
                updateStatus('GameApp已存在');
                return;
            }
            
            try {
                updateStatus('正在初始化GameApp...');
                // 这里不直接创建GameApp，因为它会自动开始加载
                // 我们需要先加载game.js
                updateStatus('请先加载game.js文件');
            } catch (error) {
                updateStatus(`初始化失败: ${error.message}`);
                console.error('初始化错误:', error);
            }
        }
    </script>
    
    <!-- 加载游戏主文件 -->
    <script src="game.js"></script>
    
    <script>
        // 等待GameApp初始化完成
        setTimeout(() => {
            if (window.gameApp) {
                gameApp = window.gameApp;
                updateStatus('GameApp初始化完成，可以开始测试');
            } else {
                updateStatus('GameApp初始化失败或仍在进行中');
            }
        }, 1000);
    </script>
</body>
</html>
