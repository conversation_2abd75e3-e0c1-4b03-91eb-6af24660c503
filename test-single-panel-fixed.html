<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的单面板布局测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 20px;
        }
        
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .fix-info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .fix-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
        
        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 修复后的单面板布局测试</h1>
        
        <div class="fix-info">
            <h3>修复内容</h3>
            <ul>
                <li>修复了renderPatternLines、renderWall、renderFloorLine方法不存在的问题</li>
                <li>实现了renderPatternLinesInArea、renderWallInArea、renderFloorLineInArea方法</li>
                <li>使用现有的renderTile和renderWallSlot方法进行渲染</li>
                <li>保持了单面板布局的所有功能</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testSinglePanel()">测试单面板布局</button>
            <button onclick="testAvatarSwitch()">测试头像切换</button>
            <button onclick="testMultiplayer()">测试多人游戏</button>
            <button onclick="showDebugInfo()">显示调试信息</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`, 'success');
                }
            };
        }
        
        let gameApp;
        let azulGame;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log('测试状态:', message);
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功', 'success');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`, 'error');
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function createTestGame(playerCount = 3) {
            const app = initializeGameApp();
            if (app) {
                try {
                    const config = {
                        playerCount: playerCount,
                        aiCount: playerCount - 1,
                        aiDifficulty: 'medium'
                    };
                    
                    azulGame = new AzulGame(config, app.sharedCanvas);
                    app.gameScreen = azulGame;
                    app.currentScreen = 'game';
                    
                    // 模拟一些游戏状态
                    const gameState = azulGame.gameState;
                    gameState.players.forEach((player, index) => {
                        player.score = Math.floor(Math.random() * 50) + index * 10;
                        // 添加一些磁砖到准备区
                        if (index === 0) {
                            player.patternLines[0] = ['blue'];
                            player.patternLines[1] = ['red', 'red'];
                            player.floorLine = ['yellow', 'first'];
                        }
                        if (index === 1) {
                            player.patternLines[2] = ['yellow', 'yellow', 'yellow'];
                            player.wall[0][0].filled = true; // 填充一个墙壁格子
                        }
                    });
                    
                    azulGame.render();
                    updateStatus(`${playerCount}人游戏创建成功，当前查看玩家${azulGame.currentViewingPlayer + 1}`, 'success');
                    
                } catch (error) {
                    updateStatus(`创建${playerCount}人游戏失败: ${error.message}`, 'error');
                    console.error('游戏创建错误:', error);
                }
            }
        }
        
        function testSinglePanel() {
            createTestGame(3);
        }
        
        function testAvatarSwitch() {
            if (azulGame && azulGame.playerAvatarAreas && azulGame.playerAvatarAreas.length > 1) {
                try {
                    const currentViewing = azulGame.currentViewingPlayer;
                    const nextPlayer = (currentViewing + 1) % azulGame.playerAvatarAreas.length;
                    const targetAvatar = azulGame.playerAvatarAreas[nextPlayer];
                    
                    const centerX = targetAvatar.x + targetAvatar.width / 2;
                    const centerY = targetAvatar.y + targetAvatar.height / 2;
                    
                    updateStatus(`点击玩家${nextPlayer + 1}的头像进行切换`, 'info');
                    azulGame.handleClick(centerX, centerY);
                    
                    updateStatus(`切换成功！当前查看玩家${azulGame.currentViewingPlayer + 1}`, 'success');
                    
                } catch (error) {
                    updateStatus(`头像切换测试失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('请先创建游戏', 'error');
            }
        }
        
        function testMultiplayer() {
            createTestGame(4);
        }
        
        function showDebugInfo() {
            if (azulGame) {
                try {
                    const gameState = azulGame.gameState;
                    const playerCount = gameState.players.length;
                    const currentViewing = azulGame.currentViewingPlayer + 1;
                    const currentPlayer = gameState.currentPlayer + 1;
                    const avatarCount = azulGame.playerAvatarAreas ? azulGame.playerAvatarAreas.length : 0;
                    
                    let info = `调试信息:\n`;
                    info += `- 玩家总数: ${playerCount}\n`;
                    info += `- 当前查看: 玩家${currentViewing}\n`;
                    info += `- 当前回合: 玩家${currentPlayer}\n`;
                    info += `- 头像数量: ${avatarCount}\n`;
                    info += `- 游戏阶段: ${gameState.phase}\n`;
                    
                    // 显示每个玩家的状态
                    gameState.players.forEach((player, index) => {
                        const patternTiles = player.patternLines.reduce((total, line) => total + line.length, 0);
                        const floorTiles = player.floorLine.length;
                        const wallTiles = player.wall.reduce((total, row) => total + row.filter(cell => cell.filled).length, 0);
                        info += `- 玩家${index + 1}: 分数${player.score}, 准备区${patternTiles}个, 墙壁${wallTiles}个, 地板线${floorTiles}个\n`;
                    });
                    
                    updateStatus(info, 'info');
                    
                } catch (error) {
                    updateStatus(`获取调试信息失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('请先创建游戏', 'error');
            }
        }
        
        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateStatus('页面加载完成，渲染方法已修复，准备测试单面板布局...');
                // 自动创建一个3人游戏进行测试
                setTimeout(() => {
                    testSinglePanel();
                }, 1000);
            }, 500);
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>
