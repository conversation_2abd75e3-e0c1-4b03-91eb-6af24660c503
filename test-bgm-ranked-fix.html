<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排位赛BGM开关修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 20px;
        }
        
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .fix-info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .fix-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 排位赛BGM开关修复测试</h1>
        
        <div class="fix-info">
            <h3>修复内容</h3>
            <ul>
                <li>修复了refreshCurrentScreen()中调用this.rankedUI.render()的错误</li>
                <li>改为调用this.rankedUI.showMainScreen()方法</li>
                <li>现在BGM开关应该能正常工作，不会报错</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testRankedBgmToggle()">测试排位赛BGM开关</button>
            <button onclick="simulateMultipleBgmClicks()">模拟多次BGM切换</button>
            <button onclick="showCurrentStatus()">显示当前状态</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境
        if (typeof wx === 'undefined') {
            window.wx = {
                createCanvas: () => document.getElementById('gameCanvas'),
                createImage: () => new Image(),
                createInnerAudioContext: () => ({
                    src: '',
                    volume: 0.7,
                    loop: false,
                    play: () => console.log('播放音频'),
                    stop: () => console.log('停止音频'),
                    onCanplay: null,
                    onError: null
                }),
                showToast: (options) => {
                    updateStatus(`Toast: ${options.title}`, 'success');
                },
                onTouchStart: (handler) => {
                    console.log('设置触摸事件监听器');
                    window.wxTouchHandler = handler;
                },
                offTouchStart: (handler) => {
                    console.log('移除触摸事件监听器');
                    if (window.wxTouchHandler === handler) {
                        window.wxTouchHandler = null;
                    }
                }
            };
        }
        
        let gameApp;
        let rankedUI;
        let testResults = [];
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `状态：${message}`;
            statusDiv.className = `status ${type}`;
            console.log('测试状态:', message);
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功', 'success');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`, 'error');
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function testRankedBgmToggle() {
            const app = initializeGameApp();
            if (app) {
                try {
                    // 模拟排位赛UI初始化
                    if (!rankedUI) {
                        rankedUI = new RankedUI(app.sharedCanvas, null);
                        rankedUI.userRanking = {
                            rating: 1587,
                            tier: { name: '白银 3', icon: '🥈', color: '#C0C0C0', progress: 65 },
                            rank: 4,
                            totalGames: 115,
                            wins: 19,
                            losses: 96,
                            winRate: 0.165,
                            currentStreak: 0,
                            highestRating: 1587
                        };
                        app.rankedUI = rankedUI;
                        app.currentScreen = 'ranked';
                    }
                    
                    rankedUI.showMainScreen();
                    
                    // 测试BGM开关
                    const initialBgmState = app.bgmEnabled;
                    updateStatus(`排位赛界面显示成功，初始BGM状态: ${initialBgmState ? '开启' : '关闭'}`, 'success');
                    
                    // 模拟点击BGM开关
                    setTimeout(() => {
                        try {
                            app.toggleBgm();
                            const newBgmState = app.bgmEnabled;
                            updateStatus(`BGM开关测试成功！状态从 ${initialBgmState ? '开启' : '关闭'} 切换到 ${newBgmState ? '开启' : '关闭'}`, 'success');
                            testResults.push('BGM开关切换成功');
                        } catch (error) {
                            updateStatus(`BGM开关测试失败: ${error.message}`, 'error');
                            testResults.push(`BGM开关切换失败: ${error.message}`);
                        }
                    }, 1000);
                    
                } catch (error) {
                    updateStatus(`排位赛BGM测试失败: ${error.message}`, 'error');
                    console.error('测试错误:', error);
                }
            }
        }
        
        function simulateMultipleBgmClicks() {
            if (!gameApp || !rankedUI) {
                updateStatus('请先运行排位赛BGM开关测试', 'error');
                return;
            }
            
            let clickCount = 0;
            const maxClicks = 5;
            
            const clickInterval = setInterval(() => {
                try {
                    const beforeState = gameApp.bgmEnabled;
                    gameApp.toggleBgm();
                    const afterState = gameApp.bgmEnabled;
                    
                    clickCount++;
                    updateStatus(`第${clickCount}次BGM切换: ${beforeState ? '开启' : '关闭'} → ${afterState ? '开启' : '关闭'}`, 'success');
                    
                    if (clickCount >= maxClicks) {
                        clearInterval(clickInterval);
                        updateStatus(`完成${maxClicks}次BGM切换测试，全部成功！`, 'success');
                    }
                } catch (error) {
                    clearInterval(clickInterval);
                    updateStatus(`第${clickCount + 1}次BGM切换失败: ${error.message}`, 'error');
                }
            }, 1000);
        }
        
        function showCurrentStatus() {
            if (gameApp && rankedUI) {
                const bgmStatus = gameApp.bgmEnabled ? '开启' : '关闭';
                const currentBgm = gameApp.currentBgm || '无';
                const currentScreen = gameApp.currentScreen;
                const buttons = rankedUI.buttons ? Object.keys(rankedUI.buttons) : [];
                
                const statusInfo = `
当前状态:
- BGM状态: ${bgmStatus}
- 当前BGM: ${currentBgm}
- 当前界面: ${currentScreen}
- 可用按钮: [${buttons.join(', ')}]
- 测试结果: ${testResults.length > 0 ? testResults.join('; ') : '无'}
                `.trim();
                
                updateStatus(statusInfo, 'info');
            } else {
                updateStatus('请先运行排位赛BGM开关测试', 'error');
            }
        }
        
        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateStatus('页面加载完成，准备测试排位赛BGM开关修复...');
            }, 500);
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>
