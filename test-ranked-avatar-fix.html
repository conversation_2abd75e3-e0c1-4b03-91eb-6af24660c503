<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排位赛头像显示修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .fix-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .fix-info h3 {
            margin-top: 0;
            color: #FFD700;
        }
        .fix-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .fix-info li {
            margin: 5px 0;
        }
        canvas {
            border: 2px solid #fff;
            border-radius: 10px;
            background: #f0f0f0;
            display: block;
            margin: 20px auto;
        }
        .buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #FF9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 排位赛头像显示修复测试</h1>
        
        <div class="fix-info">
            <h3>修复内容</h3>
            <ul>
                <li>✅ 修复了四人游戏中只能显示两个头像的问题</li>
                <li>✅ 根据玩家数量动态计算面板高度</li>
                <li>✅ 确保所有头像都能正常显示和点击</li>
                <li>✅ 保持头像点击切换功能正常工作</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testRankedGame()">创建排位赛游戏</button>
            <button onclick="testAvatarClick()">测试头像点击</button>
            <button onclick="testAllAvatars()">测试所有头像</button>
            <button onclick="showDebugInfo()">显示调试信息</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script src="game.js"></script>
    <script>
        let azulGame;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<span class="${type}">${message}</span>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function testRankedGame() {
            try {
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 创建排位赛游戏配置
                const rankedGameData = {
                    players: [
                        { id: 'player1', nickName: '玩家1', rating: 1200, tier: { name: '青铜 5', icon: '🥉', color: '#CD7F32' } },
                        { id: 'player2', nickName: '玩家2', rating: 1250, tier: { name: '青铜 4', icon: '🥉', color: '#CD7F32' } },
                        { id: 'player3', nickName: '玩家3', rating: 1300, tier: { name: '青铜 3', icon: '🥉', color: '#CD7F32' } },
                        { id: 'player4', nickName: '玩家4', rating: 1350, tier: { name: '青铜 2', icon: '🥉', color: '#CD7F32' } }
                    ],
                    matchId: 'test-match-001',
                    gameState: {}
                };
                
                azulGame = new AzulGame(canvas, ctx);
                const gameConfig = azulGame.createRankedGameConfig(rankedGameData);
                azulGame.initGame(gameConfig);
                
                updateStatus('排位赛游戏创建成功！四个玩家头像应该都能显示', 'success');
                
                // 检查头像数量
                setTimeout(() => {
                    if (azulGame.playerAvatarAreas) {
                        updateStatus(`检测到 ${azulGame.playerAvatarAreas.length} 个头像区域`, 'info');
                        if (azulGame.playerAvatarAreas.length === 4) {
                            updateStatus('✅ 所有四个头像都正常显示！', 'success');
                        } else {
                            updateStatus(`❌ 头像显示异常，应该是4个，实际是${azulGame.playerAvatarAreas.length}个`, 'error');
                        }
                    }
                }, 100);
                
            } catch (error) {
                updateStatus(`创建游戏失败: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        function testAvatarClick() {
            if (!azulGame || !azulGame.playerAvatarAreas || azulGame.playerAvatarAreas.length < 4) {
                updateStatus('请先创建排位赛游戏', 'error');
                return;
            }
            
            try {
                const currentViewing = azulGame.currentViewingPlayer;
                const nextPlayer = (currentViewing + 1) % azulGame.playerAvatarAreas.length;
                const targetAvatar = azulGame.playerAvatarAreas[nextPlayer];
                
                const centerX = targetAvatar.x + targetAvatar.width / 2;
                const centerY = targetAvatar.y + targetAvatar.height / 2;
                
                updateStatus(`点击玩家${nextPlayer + 1}的头像 (${centerX}, ${centerY})`, 'info');
                azulGame.handleClick(centerX, centerY);
                
                if (azulGame.currentViewingPlayer === nextPlayer) {
                    updateStatus(`✅ 头像切换成功！当前查看玩家${azulGame.currentViewingPlayer + 1}`, 'success');
                } else {
                    updateStatus(`❌ 头像切换失败，期望${nextPlayer + 1}，实际${azulGame.currentViewingPlayer + 1}`, 'error');
                }
                
            } catch (error) {
                updateStatus(`头像点击测试失败: ${error.message}`, 'error');
            }
        }
        
        function testAllAvatars() {
            if (!azulGame || !azulGame.playerAvatarAreas || azulGame.playerAvatarAreas.length < 4) {
                updateStatus('请先创建排位赛游戏', 'error');
                return;
            }
            
            updateStatus('开始测试所有头像点击...', 'info');
            
            let testIndex = 0;
            const testInterval = setInterval(() => {
                if (testIndex >= azulGame.playerAvatarAreas.length) {
                    clearInterval(testInterval);
                    updateStatus('✅ 所有头像点击测试完成！', 'success');
                    return;
                }
                
                const targetAvatar = azulGame.playerAvatarAreas[testIndex];
                const centerX = targetAvatar.x + targetAvatar.width / 2;
                const centerY = targetAvatar.y + targetAvatar.height / 2;
                
                updateStatus(`测试玩家${testIndex + 1}头像 (${centerX}, ${centerY})`, 'info');
                azulGame.handleClick(centerX, centerY);
                
                if (azulGame.currentViewingPlayer === testIndex) {
                    updateStatus(`✅ 玩家${testIndex + 1}头像点击成功`, 'success');
                } else {
                    updateStatus(`❌ 玩家${testIndex + 1}头像点击失败`, 'error');
                }
                
                testIndex++;
            }, 1000);
        }
        
        function showDebugInfo() {
            if (!azulGame) {
                updateStatus('请先创建游戏', 'error');
                return;
            }
            
            const info = {
                playerCount: azulGame.gameState?.players?.length || 0,
                avatarAreas: azulGame.playerAvatarAreas?.length || 0,
                currentViewing: azulGame.currentViewingPlayer,
                gameMode: azulGame.gameState?.gameMode || 'unknown'
            };
            
            updateStatus(`调试信息: ${JSON.stringify(info, null, 2)}`, 'info');
            
            if (azulGame.playerAvatarAreas) {
                azulGame.playerAvatarAreas.forEach((area, index) => {
                    console.log(`头像${index + 1}:`, area);
                });
            }
        }
        
        // 页面加载完成后自动创建测试游戏
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，点击"创建排位赛游戏"开始测试', 'info');
        });
    </script>
</body>
</html>
