<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BGM开关功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 30px;
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .test-info h3 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .test-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin: 5px 0;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: block;
            margin: 20px auto;
        }
        
        .buttons {
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #757575;
            cursor: not-allowed;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 花砖物语 BGM开关功能测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <ul>
                <li>本页面用于测试BGM开关功能是否正常工作</li>
                <li>点击左上角的BGM开关按钮（🎵/🔇）来切换BGM状态</li>
                <li>BGM开启时显示🎵图标，背景为绿色</li>
                <li>BGM关闭时显示🔇图标，背景为灰色</li>
                <li>状态变化会在下方显示</li>
            </ul>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="buttons">
            <button onclick="testMainMenu()">测试主菜单</button>
            <button onclick="testConfigScreen()">测试配置页面</button>
            <button onclick="testGameScreen()">测试游戏页面</button>
            <button onclick="testMultiplayerUI()">测试联机页面</button>
            <button onclick="testRankedUI()">测试排位赛页面</button>
        </div>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
    </div>

    <script src="game.js"></script>
    <script>
        let gameApp;
        let currentTest = null;
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `状态：${message}`;
            console.log('测试状态:', message);
        }
        
        function initializeGameApp() {
            if (!gameApp) {
                try {
                    // 模拟微信小游戏环境
                    if (typeof wx === 'undefined') {
                        window.wx = {
                            createCanvas: () => document.getElementById('gameCanvas'),
                            createImage: () => new Image(),
                            createInnerAudioContext: () => ({
                                src: '',
                                volume: 0.7,
                                loop: false,
                                play: () => console.log('播放音频'),
                                stop: () => console.log('停止音频'),
                                onCanplay: null,
                                onError: null
                            }),
                            showToast: (options) => {
                                alert(options.title);
                            }
                        };
                    }
                    
                    gameApp = new GameApp();
                    updateStatus('游戏应用初始化成功');
                } catch (error) {
                    updateStatus(`初始化失败: ${error.message}`);
                    console.error('初始化错误:', error);
                }
            }
            return gameApp;
        }
        
        function testMainMenu() {
            const app = initializeGameApp();
            if (app) {
                try {
                    app.showMainMenu();
                    currentTest = 'mainMenu';
                    updateStatus('主菜单测试 - 请点击左上角BGM开关');
                } catch (error) {
                    updateStatus(`主菜单测试失败: ${error.message}`);
                }
            }
        }
        
        function testConfigScreen() {
            const app = initializeGameApp();
            if (app) {
                try {
                    app.showConfigScreen();
                    currentTest = 'config';
                    updateStatus('配置页面测试 - 请点击左上角BGM开关');
                } catch (error) {
                    updateStatus(`配置页面测试失败: ${error.message}`);
                }
            }
        }
        
        function testGameScreen() {
            updateStatus('游戏页面测试需要完整的游戏初始化，暂时跳过');
        }
        
        function testMultiplayerUI() {
            updateStatus('联机页面测试需要服务器连接，暂时跳过');
        }
        
        function testRankedUI() {
            updateStatus('排位赛页面测试需要服务器连接，暂时跳过');
        }
        
        // 监听BGM状态变化
        setInterval(() => {
            if (gameApp && gameApp.bgmEnabled !== undefined) {
                const bgmStatus = gameApp.bgmEnabled ? '开启' : '关闭';
                const currentBgm = gameApp.currentBgm || '无';
                document.getElementById('status').innerHTML += `<br>BGM状态: ${bgmStatus}, 当前BGM: ${currentBgm}`;
            }
        }, 2000);
        
        // 页面加载完成后自动测试主菜单
        window.addEventListener('load', () => {
            setTimeout(() => {
                testMainMenu();
            }, 1000);
        });
    </script>
</body>
</html>
